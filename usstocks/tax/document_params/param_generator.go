package document_params

//go:generate mockgen -source=param_generator.go -destination=./mocks/mock_param_generator.go package=mocks

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/wire"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/monitoring"

	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/api/usstocks/tax/model"
	"github.com/epifi/gamma/usstocks/tax/dao"
	"github.com/epifi/gamma/usstocks/tax/document_params/param_generator"
)

var ParamGeneratorWireSet = wire.NewSet(NewParamsGenerator, wire.Bind(new(IParamsGenerator), new(*ParamsGenerator)))

type IParamsGenerator interface {
	// GenerateParams generates document params for all tax documents, persists in tax_document_params entity and
	// returns a map of document type to id of params entry created in tax_document_params
	GenerateParams(ctx context.Context, req *GenerateParamsRequest) (map[tax.UsStockDocumentType]string, error)
}

type ParamsGenerator struct {
	paramsGenFactory param_generator.ParamGeneratorFactory
	taxDocParamsDao  dao.TaxDocumentParamsDao
}

func NewParamsGenerator(paramsGenFactory param_generator.ParamGeneratorFactory,
	taxDocParamsDao dao.TaxDocumentParamsDao) *ParamsGenerator {
	return &ParamsGenerator{
		paramsGenFactory: paramsGenFactory,
		taxDocParamsDao:  taxDocParamsDao,
	}
}

func (p *ParamsGenerator) GenerateParams(ctx context.Context, req *GenerateParamsRequest) (map[tax.UsStockDocumentType]string, error) {
	if req == nil {
		return nil, fmt.Errorf("GenerateDocumentParams cannot be nil")
	}
	if len(req.RequestedTaxDocuments) == 0 {
		return nil, fmt.Errorf("no requested tax documents in request")
	}
	req.RequestedTaxDocuments = lo.Uniq(req.RequestedTaxDocuments)

	taxDocumentParams := make([]*documentparams.DocumentParams, len(req.RequestedTaxDocuments))
	taxDocumentIdMap := make(map[tax.UsStockDocumentType]string)

	errGrp, grpCtx := errgroup.WithContext(ctx)
	for idx, docType := range req.RequestedTaxDocuments {
		idx := idx
		docType := docType
		errGrp.Go(func() error {
			params, paramsErr := p.getParams(grpCtx, &getParamsRequest{
				docType:   docType,
				accountId: req.AccountId,
				fromTime:  req.FromTime,
				toTime:    req.ToTime,
				txns:      req.Txns,
			})
			if paramsErr != nil {
				return fmt.Errorf("failed to get params: %w", paramsErr)
			}
			taxDocumentParams[idx] = params
			return nil
		})
	}
	if gErr := errGrp.Wait(); gErr != nil {
		return nil, fmt.Errorf("failed to generate document params: %w", gErr)
	}

	var paramsProtoArr []*model.TaxDocumentParams
	for idx, docType := range req.RequestedTaxDocuments {
		if taxDocumentParams[idx] == nil {
			logger.Info(ctx, fmt.Sprintf("tax document params is nil, skipping : %s", docType.String()))
			continue
		}
		paramsProtoArr = append(paramsProtoArr, &model.TaxDocumentParams{
			DocumentType: docType,
			AccountId:    req.AccountId,
			Params:       taxDocumentParams[idx],
		})
	}

	bulkCreateResp, bulkCreateErr := p.taxDocParamsDao.BulkCreate(ctx, paramsProtoArr)
	if bulkCreateErr != nil {
		return nil, fmt.Errorf("failed to bulk_create tax_document_params : %w", bulkCreateErr)
	}
	for _, params := range bulkCreateResp {
		taxDocumentIdMap[params.GetDocumentType()] = params.GetId()
	}
	return taxDocumentIdMap, nil
}

func (p *ParamsGenerator) getParams(ctx context.Context, req *getParamsRequest) (*documentparams.DocumentParams, error) {
	paramGenerator, err := p.paramsGenFactory.GetParamGenerator(req.docType)
	if err != nil {
		return nil, fmt.Errorf("failed to get param generator: %w", err)
	}
	docParamsInfo, docParamsInfoErr := paramGenerator.PrepareDataForDocumentParams(ctx, req.txns, req.fromTime, req.toTime)
	if docParamsInfoErr != nil {
		return nil, fmt.Errorf("failed to fetch data for document params: %w", docParamsInfoErr)
	}
	params, paramsErr := paramGenerator.GetDocumentParams(ctx, &param_generator.ParamsGenerateReq{
		AccountId:    req.accountId,
		FromTime:     req.fromTime,
		ToTime:       req.toTime,
		DividendInfo: docParamsInfo.DividendInfo,
		BuySellPairs: docParamsInfo.BuySellPairs,
		Transactions: req.txns,
		InterestInfo: docParamsInfo.InterestInfo,
	})
	if paramsErr != nil {
		if errors.Is(paramsErr, param_generator.ErrHistoricalStockPriceNotFound) {
			// ignoring this error, as this should not impact other form generation
			// if we get this error need to check manually and fix and re-trigger form generation
			logger.Error(ctx, "failed to get doc params for scheduled fa form due to historical stock price not found", zap.Error(param_generator.ErrHistoricalStockPriceNotFound))
			monitoring.KibanaInfoServiceMonitor(ctx, cfg.US_STOCKS_SERVICE, "historical stock prices not found for schedule fa")
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get params: %w", paramsErr)
	}
	return params, nil
}

type getParamsRequest struct {
	docType   tax.UsStockDocumentType
	accountId string
	fromTime  time.Time
	toTime    time.Time
	txns      []*tax.TransactionWrapper
}

type GenerateParamsRequest struct {
	AccountId             string
	FromTime              time.Time
	ToTime                time.Time
	Txns                  []*tax.TransactionWrapper
	RequestedTaxDocuments []tax.UsStockDocumentType
	DocParamsInfo         []DocParamsInfo
}

type DocParamsInfo struct {
	FromTime             time.Time
	ToTime               time.Time
	RequestedTaxDocument tax.UsStockDocumentType
}
