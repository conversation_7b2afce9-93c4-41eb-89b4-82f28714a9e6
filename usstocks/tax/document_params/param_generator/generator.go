//go:generate mockgen -source=generator.go -destination=./mocks/mock_generator.go package=mocks
package param_generator

import (
	"context"
	"time"

	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
)

type ParamsGenerator interface {
	PrepareDataForDocumentParams(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (*transactionprocessor.DocumentParamsInfo, error)
	GetDocumentParams(ctx context.Context, req *ParamsGenerateReq) (*documentparams.DocumentParams, error)
}

type ParamsGenerateReq struct {
	AccountId    string
	FromTime     time.Time
	ToTime       time.Time
	DividendInfo *transactionprocessor.DividendInfo
	BuySellPairs *transactionprocessor.BuySellPairs
	Transactions []*tax.TransactionWrapper
}
