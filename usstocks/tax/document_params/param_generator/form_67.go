package param_generator

import (
	"context"
	"fmt"

	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/api/usstocks/tax/moneywrapper"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
)

const (
	dividendTaxRatePercent = "25%"
)

type Form67DocParamsGenerator struct {
	*BaseDataProvider
}

func NewForm67DocParamsGenerator(transactionProcessor transactionprocessor.ITransactionProcessor) *Form67DocParamsGenerator {
	return &Form67DocParamsGenerator{
		BaseDataProvider: NewBaseDataProvider(transactionProcessor),
	}
}

func (g *Form67DocParamsGenerator) GetDocumentParams(ctx context.Context, req *ParamsGenerateReq) (*documentparams.DocumentParams, error) {
	if err := g.validateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failure : %w", err)
	}

	usaIncomeDividendDetails := g.getUsaDividendIncomeDetails(req.DividendInfo)

	usaIncomeInterestDetails, err := g.getUsaInterestIncomeDetails(req.Transactions)
	if err != nil {
		return nil, fmt.Errorf("failed to get usa_income_summary : %w", err)
	}

	return &documentparams.DocumentParams{
		Params: &documentparams.DocumentParams_Form_67Params{
			Form_67Params: &documentparams.Form67Params{
				DetailsOfAllIncomes: []*documentparams.IncomeDetails{
					usaIncomeDividendDetails,
					usaIncomeInterestDetails,
				},
			},
		},
	}, nil
}

func (g *Form67DocParamsGenerator) validateRequest(req *ParamsGenerateReq) error {
	switch {
	case req == nil:
		return fmt.Errorf("param_gen_req cannot be nil")
	case req.DividendInfo == nil:
		return fmt.Errorf("dividend_info cannot be nil")
	case req.DividendInfo.Summary == nil:
		return fmt.Errorf("dividend_info summary cannot be nil")
	}
	return nil
}

func (g *Form67DocParamsGenerator) getUsaDividendIncomeDetails(dividendInfo *transactionprocessor.DividendInfo) *documentparams.IncomeDetails {
	return &documentparams.IncomeDetails{
		CountryName:  CountryUSA,
		IncomeSource: tax.IncomeSource_INCOME_SOURCE_DIVIDEND,
		IncomeFromOutsideIndia: &moneywrapper.UsdInrWrapper{
			Usd: dividendInfo.Summary.TotalDividendIncomeInUsd,
			Inr: dividendInfo.Summary.TotalDividendIncomeInInr,
		},
		TaxPaidOutsideIndia: &moneywrapper.UsdInrWrapper{
			Usd: dividendInfo.Summary.TotalTaxPaidInUsd,
			Inr: dividendInfo.Summary.TotalTaxPaidInInr,
		},
		TaxPaidOutsideIndiaRateInPercent: dividendTaxRatePercent,
	}
}

func (g *Form67DocParamsGenerator) getUsaInterestIncomeDetails(txns []*tax.TransactionWrapper) (*documentparams.IncomeDetails, error) {
	taxPaidInUsd := moneyPb.ZeroUSD()
	taxPaidInInr := moneyPb.ZeroINR().GetPb()

	totalInterestIncomeInr := moneyPb.ZeroUSD()
	totalInterestIncomeUsd := moneyPb.ZeroINR().GetPb()

	for _, txn := range txns {
		if txn.GetTransactionType() == tax.TransactionType_TRANSACTION_TYPE_INTEREST || txn.GetTransactionType() == tax.TransactionType_TRANSACTION_TYPE_INTEREST_WITHHELD {
			return nil, fmt.Errorf("unhandled txn type %s for income details", txn.GetTransactionType())
		}
	}

	return &documentparams.IncomeDetails{
		CountryName:  CountryUSA,
		IncomeSource: tax.IncomeSource_INCOME_SOURCE_INTEREST,
		IncomeFromOutsideIndia: &moneywrapper.UsdInrWrapper{
			Usd: totalInterestIncomeUsd,
			Inr: totalInterestIncomeInr,
		},
		TaxPaidOutsideIndia: &moneywrapper.UsdInrWrapper{
			Usd: taxPaidInUsd,
			Inr: taxPaidInInr,
		},
		TaxPaidOutsideIndiaRateInPercent: "-",
	}, nil
}
