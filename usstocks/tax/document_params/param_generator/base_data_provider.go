package param_generator

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
)

type BaseDataProvider struct {
	transactionProcessor transactionprocessor.ITransactionProcessor
}

func NewBaseDataProvider(transactionProcessor transactionprocessor.ITransactionProcessor) *BaseDataProvider {
	return &BaseDataProvider{
		transactionProcessor: transactionProcessor,
	}
}

func (p *BaseDataProvider) PrepareDataForDocumentParams(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (*transactionprocessor.DocumentParamsInfo, error) {
	var (
		buySellPairs    []*transactionprocessor.BuySellPair
		netLtcgStcgInfo *transactionprocessor.LtcgStcgInfo
		dividendInfo    *transactionprocessor.DividendInfo
		interestInfo    *transactionprocessor.InterestInfo
	)
	errGrp, grpCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		var ltcgStcgInfoFetchErr error
		buySellPairsMap, buySellPairFetchErr := p.transactionProcessor.GetBuySellPairsMap(grpCtx, txns, fromTime, toTime)
		if buySellPairFetchErr != nil {
			return fmt.Errorf("failed to get buy sell pairs : %w", buySellPairFetchErr)
		}
		buySellPairs = getAllBuySellPairsFromMap(buySellPairsMap)
		netLtcgStcgInfo, ltcgStcgInfoFetchErr = p.transactionProcessor.GetNetLtcgAndStcg(grpCtx, buySellPairs)
		if ltcgStcgInfoFetchErr != nil {
			return fmt.Errorf("failed to get net ltcg and stcg info: %w", ltcgStcgInfoFetchErr)
		}
		return nil
	})
	errGrp.Go(func() error {
		var dividendInfoFetchErr error
		dividendInfo, dividendInfoFetchErr = p.transactionProcessor.GetDividendInfo(grpCtx, txns, fromTime, toTime)
		if dividendInfoFetchErr != nil {
			return fmt.Errorf("failed to get dividend info: %w", dividendInfoFetchErr)
		}
		return nil
	})
	errGrp.Go(func() error {
		var interestInfoFetchErr error
		interestInfo, interestInfoFetchErr = p.transactionProcessor.GetInterestInfo(grpCtx, txns, fromTime, toTime)
		if interestInfoFetchErr != nil {
			logger.Error(ctx, "failed to get interest info", zap.Error(interestInfoFetchErr))
			return fmt.Errorf("failed to get interest info: %w", interestInfoFetchErr)
		}
		return nil
	})
	if gErr := errGrp.Wait(); gErr != nil {
		return nil, fmt.Errorf("failed to get data : %w", gErr)
	}
	return &transactionprocessor.DocumentParamsInfo{
		BuySellPairs: &transactionprocessor.BuySellPairs{
			Pairs:           buySellPairs,
			NetLtcgStcgInfo: netLtcgStcgInfo,
		},
		DividendInfo: dividendInfo,
		InterestInfo: interestInfo,
	}, nil
}

func getAllBuySellPairsFromMap(pairsMap map[string][]*transactionprocessor.BuySellPair) []*transactionprocessor.BuySellPair {
	var allPairs []*transactionprocessor.BuySellPair
	for _, pairs := range pairsMap {
		allPairs = append(allPairs, pairs...)
	}
	return allPairs
}
