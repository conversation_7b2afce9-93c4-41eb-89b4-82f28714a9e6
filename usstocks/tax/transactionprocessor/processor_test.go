package transactionprocessor

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/shopspring/decimal"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks/catalog"
	MockcatalogClient "github.com/epifi/gamma/api/usstocks/catalog/mocks"
	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/usstocks/config"
	"github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/utils/mocks"
)

type fields struct {
	ciiProvider             *mocks.MockCostInflationIndexProvider
	sbiExchangeRateProvider *mocks.MockSBIExchangeRateProvider
	ussCatalogMgrClient     *MockcatalogClient.MockCatalogManagerClient
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		ciiProvider:             mocks.NewMockCostInflationIndexProvider(ctrl),
		sbiExchangeRateProvider: mocks.NewMockSBIExchangeRateProvider(ctrl),
		ussCatalogMgrClient:     MockcatalogClient.NewMockCatalogManagerClient(ctrl),
	}
}

func TestTransactionProcessor_GetBuySellPairsMap(t *testing.T) {
	type args struct {
		ctx                                context.Context
		txns                               []*tax.TransactionWrapper
		fromTime                           time.Time
		toTime                             time.Time
		indexationBenefitNotApplicableFrom time.Time
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       map[string][]*BuySellPair
		wantErr    bool
	}{
		{
			name: "success get buy sell pairs map - 1",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					SellA5Jan2022(),
					SellA5Jan2023(),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ciiProvider.EXPECT().GetCII(2020).Return(100, nil)
				f.ciiProvider.EXPECT().GetCII(2022).Return(150, nil)

				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2021, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want: map[string][]*BuySellPair{
				"AA": {
					{
						Symbol:      "AA",
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						DisplayName: "Apple Inc",
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        95,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        90,
						},
						Comments: "",
					},
					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_LONG_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        180,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        82,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        97,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success get buy sell pairs map without indexation benefit got ltcg",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					SellA5Jan2022(),
					SellA5Jan2023(),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2021, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want: map[string][]*BuySellPair{
				"AA": {
					{
						Symbol:      "AA",
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						DisplayName: "Apple Inc",
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        95,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        90,
						},
						Comments: "",
					},
					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_LONG_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        180,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        125,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success get buy sell pairs map (with pairs outside from-to range)",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					SellA5Jan2022(),
					SellA5Jan2023(),
				},
				fromTime:                           time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ciiProvider.EXPECT().GetCII(2020).Return(100, nil)
				f.ciiProvider.EXPECT().GetCII(2022).Return(150, nil)

				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2021, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want: map[string][]*BuySellPair{
				"AA": {
					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_LONG_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        180,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        82,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        97,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success get buy sell pairs map (with pairs outside from-to range)",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					SellA5Jan2022(),
					SellA5Jan2023(),
				},
				fromTime:                           time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ciiProvider.EXPECT().GetCII(2020).Return(100, nil)
				f.ciiProvider.EXPECT().GetCII(2022).Return(150, nil)

				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2021, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want: map[string][]*BuySellPair{
				"AA": {
					{
						Symbol:      "AA",
						SellType:    tax.SellType_SELL_TYPE_LONG_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						DisplayName: "Apple Inc",
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        180,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        82,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        97,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success get buy sell pairs map - 3 (involving non-terminating repeating fractions)",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					getTransactionWrapper(tax.TransactionType_TRANSACTION_TYPE_BUY, 30, 1, 2023, 100, 10),
					getTransactionWrapper(tax.TransactionType_TRANSACTION_TYPE_SELL, 10, 2, 2023, 50, 10),
					getTransactionWrapper(tax.TransactionType_TRANSACTION_TYPE_SELL, 10, 3, 2023, 50, 10),
					getTransactionWrapper(tax.TransactionType_TRANSACTION_TYPE_SELL, 10, 4, 2023, 50, 10),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2023, 2, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2023, 3, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
				}, nil)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"A"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"A": {
							Symbol: "A",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want: map[string][]*BuySellPair{
				"A": {
					{
						Symbol:      "A",
						DisplayName: "Apple Inc",
						Quantity:    decimal.NewFromInt32(10),
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						BuyTime:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 2, 1, 0, 0, 0, 0, datetime.IST),
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        36,
							Nanos:        *********,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        36,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        3,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
					{
						Symbol:      "A",
						DisplayName: "Apple Inc",
						Quantity:    decimal.NewFromInt32(10),
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						BuyTime:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 3, 1, 0, 0, 0, 0, datetime.IST),
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        36,
							Nanos:        *********,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        36,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        3,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        90,
						},
						Comments: "",
					},
					{
						Symbol:      "A",
						DisplayName: "Apple Inc",
						Quantity:    decimal.NewFromInt32(10),
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						BuyTime:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST),
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        36,
							Nanos:        *********,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        36,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        3,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Comments: "",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "no buy qty found for sell, failure",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					SellA5Jan2022(),
					SellA6June2023(),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ciiProvider.EXPECT().GetCII(2020).Return(100, nil)
				f.ciiProvider.EXPECT().GetCII(2023).Return(150, nil)

				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2021, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2023, 5, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        95,
					},
				}, nil)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "buy txn has 0 units, failure",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA0Jan2021(),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no buy txn found for given stock sell, failure",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					BuyC10Jan2021(),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA", "CC"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
						"CC": {
							Symbol: "CC",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Google",
									StandardName: "Google Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success get buy sell pairs map - 2",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					SellA5Jan2022(),
					SellA3Jan2023(),
					SellA6June2023(),
					BuyA20April2023(),

					BuyB10May2021(),
					SellB5Jan2023(),
				},
				fromTime:                           time.Date(2020, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:                             time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
				indexationBenefitNotApplicableFrom: time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.ciiProvider.EXPECT().GetCII(2020).Return(100, nil).Times(2)
				f.ciiProvider.EXPECT().GetCII(2022).Return(150, nil)
				f.ciiProvider.EXPECT().GetCII(2023).Return(200, nil)

				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil).Times(2)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2021, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2023, 5, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
				}, nil).Times(2)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA", "BB"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"AA": {
							Symbol: "AA",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
						"BB": {
							Symbol: "BB",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Google",
									StandardName: "Google Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_ETF,
						},
					},
				}, nil)
			},
			want: map[string][]*BuySellPair{
				"AA": {
					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        95,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        55,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        90,
						},
						Comments: "",
					},
					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_LONG_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(3),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        33,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        135,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        49,
							Nanos:        *********,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        85,
							Nanos:        *********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},

					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_LONG_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(2),
						BuyTime:     time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 6, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        22,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        23,
							Nanos:        *********,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        44,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        -20,
							Nanos:        -*********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Comments: "",
					},
					{
						Symbol:      "AA",
						DisplayName: "Apple Inc",
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:    decimal.NewFromInt32(4),
						BuyTime:     time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 6, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        64,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        46,
							Nanos:        *********,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        64,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        -17,
							Nanos:        -*********,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Comments: "",
					},
				},
				"BB": {
					{
						Symbol:      "BB",
						DisplayName: "Google Inc",
						SellType:    tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType:   catalog.StockType_STOCK_TYPE_ETF,
						Quantity:    decimal.NewFromInt32(5),
						BuyTime:     time.Date(2021, 5, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:    time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						BuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        275,
						},
						SellValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        150,
						},
						IndexedBuyValueInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        275,
						},
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        -125,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			conf := &config.Config{}
			gconf, _ := genconf.NewConfig()
			_ = gconf.Set(conf, false, nil)
			err := gconf.SetIndexationBenefitNotApplicableFrom(tt.args.indexationBenefitNotApplicableFrom, true, nil)
			if err != nil {
				t.Fatalf("failed to set indexation benefit time")
			}
			tt.setupMocks(f)

			p := NewTransactionProcessor(gconf, f.ciiProvider, f.sbiExchangeRateProvider, f.ussCatalogMgrClient)
			got, err := p.GetBuySellPairsMap(tt.args.ctx, tt.args.txns, tt.args.fromTime, tt.args.toTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBuySellPairsMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetBuySellPairsMap() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func getQuantity(units int32) decimal.Decimal {
	d := decimal.NewFromInt32(units)
	return d
}

//nolint:unparam
func getTransactionWrapper(txnType tax.TransactionType, qty int32, month time.Month, year int, amountUnits int64, expenseUnits int64) *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol: "A",
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        amountUnits,
			},
			ExecutedAt:      timestamppb.New(time.Date(year, month, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: txnType,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        expenseUnits,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(qty),
	}
}

func BuyA10Jan2021() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        100,
			},
			ExecutedAt:      timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_BUY,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        10,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(10),
	}
}

func SellA5Jan2022() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        100,
			},
			ExecutedAt:      timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_SELL,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        5,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(5),
	}
}

func SellA5Jan2023() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        200,
			},
			ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_SELL,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        20,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(5),
	}
}

func SellA3Jan2023() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        150,
			},
			ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_SELL,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        15,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(3),
	}
}

func SellA6June2023() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        100,
			},
			ExecutedAt:      timestamppb.New(time.Date(2023, 6, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_SELL,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        30,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(6),
	}
}

func BuyA20April2023() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        300,
			},
			ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_BUY,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        20,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(20),
	}
}

func BuyB10May2021() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "BB",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        500,
			},
			ExecutedAt:      timestamppb.New(time.Date(2021, 5, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_BUY,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        50,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(10),
	}
}

func SellB5Jan2023() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "BB",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        200,
			},
			ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_SELL,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        50,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(5),
	}
}

func BuyA0Jan2021() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "AA",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        100,
			},
			ExecutedAt:      timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_BUY,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        10,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(0),
	}
}

func BuyC10Jan2021() *tax.TransactionWrapper {
	return &tax.TransactionWrapper{
		Transaction: &tax.Transaction{
			Symbol:   "CC",
			Quantity: nil,
			Amount: &money.Money{
				CurrencyCode: "USD",
				Units:        100,
			},
			ExecutedAt:      timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST)),
			TransactionType: tax.TransactionType_TRANSACTION_TYPE_SELL,
			Expense: &money.Money{
				CurrencyCode: "USD",
				Units:        10,
			},
			ParentActivities: nil,
		},
		Quantity: getQuantity(10),
	}
}

func TestTransactionProcessor_GetDividendInfo(t *testing.T) {
	type args struct {
		ctx      context.Context
		txns     []*tax.TransactionWrapper
		fromTime time.Time
		toTime   time.Time
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *DividendInfo
		wantErr    bool
	}{
		{
			name: "success get dividend info",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					{
						Transaction: &tax.Transaction{
							Symbol: "A",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        20,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
						},
					},
					{
						Transaction: &tax.Transaction{
							Symbol: "A",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        -5,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
						},
					},
					BuyB10May2021(),
					{
						Transaction: &tax.Transaction{
							Symbol: "B",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        100,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
						},
					},
					{
						Transaction: &tax.Transaction{
							Symbol: "B",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        -25,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
						},
					},
					SellA6June2023(),
				},
				fromTime: time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:   time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil).Times(2)
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2023, 3, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        90,
					},
				}, nil).Times(2)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA", "A", "BB", "B"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"A": {
							Symbol: "A",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
						"B": {
							Symbol: "B",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Google",
									StandardName: "Google Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_ETF,
						},
					},
				}, nil)
			},
			want: &DividendInfo{
				DividendTxns: []*DividendTxn{
					{
						ExecutedAt:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SecuritySymbol: "A",
						SecurityType:   tax.SecurityType_SECURITY_TYPE_EQUITY,
						SecurityName:   "Apple Inc",
						AmountInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        20,
						},
						AmountInInr: &money.Money{
							CurrencyCode: "INR",
							Units:        1600,
						},
						TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
					},
					{
						ExecutedAt:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SecuritySymbol: "A",
						SecurityType:   tax.SecurityType_SECURITY_TYPE_EQUITY,
						SecurityName:   "Apple Inc",
						AmountInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        -5,
						},
						AmountInInr: &money.Money{
							CurrencyCode: "INR",
							Units:        -400,
						},
						TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
					},
					{
						ExecutedAt:     time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST),
						SecuritySymbol: "B",
						SecurityType:   tax.SecurityType_SECURITY_TYPE_ETF,
						SecurityName:   "Google Inc",
						AmountInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        100,
						},
						AmountInInr: &money.Money{
							CurrencyCode: "INR",
							Units:        9000,
						},
						TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
					},
					{
						ExecutedAt:     time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST),
						SecuritySymbol: "B",
						SecurityType:   tax.SecurityType_SECURITY_TYPE_ETF,
						SecurityName:   "Google Inc",
						AmountInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        -25,
						},
						AmountInInr: &money.Money{
							CurrencyCode: "INR",
							Units:        -2250,
						},
						TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
					},
				},
				Summary: &DividendSummary{
					TotalDividendIncomeInUsd: &money.Money{
						CurrencyCode: "USD",
						Units:        120,
					},
					TotalDividendIncomeInInr: &money.Money{
						CurrencyCode: "INR",
						Units:        10600,
					},
					TotalTaxPaidInUsd: &money.Money{
						CurrencyCode: "USD",
						Units:        30,
					},
					TotalTaxPaidInInr: &money.Money{
						CurrencyCode: "INR",
						Units:        2650,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success get dividend info (filtered by executed time)",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					{
						Transaction: &tax.Transaction{
							Symbol: "A",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        20,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
						},
					},
					{
						Transaction: &tax.Transaction{
							Symbol: "A",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        -5,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
						},
					},
					BuyB10May2021(),
					{
						Transaction: &tax.Transaction{
							Symbol: "B",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        100,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
						},
					},
					{
						Transaction: &tax.Transaction{
							Symbol: "B",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        -25,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
						},
					},
					SellA6June2023(),
				},
				fromTime: time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
				toTime:   time.Date(2023, 2, 1, 0, 0, 0, 0, datetime.IST),
			},
			setupMocks: func(f *fields) {
				f.sbiExchangeRateProvider.EXPECT().GetMonthEndBuyRate(time.Date(2022, 12, 1, 0, 0, 0, 0, datetime.IST)).Return(&moneyPb.Money{
					Pb: &money.Money{
						CurrencyCode: "INR",
						Units:        80,
					},
				}, nil).Times(2)
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA", "A", "BB", "B"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusOk(),
					Stocks: map[string]*catalog.Stock{
						"A": {
							Symbol: "A",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Apple",
									StandardName: "Apple Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						},
						"B": {
							Symbol: "B",
							StockBasicDetails: &catalog.StockBasicDetails{
								Name: &catalog.CompanyName{
									ShortName:    "Google",
									StandardName: "Google Inc",
								},
							},
							StockType: catalog.StockType_STOCK_TYPE_ETF,
						},
					},
				}, nil)
			},
			want: &DividendInfo{
				DividendTxns: []*DividendTxn{
					{
						ExecutedAt:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SecuritySymbol: "A",
						SecurityType:   tax.SecurityType_SECURITY_TYPE_EQUITY,
						SecurityName:   "Apple Inc",
						AmountInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        20,
						},
						AmountInInr: &money.Money{
							CurrencyCode: "INR",
							Units:        1600,
						},
						TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
					},
					{
						ExecutedAt:     time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						SecuritySymbol: "A",
						SecurityType:   tax.SecurityType_SECURITY_TYPE_EQUITY,
						SecurityName:   "Apple Inc",
						AmountInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        -5,
						},
						AmountInInr: &money.Money{
							CurrencyCode: "INR",
							Units:        -400,
						},
						TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
					},
				},
				Summary: &DividendSummary{
					TotalDividendIncomeInUsd: &money.Money{
						CurrencyCode: "USD",
						Units:        20,
					},
					TotalDividendIncomeInInr: &money.Money{
						CurrencyCode: "INR",
						Units:        1600,
					},
					TotalTaxPaidInUsd: &money.Money{
						CurrencyCode: "USD",
						Units:        5,
					},
					TotalTaxPaidInInr: &money.Money{
						CurrencyCode: "INR",
						Units:        400,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to get stocks info",
			args: args{
				ctx: context.Background(),
				txns: []*tax.TransactionWrapper{
					BuyA10Jan2021(),
					{
						Transaction: &tax.Transaction{
							Symbol: "A",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        20,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
						},
					},
					{
						Transaction: &tax.Transaction{
							Symbol: "A",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        -5,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
						},
					},
					BuyB10May2021(),
					{
						Transaction: &tax.Transaction{
							Symbol: "B",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        100,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND,
						},
					},
					{
						Transaction: &tax.Transaction{
							Symbol: "B",
							Amount: &money.Money{
								CurrencyCode: "USD",
								Units:        -25,
							},
							ExecutedAt:      timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, datetime.IST)),
							TransactionType: tax.TransactionType_TRANSACTION_TYPE_DIVIDEND_WITHHELD,
						},
					},
					SellA6June2023(),
				},
			},
			setupMocks: func(f *fields) {
				f.ussCatalogMgrClient.EXPECT().GetStocks(gomock.Any(), &catalog.GetStocksRequest{
					Identifiers: &catalog.GetStocksRequest_Symbols{
						Symbols: &catalog.RepeatedStrings{
							Ids: []string{"AA", "A", "BB", "B"},
						},
					},
					FieldMask: &fieldmaskpb.FieldMask{
						Paths: []string{
							StockModelSymbolFieldName,
							StockModelStockTypeFieldName,
							StockModelStockBasicDetailsFieldName,
						},
					},
				}).Return(&catalog.GetStocksResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			conf := &config.Config{}
			gconf, _ := genconf.NewConfig()
			_ = gconf.Set(conf, false, nil)
			tt.setupMocks(f)

			p := NewTransactionProcessor(gconf, f.ciiProvider, f.sbiExchangeRateProvider, f.ussCatalogMgrClient)
			got, err := p.GetDividendInfo(tt.args.ctx, tt.args.txns, tt.args.fromTime, tt.args.toTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDividendInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetDividendInfo() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func TestTransactionProcessor_GetNetLtcgAndStcg(t *testing.T) {
	type args struct {
		ctx   context.Context
		pairs []*BuySellPair
	}
	tests := []struct {
		name    string
		args    args
		want    *LtcgStcgInfo
		wantErr bool
	}{
		{
			name: "success getting net ltcg and stcg info",
			args: args{
				ctx: context.Background(),
				pairs: []*BuySellPair{
					{
						Symbol:    "AA",
						SellType:  tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						BuyTime:   time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:  time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST),
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        40,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        90,
						},
						Comments: "",
					},
					{
						Symbol:    "AA",
						SellType:  tax.SellType_SELL_TYPE_LONG_TERM,
						StockType: catalog.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
						Quantity:  decimal.NewFromInt32(5),
						BuyTime:   time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:  time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        100,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        80,
						},
						Comments: "",
					},
					{
						Symbol:    "BB",
						SellType:  tax.SellType_SELL_TYPE_SHORT_TERM,
						StockType: catalog.StockType_STOCK_TYPE_ETF,
						Quantity:  decimal.NewFromInt32(5),
						BuyTime:   time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:  time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST),
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        150,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Comments: "",
					},
					{
						Symbol:    "BB",
						SellType:  tax.SellType_SELL_TYPE_LONG_TERM,
						StockType: catalog.StockType_STOCK_TYPE_ETF,
						Quantity:  decimal.NewFromInt32(5),
						BuyTime:   time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST),
						SellTime:  time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
						CapitalGainInUsd: &money.Money{
							CurrencyCode: "USD",
							Units:        200,
						},
						ConversionRate: &money.Money{
							CurrencyCode: "INR",
							Units:        110,
						},
						Comments: "",
					},
				},
			},
			want: &LtcgStcgInfo{
				LtcgInUsd: &money.Money{
					CurrencyCode: "USD",
					Units:        300,
				},
				StcgInUsd: &money.Money{
					CurrencyCode: "USD",
					Units:        190,
				},
				LtcgInInr: &money.Money{
					CurrencyCode: "INR",
					Units:        30000,
				},
				StcgInInr: &money.Money{
					CurrencyCode: "INR",
					Units:        18600,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := TransactionProcessor{}
			got, err := p.GetNetLtcgAndStcg(tt.args.ctx, tt.args.pairs)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNetLtcgAndStcg() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetNetLtcgAndStcg() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
