// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mock_transactionprocessor is a generated GoMock package.
package mock_transactionprocessor

import (
	context "context"
	reflect "reflect"
	time "time"

	tax "github.com/epifi/gamma/api/usstocks/tax"
	transactionprocessor "github.com/epifi/gamma/usstocks/tax/transactionprocessor"
	gomock "github.com/golang/mock/gomock"
)

// MockITransactionProcessor is a mock of ITransactionProcessor interface.
type MockITransactionProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockITransactionProcessorMockRecorder
}

// MockITransactionProcessorMockRecorder is the mock recorder for MockITransactionProcessor.
type MockITransactionProcessorMockRecorder struct {
	mock *MockITransactionProcessor
}

// NewMockITransactionProcessor creates a new mock instance.
func NewMockITransactionProcessor(ctrl *gomock.Controller) *MockITransactionProcessor {
	mock := &MockITransactionProcessor{ctrl: ctrl}
	mock.recorder = &MockITransactionProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITransactionProcessor) EXPECT() *MockITransactionProcessorMockRecorder {
	return m.recorder
}

// GetBuySellPairForScheduleFA mocks base method.
func (m *MockITransactionProcessor) GetBuySellPairForScheduleFA(ctx context.Context, txns []*tax.TransactionWrapper, fromTime, toTime time.Time) (map[string][]*transactionprocessor.BuySellPair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuySellPairForScheduleFA", ctx, txns, fromTime, toTime)
	ret0, _ := ret[0].(map[string][]*transactionprocessor.BuySellPair)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBuySellPairForScheduleFA indicates an expected call of GetBuySellPairForScheduleFA.
func (mr *MockITransactionProcessorMockRecorder) GetBuySellPairForScheduleFA(ctx, txns, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuySellPairForScheduleFA", reflect.TypeOf((*MockITransactionProcessor)(nil).GetBuySellPairForScheduleFA), ctx, txns, fromTime, toTime)
}

// GetBuySellPairsMap mocks base method.
func (m *MockITransactionProcessor) GetBuySellPairsMap(ctx context.Context, txns []*tax.TransactionWrapper, fromTime, toTime time.Time) (map[string][]*transactionprocessor.BuySellPair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuySellPairsMap", ctx, txns, fromTime, toTime)
	ret0, _ := ret[0].(map[string][]*transactionprocessor.BuySellPair)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBuySellPairsMap indicates an expected call of GetBuySellPairsMap.
func (mr *MockITransactionProcessorMockRecorder) GetBuySellPairsMap(ctx, txns, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuySellPairsMap", reflect.TypeOf((*MockITransactionProcessor)(nil).GetBuySellPairsMap), ctx, txns, fromTime, toTime)
}

// GetDividendInfo mocks base method.
func (m *MockITransactionProcessor) GetDividendInfo(ctx context.Context, txns []*tax.TransactionWrapper, fromTime, toTime time.Time) (*transactionprocessor.DividendInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDividendInfo", ctx, txns, fromTime, toTime)
	ret0, _ := ret[0].(*transactionprocessor.DividendInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDividendInfo indicates an expected call of GetDividendInfo.
func (mr *MockITransactionProcessorMockRecorder) GetDividendInfo(ctx, txns, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDividendInfo", reflect.TypeOf((*MockITransactionProcessor)(nil).GetDividendInfo), ctx, txns, fromTime, toTime)
}

// GetInterestInfo mocks base method.
func (m *MockITransactionProcessor) GetInterestInfo(ctx context.Context, txns []*tax.TransactionWrapper, fromTime, toTime time.Time) (*transactionprocessor.InterestInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInterestInfo", ctx, txns, fromTime, toTime)
	ret0, _ := ret[0].(*transactionprocessor.InterestInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInterestInfo indicates an expected call of GetInterestInfo.
func (mr *MockITransactionProcessorMockRecorder) GetInterestInfo(ctx, txns, fromTime, toTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInterestInfo", reflect.TypeOf((*MockITransactionProcessor)(nil).GetInterestInfo), ctx, txns, fromTime, toTime)
}

// GetNetLtcgAndStcg mocks base method.
func (m *MockITransactionProcessor) GetNetLtcgAndStcg(ctx context.Context, pairs []*transactionprocessor.BuySellPair) (*transactionprocessor.LtcgStcgInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetLtcgAndStcg", ctx, pairs)
	ret0, _ := ret[0].(*transactionprocessor.LtcgStcgInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetLtcgAndStcg indicates an expected call of GetNetLtcgAndStcg.
func (mr *MockITransactionProcessorMockRecorder) GetNetLtcgAndStcg(ctx, pairs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetLtcgAndStcg", reflect.TypeOf((*MockITransactionProcessor)(nil).GetNetLtcgAndStcg), ctx, pairs)
}
