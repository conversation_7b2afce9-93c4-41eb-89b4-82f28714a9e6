package generator

import (
	"context"
	"fmt"
	"strings"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	ussTaxDocumentParamsPb "github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/api/usstocks/tax/moneywrapper"
	"github.com/epifi/be-common/pkg/money"
)

type Form67Generator struct {
}

type Form67GeneratorImpl DocumentGenerator

func NewForm67Generator() *Form67Generator {
	return &Form67Generator{}
}

// GenerateExcel creates an excel using the form 67 params in the
// defined format https://docs.google.com/document/d/1dj4Nk82RAnwQn-Ukrq6MnWf_Qnt0GBWzo2EIf0SsldE/edit#bookmark=id.5af3ym4rqogl
func (f *Form67Generator) GenerateExcel(ctx context.Context, params *ussTaxDocumentParamsPb.DocumentParams) ([]byte, error) {
	form67Params := params.GetForm_67Params()
	if form67Params == nil {
		return nil, fmt.Errorf("from 67 params cannot be nil, got params of type %T", params.GetParams())
	}

	section := NewSection().
		AppendRow(append(NewStringCellsWitStyle(Bold, "Sr. No", "Name of country", "Source of Income", "Income from outside India",
			"Tax paid outside India", "Rate", "Tax payable on such income in India ",
			"Tax payable on such income under section 115JB/JC"),
			NewStringCellWithStyle(Bold, "For Section 90/90A").WithHorizontalSpan(3),
			NewStringCellWithStyle(Bold, "For Section 91"))).
		AppendRow(NewStringCellsWitStyle(Bold, append(strings.Split(".......", "."),
			"Article No. of Double Taxation Avoidance Agreements", "Rate of tax as per Double Taxation Avoidance Agreements", "Amount")...))

	for i, incomeDetails := range form67Params.GetDetailsOfAllIncomes() {
		if money.IsZero(incomeDetails.GetTaxPaidOutsideIndia().GetInr()) {
			// form 67 entry is not required since no tax has been deducted outside India
			continue
		}
		incomeSourceStr, err := f.incomeSourceToString(incomeDetails.GetIncomeSource())
		if err != nil {
			return nil, fmt.Errorf("failed to transform income source to string: %w", err)
		}

		section.AppendRow(NewStringCells(
			fmt.Sprint(i+1),
			incomeDetails.GetCountryName(),
			incomeSourceStr,
			dollarInrToString(incomeDetails.GetIncomeFromOutsideIndia()),
			dollarInrToString(incomeDetails.GetTaxPaidOutsideIndia()),
			incomeDetails.GetTaxPaidOutsideIndiaRateInPercent(),
			"To be filled by user",
			"Not applicable for salaried individuals:\nEnter 0 (zero).\n\nFor business owners,\nplease contact your tax advisor.",
			"Article 10 of India USA DTAA",
			"Fixed rate of 25% for people who have less than 10% of a company",
			"Lower of\n1. Tax payable under normal provisions in India or\n2. Tax paid outside India\n",
			"0",
		))
	}

	disclaimer, err := GetDisclaimerSection(ussTaxPb.UsStockDocumentType_US_STOCK_DOCUMENT_TYPE_FORM_67)
	if err != nil {
		return nil, fmt.Errorf("failed to get disclaimer section: %w", err)
	}
	// append disclaimer at end
	section.AppendEmptyRows(4).AppendSection(disclaimer)

	buf, err := section.CreateExcel(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create form 67 excel: %w", err)
	}

	return buf, nil
}

func (f *Form67Generator) incomeSourceToString(incomeSource ussTaxPb.IncomeSource) (string, error) {
	switch incomeSource {
	case ussTaxPb.IncomeSource_INCOME_SOURCE_DIVIDEND:
		return "Dividend", nil
	case ussTaxPb.IncomeSource_INCOME_SOURCE_INTEREST:
		return "Interest", nil
	default:
		return "", fmt.Errorf("unhandled income source %v", incomeSource)
	}
}

func dollarInrToString(usdInrWrapper *moneywrapper.UsdInrWrapper) string {
	return fmt.Sprintf("$%s/₹%s", moneyToString(usdInrWrapper.GetUsd()), moneyToString(usdInrWrapper.GetInr()))
}
