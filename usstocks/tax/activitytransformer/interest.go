package activitytransformer

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/money"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

// InterestProcessor transforms an interest activity to a tax transaction
// Interest can be positive (credited to wallet) in case of bond, preferred share interest etc
// Interest can be negative (debited from wallet) in case of margin interest
type InterestProcessor struct {
	*UnHandledSuspenseActivities
}

func NewInterestProcessor() *InterestProcessor {
	return &InterestProcessor{}
}

func (i *InterestProcessor) ProcessActivity(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	activity := req.Activity
	nonTradeActivity := activity.GetNonTradeActivity()
	if nonTradeActivity.GetType() != vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_INTEREST {
		return nil, fmt.Errorf("unhandled %T Activity of type %v", activity.GetActivity(), nonTradeActivity.GetType())
	}

	if nonTradeActivity.GetStatus() != vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED {
		return NewProcessActivityResponse(req.Transactions, req.SuspenseActivities).appendTxn(createNonTradeTransaction(activity, ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP)), nil
	}

	var transactionType ussTaxPb.TransactionType
	if money.IsNegative(nonTradeActivity.GetNetAmount()) {
		// margin interest debit is no-op
		transactionType = ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP
	} else {
		transactionType = ussTaxPb.TransactionType_TRANSACTION_TYPE_INTEREST
	}
	return NewProcessActivityResponse(req.Transactions, req.SuspenseActivities).appendTxn(createNonTradeTransaction(activity, transactionType)), nil
}
