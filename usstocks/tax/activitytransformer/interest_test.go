package activitytransformer

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

func TestInterestProcessor_ProcessActivity(t *testing.T) {
	tests := []struct {
		name    string
		req     *ProcessActivityRequest
		want    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse
		wantErr bool
	}{
		{
			name: "Successfully process interest debit",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_INTEREST,
							ExecutedDate: &datePb.Date{Year: 2023, Month: 05, Day: 11},
							NetAmount:    &moneyPb.Money{CurrencyCode: "USD", Units: -5},
							Description:  "Margin interest debit",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: -5},
								ExecutedAt:       timestampPb.New(time.Date(2023, time.May, 11, 0, 0, 0, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
							},
						},
					},
				}
			},
		},
		{
			name: "Interest credit activity, success",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_INTEREST,
							ExecutedDate: &datePb.Date{Year: 2023, Month: 05, Day: 11},
							NetAmount:    &moneyPb.Money{CurrencyCode: "USD", Units: 5},
							Description:  "credit",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 5},
								ExecutedAt:       timestampPb.New(time.Date(2023, time.May, 11, 0, 0, 0, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_INTEREST,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
							},
						},
					},
				}
			},
			wantErr: false,
		},
		{
			name: "Non interest activity should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_DIVIDEND,
							ExecutedDate: &datePb.Date{Year: 2023, Month: 05, Day: 11},
							NetAmount:    &moneyPb.Money{CurrencyCode: "USD", Units: -5},
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return nil
			},
			wantErr: true,
		},
		{
			name: "Cancelled interest activity should lead to no-op txn",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_INTEREST,
							ExecutedDate: &datePb.Date{Year: 2023, Month: 05, Day: 11},
							NetAmount:    &moneyPb.Money{CurrencyCode: "USD", Units: -5},
							Description:  "Margin interest debit",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_CANCELED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: -5},
								ExecutedAt:       timestampPb.New(time.Date(2023, time.May, 11, 0, 0, 0, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP,
								ParentActivities: []*vgStocksPb.AccountActivity{activity},
							},
						},
					},
				}
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			b := NewInterestProcessor()
			got, err := b.ProcessActivity(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want(tt.req.Activity), protocmp.Transform()); diff != "" {
				t.Errorf("ProcessActivity() diff=%v", diff)
			}
		})
	}
}
