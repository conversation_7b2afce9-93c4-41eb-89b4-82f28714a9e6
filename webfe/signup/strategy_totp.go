package signup

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/session"
	"github.com/epifi/gamma/api/auth/totp"
	"github.com/epifi/gamma/api/auth/totp/enums"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/webfe/signup"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/frontend/header"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/webfe/events"
)

type TOTPAuthStrategy struct {
	authClient       auth.AuthClient
	totpClient       totp.TotpClient
	userClient       user.UsersClient
	sessionClient    session.SessionManagerClient
	releaseEvaluator release.IEvaluator
	eventLogger      events.EventLogger
}

func NewTOTPAuthStrategy(
	authClient auth.AuthClient,
	totpClient totp.TotpClient,
	userClient user.UsersClient,
	sessionClient session.SessionManagerClient,
	releaseEvaluator release.IEvaluator,
	eventLogger events.EventLogger,
) *TOTPAuthStrategy {
	return &TOTPAuthStrategy{
		authClient:       authClient,
		totpClient:       totpClient,
		userClient:       userClient,
		sessionClient:    sessionClient,
		releaseEvaluator: releaseEvaluator,
		eventLogger:      eventLogger,
	}
}

const (
	dummyAccessToken = "dummyTokenFromTOTPAuth"
)

var _ AuthStrategy = &TOTPAuthStrategy{}

func (s *TOTPAuthStrategy) GenerateOtp(ctx context.Context, req *signup.GenerateOtpRequest) (*signup.GenerateOtpResponse, error) {
	ctx = epificontext.CtxWithPhoneNumToken(ctx, obfuscator.HashedPhoneNum(req.GetPhoneNumber()))
	logger.Info(ctx, "gen otp req received")
	if validationResp := s.validateGenerateOtpRequest(ctx, req); validationResp != nil {
		return validationResp, nil
	}

	// return NotFound for non-registered users. Ignoring errors
	actorId, err := s.getRegisteredUser(ctx, req.GetPhoneNumber())
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &signup.GenerateOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusRecordNotFound(),
			},
		}, nil
	}

	ctx = epificontext.CtxWithActorId(ctx, actorId)

	// return AlreadyExists if session is already validated. Ignoring errors.
	hasValid, _ := s.hasValidSession(ctx, req.GetToken())
	if hasValid {
		logger.Info(ctx, "has valid session in gen otp")
		return &signup.GenerateOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusAlreadyExists(),
			},
		}, nil
	}

	return &signup.GenerateOtpResponse{
		RespHeader: header.SuccessRespHeader(),
		Status:     rpc.StatusOk(),
		Token:      req.GetToken(),
	}, nil
}

func (s *TOTPAuthStrategy) VerifyOtp(ctx context.Context, req *signup.VerifyOtpRequest) (*signup.VerifyOtpResponse, error) {
	ctx = epificontext.CtxWithPhoneNumToken(ctx, obfuscator.HashedPhoneNum(req.GetPhoneNumber()))
	logger.Info(ctx, "verify otp req received")
	if validationResp := s.validateVerifyOtpRequest(ctx, req); validationResp != nil {
		return validationResp, nil
	}

	// get registered user actor ID
	actorId, err := s.getRegisteredUser(ctx, req.GetPhoneNumber())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			}, nil
		}
		logger.Error(ctx, "error in get user", zap.Error(err))
		return nil, err
	}

	ctx = epificontext.CtxWithActorId(ctx, actorId)

	isReleased, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_NETWORTH_MCP).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate release for feature", zap.Error(err), zap.Any(logger.FEATURE, typesPb.Feature_FEATURE_NETWORTH_MCP))
		return nil, err
	}
	if !isReleased {
		logger.Info(ctx, "feature networth mcp not released")
		return &signup.VerifyOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusPermissionDenied(),
			},
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	totpResp, err := s.totpClient.VerifyTotp(ctx, &totp.VerifyTotpRequest{
		ActorId: actorId,
		Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
		Totp:    req.GetOtp(),
	})
	if rpcErr := epifigrpc.RPCError(totpResp, err); rpcErr != nil {
		if s.eventLogger != nil {
			s.eventLogger.LogMCPAuthentication(ctx, actorId, req.GetToken(), false)
		}
		if totpResp.GetStatus().GetCode() == uint32(totp.VerifyTotpResponse_STATUS_INVALID_CODE) {
			return &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.NewStatusWithoutDebug(uint32(signup.VerifyOtpResponse_OTP_INCORRECT), "wrong code"),
				},
			}, nil
		}
		logger.Error(ctx, "failed to verify totp", zap.Error(rpcErr))
		return nil, rpcErr
	}

	if s.eventLogger != nil {
		s.eventLogger.LogMCPAuthentication(ctx, actorId, req.GetToken(), true)
	}

	sessRes, err := s.sessionClient.CreateSession(ctx, &session.CreateSessionRequest{
		ActorId:     actorId,
		SessionId:   req.GetToken(),
		Device:      req.GetReq().GetAuth().GetDevice(),
		PhoneNumber: req.GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(sessRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to create session", zap.Error(rpcErr))
		return nil, rpcErr
	}

	return &signup.VerifyOtpResponse{
		RespHeader: header.SuccessRespHeader(),
		Status:     rpc.StatusOk(),
		// Sending dummy token as web frontend expects access token in response
		AccessToken: dummyAccessToken,
	}, nil
}

// validateGenerateOtpRequest validates the GenerateOtp request and returns error response if validation fails
func (s *TOTPAuthStrategy) validateGenerateOtpRequest(ctx context.Context, req *signup.GenerateOtpRequest) *signup.GenerateOtpResponse {
	if req.GetToken() == "" {
		logger.Debug(ctx, "no token provided in totp gen otp")
		return &signup.GenerateOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
		}
	}

	if req.GetPhoneNumber().GetNationalNumber() == 0 {
		logger.Debug(ctx, "invalid phone number provided in totp gen otp")
		return &signup.GenerateOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("phone number required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("phone number required"),
		}
	}
	return nil
}

// validateVerifyOtpRequest validates the VerifyOtp request and returns error response if validation fails
func (s *TOTPAuthStrategy) validateVerifyOtpRequest(ctx context.Context, req *signup.VerifyOtpRequest) *signup.VerifyOtpResponse {
	if req.GetToken() == "" {
		logger.Debug(ctx, "no token provided in totp verify otp")
		return &signup.VerifyOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
		}
	}

	if req.GetOtp() == "" {
		logger.Debug(ctx, "no otp provided in totp verify otp")
		return &signup.VerifyOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("otp required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("otp required"),
		}
	}

	if req.GetPhoneNumber().GetNationalNumber() == 0 {
		logger.Debug(ctx, "invalid phone number provided in totp gen otp")
		return &signup.VerifyOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("phone number required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("phone number required"),
		}
	}
	return nil
}

// getRegisteredUser retrieves the actor ID for a registered user by phone number
func (s *TOTPAuthStrategy) getRegisteredUser(ctx context.Context, phoneNumber *types.PhoneNumber) (string, error) {
	minUserRes, err := s.userClient.GetMinimalUser(ctx, &user.GetMinimalUserRequest{
		Identifier: &user.GetMinimalUserRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
	})
	if rpcErr := epifigrpc.RPCError(minUserRes, err); rpcErr != nil {
		if minUserRes.GetStatus().IsRecordNotFound() {
			return "", epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in get user", zap.Error(rpcErr))
		return "", rpcErr
	}

	return minUserRes.GetMinimalUser().GetActorId(), nil
}

// hasValidSession checks if the provided session token is valid
func (s *TOTPAuthStrategy) hasValidSession(ctx context.Context, token string) (bool, error) {
	res, err := s.sessionClient.ValidateSession(ctx, &session.ValidateSessionRequest{
		SessionId: token,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "failed to validate session", zap.Error(err))
		return false, err
	case res.GetStatus().GetCode() == uint32(session.ValidateSessionResponse_SESSION_EXPIRED):
		return false, nil
	case res.GetStatus().IsSuccess():
		return true, nil
	default:
		logger.Error(ctx, "unexpected status in validate session", zap.String(logger.STATUS, res.GetStatus().String()))
		return false, rpc.StatusAsError(res.GetStatus())
	}
}
