Application:
  Environment: "qa"
  Name: "user"
  IsSecureRedis: true

EpifiDb:
  DbType: "CRDB"
  AppName: "user"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "user"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/postgres/feature-engineering"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    UsersBucketName: "epifi-qa-dev-users"
    CreditReportsBucketName: "epifi-qa-credit-reports"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

OnboardingStageEventPublisher:
  TopicName: "qa-onboarding-stage-update-topic"

Consent:
  Versions:
    FiTnc: 0
    FedTnc: 0
    FiPrivacyPolicy: 0
    FiWealthTnc: 0
    FiP2pInvestmentTnc: 0
    VpaMigration: 0
    SecureUsageTnC: 1
  Urls:
    FiTnc: "https://web.qa.pointz.in/T&C"
    FiTncNonResident: "https://fi.money/tnc/nr"
    FedTnc: "https://www.federalbank.co.in/epifi-tandc#CASA"
    FiPrivacyPolicy: "https://web.qa.pointz.in/privacy"
    FiWealthTnc: "https://web.qa.pointz.in/wealth/TnC"
    FiP2pInvestmentTnc: "https://web.qa.pointz.in/wealth/TnC"

FeatureReleaseConfig:
  FeatureConstraints:
    - ONBOARDING_ADD_FUNDS_V2_2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 314
          MinIOSVersion: 2022
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # internal
    - FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 388
          MinIOSVersion: 2462
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # internal
    - FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 415
          MinIOSVersion: 2619
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # internal
    - FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 422
          MinIOSVersion: 546
    - FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # Pay_Experimental
    - FEATURE_SEND_SMS_DATA_WEALTH_BUILDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 429
          MinIOSVersion: 100000
    - FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 441
          MinIOSVersion: 2798
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

Onboarding:
  NonResidentCrossValidationConfig:
    CrossValidationDataSources:
      FEATURE_NON_RESIDENT_SA:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
      FEATURE_NON_RESIDENT_SA:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
  ConfirmCardMailingAddress:
    EnableConfirmCardMailingAddressV2: true
  IntentSelectionConfigV2:
    EnableDefaultIntentSelection: false
    IntentCollectionScreenFeatureConfig:
      MinIOSVersion: 100
      MinAndroidVersion: 250
      FallbackToEnableFeature: false
      DisableFeature: false
    IntentCollectionScreenPercentageRollout: 100
    IntentConfigMap:
      ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_PERSONAL_LOANS:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_CREDIT_CARD:
        FeatureConfig:
          MinIOSVersion: 2028
          MinAndroidVersion: 316
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_FI_LITE:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_DEBIT_CARD:
        FeatureConfig:
          MinIOSVersion: 2495
          MinAndroidVersion: 395
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100

  NextActionDecisionCacheConfig:
    IsCacheEnabled: true
    CacheTTL: "2160h" # 3 months
  PanValidateV2FeatureConfig:
    MinIOSVersion: 484
    MinAndroidVersion: 336
    FallbackToEnableFeature: true
    DisableFeature: false
  PanValidateV3FeatureConfig:
    MinIOSVersion: 546
    MinAndroidVersion: 396
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableTriggerNROAccountCreation:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  Flags:
    EnableSaDeclarationstage:
      MinAndroidVersion: 100000
      MinIOSVersion: 100000
      FallbackToEnableFeature: false
      DisableFeature: true
    EnablePanAadharCheckInPreCustomerCreationCheckStage: true
    EnableCkyc: false
    EnableRiskCheckForNRUser: false
    AddMoneyBalanceOptions:
      - Amount:
          CurrencyCode: "INR"
          Units: 100
          Nanos: 0
        IsEnabled: true
    EnableSyncOnboarding: true
    EnableSecureUsageGuidelinesConsent: true
    PanAutofill:
      PrefetchCreditReportWithoutPan: true
      RefetchCreditReportOnMismatch: true
    DisabledStages:
      LINKEDIN_VERIFICATION: true
      SHIPPING_ADDRESS_UPDATE: true
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - FEATURE_ENABLE_CONSENT_SCREEN_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 379
              MinIOSVersion: 2415
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 332
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ATT_IOS_PERMISSION_PROMPT:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 1 # enabling for all in iOS as it just sends a flag in screen-options
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - PRIORITIES_VKYC_OVER_ADD_FUNDS:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ONBOARDING_ADD_FUNDS_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 314
              MinIOSVersion: 2022
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - PHONE_NUMBER_AS_REFERRAL_CODE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 1
              MinIOSVersion: 1
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
                - 2 # FNF
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - SCREENER_CHOICE_PAGE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 1
              MinIOSVersion: 1
          Buckets:
            - ONE:
                Start: 0
                End: 99
  TotalAmountViaOrderAddFunds: true
  MinAndroidVersionForManualBalanceRefreshOnbAddFunds: 259
  MinIosVersionForManualBalanceRefreshOnbAddFunds: 1538
  ReferralOfferCodesDuringOnboarding:
    - CODE_1:
        IsEnabled: true
        BeforeAppliedTitle: "<font color='#313234'>FI200: </font><font color='#5d7d4c'>Get up to ₹200</font>"
        AfterAppliedTitle: "<font color='#313234'>\"FI200\" applied</font>"
        BeforeAppliedDesc: "When you add money to your account"
        AfterAppliedDesc: "Get up to ₹200 when you add money"
        BeforeAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-tag.png"
        AfterAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-success-check.png"
        Code: "FI200"
        UnderlyingFiniteCode: "6VLHF3ZZWH"
  ReferralOfferCodesABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99 # enabled for all user-layers in QA for testing purposes
  ReferralOfferWidgetsDuringOnboarding:
    - 6VLHF3ZZWH: # FI200 based
        IsEnabled: true
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get flat </font><font color='#5D7D4C'>₹200 </font><font color='#313234'>when you sign up</font>"
        BgColor: "#E7F7DE"
    - REGULAR:
        IsEnabled: true
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get rewards when you spend</font>"
        BgColor: "#E7F7DE"
  OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
  BlockOnboardingDueToUnlinkedPANAndAadhaar: false
  SkipCountryIdVerification: false
  SkipPassportVerification: false

  SyncOnboardingSqsPublisher:
    QueueName: "qa-sync-onboarding-queue"


  UNNameCheckMailingAddress:
    FromAddress: "<EMAIL>"
    ToAddress: "<EMAIL>"
    FromName: "unnamecheck-test"
    ToName: "unnamecheck-test"

  AWS:
    Region: "ap-south-1"
    S3:
      BucketNames:
        BucketUsers: "epifi-dev-users"

  KYCDedupeRetryCount: 3
  SyncOnboardingInterval: "30s"
  SyncOnboardingCutOff: "1h"
  AccountSetupMaxStuckDuration: 20m
  OnboardingVelocityConfig:
    QueryRangeDuration: 24h
    Threshold: 100
    BucketExpiry: 24h
    BucketPrecision: 3


  CCFiliteStuckUserNudges:
    VKYC:
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 77
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 226
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to get your credit card. \n💡Keep your original PAN ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: " Just finish a 3-minute video KYC call to verify yourself. Tap to start the call now! "
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_AND_PAN:
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 74
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 223
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 224
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your credit card is ready for you 💳"
            Body: "Complete your application now on the Fi app & get access to the most rewarding credit card in just 2 min!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Why do we need PAN card details? 💭"
            Body: "Because its required to process your credit card application! Don’t worry, your details are safe. Fill them up now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "36h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your application is 50% complete!"
            Body: "It wil take just 2 more minutes to access your new Fi-Federal Credit Card. Complete your application now >"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "3% back on every spend 😱"
            Body: "That's what you get with AmpliFi Fi-Federal Credit Card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "96h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "0 Forex & Lounge Access 😱"
            Body: "That's what you get with AmpliFi Fi-Federal Credit Card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EKYC:
      - StuckDuration: "30s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 225
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 75
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🪪 Aadhaar, get set, go!"
            Body: "Enter your Aadhaar details to continue with your credit card application on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "You are just 2️⃣ steps away"
            Body: "from getting the most rewarding credit card. Complete your application now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Reminder"
            Body: "Your credit card application is still pending. Complete it now in just 2 minutes on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    LIVENESS:
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 76
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "90% Done!"
            Body: "It will take hardly a minute to get your hands on your new credit card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Action Pending ⚠️"
            Body: "Complete your application for the AmpliFi Fi-Federal Credit Card. You will get the digital card instantly"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Reminder"
            Body: "Your credit card application is still pending. Complete it now in just 2 minutes on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  StuckUserNudges:
    TNC_CONSENT:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "We just met you and this is crazy..."
            Body: "Finish your KYC to open a Federal Bank Savings Account in minutes!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Let's get to know each other"
            Body: "Finish your KYC and set up your Federal Bank Savings Account!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    INITIATE_CKYC:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Why do we need PAN card details? 💭"
            Body: "Because you are opening a savings account through Fi! Don’t worry, your details are safe with us. Fill them up now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "We're hoping things PAN out between us!"
            Body: "Keep your PAN and Aadhaar details on hand, and you can open a Federal savings account in minutes! Need help? Talk to our Fi Support Team."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 57 # PAN_REMINDER
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    CONFIRM_CARD_MAILING_ADDRESS:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Special delivery! Where do we send your Fi Card?"
            Body: "Add your shipping address and open a savings account in minutes."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DEVICE_REGISTRATION:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "You're ready to open your savings account!"
            Body: "Final step: All you need to do is – log in to the app and open your savings account."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Today is a good day, let's call it Fi-day"
            Body: "Log in to the app and open your savings account."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a secret 4-digit PIN and start using your Visa Platinum Debit Card for online payments!🔑"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    ADD_MONEY:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Congrats! Your Federal savings account is ready."
            Body: "Add ₹5000 or more into your account & earn upto ₹250 in a Smart Deposit"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Add funds into your Federal savings account & win big!"
            Body: "Earn upto ₹250 in a Smart Deposit by transferring ₹5000 or more into your account now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EKYC:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Don't lose momentum⚡"
            Body: "You're minutes away from creating your account via Fi. Securely enter your Aadhaar to continue."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "30 Lakh Fi users have added Aadhaar"
            Body: Did you? Enter your details securely to open a savings a/c in minutes!
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 58 # EKYC_REMINDER
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_MISMATCH:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Houston, we no longer have a problem"
            Body: "Update your Fi app so you can resume opening your savings account now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62 # KYC_VALIDATION_FAILURE
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    NAME_MISMATCH:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Let's do this shall we?"
            Body: "We couldn't verify your KYC details the last time. Update the app so you can continue opening your savings account ✨"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62 # KYC_VALIDATION_FAILURE
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    LIVENESS:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Not home? In a crowd? That's okay📷"
            Body: "Get on a 15-sec video check to verify your identity. Anywhere, anyhow works!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Selfie on, verify ID & you're good!"
            Body: "Continue opening your savings a/c with a quick video check. It's super simple and safe!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 60 # LIVENESS_REMINDER
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_AND_PAN:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Add your PAN details"
            Body: "You're almost there! Continue sign-up for a whole new banking experience🚀"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 118 # ONBOARDING_DOB_AND_PAN_DROP_OFF
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Missed out adding your PAN?🪪"
            Body: "PAN details fast-track your verification. With Fi, securely create your account in minutes."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "10m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "30 Lakh Fi users have added PAN"
            Body: "Did you? Tap to complete your sign-up."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Add your PAN to continue with Fi"
            Body: "Don't miss out on the Fi journey! Securely enter your PAN details🏦"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "20m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your PAN verification is pending"
            Body: "Don't worry, your data will not be shared without your knowledge"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 42 # DOB_AND_PAN_DROP_OFF
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    VKYC:
      - StuckDuration: "1m" #1m
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 44
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m" #2m
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to access your account💡Keep your original PAN card ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "3m" #3m
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 45
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "4m" # 4m
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: "Just finish a 3-minute video KYC call to verify yourself and start using Fi. Tap to start the call now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m" # 5m
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EMPLOYMENT_VERIFICATION:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Are you ready for your ₹250 reward?😍"
            Body: "Get up to ₹250 once you complete your sign-up on Fi. This will take just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💳Ready for your cool debit card?"
            Body: "Just complete your sign-up and get offers from Swiggy, Amazon, Myntra, and more. It takes just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💪Your personal information is safe."
            Body: "Finish your KYC and your savings account will be ready. Your money is insured up to ₹5L."
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  DurationToSkipAddMoney: "15m"
  AddFundsConfig:
    ShowSkipCtaViaQuest: false
    SkipDurationViaQuest: "9m"
    V2PageMinorVersion: 1
  OrderPhysicalDebitCardConfig:
    EnableViaQuest: true

  StuckUserAlerts:
    FEATURE_SA:DEVICE_REGISTRATION:
      - StuckDuration: "2m"
    FEATURE_SA:DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "5m"
      - StuckDuration: "6m"
    FEATURE_SA:CHECK_CREDIT_REPORT_PRESENCE:
      - StuckDuration: "5m"
      - StuckDuration: "3h"
    FEATURE_SA:ADD_MONEY:
      - StuckDuration: "20m"

  DurationToSkipAddFundsForAffluenceClasses:
    - ENTRY_1:
        IsEnabled: false
        AffluenceClass: 1
        Duration: 3m
    - ENTRY_2:
        IsEnabled: false
        AffluenceClass: 2
        Duration: 5m
    - ENTRY_3:
        IsEnabled: false
        AffluenceClass: 3
        Duration: 7m
  WebUrlsForSalaryB2BFlows:
    - FLOW_1:
        IsEnabled: true
        Url: "https://web.qa.pointz.in/signup"

  AffluenceClassesEligibleForBonusTransitionScreen:
    - "AFFLUENCE_CLASS_CLASS_1": true
    - "AFFLUENCE_CLASS_CLASS_2": true
    - "AFFLUENCE_CLASS_CLASS_3": false
    - "AFFLUENCE_CLASS_CLASS_4": false
    - "AFFLUENCE_CLASS_CLASS_5": false
  NrBucketName: "epifi-qa-nrusers"

Flags:
  TrimDebugMessageFromStatus: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: true

Secrets:
  Ids:
    DbUsernamePassword: "qa/rds/postgres/nudge"
    PgdbCredentials: "qa/rds/postgres/feature-engineering"
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"

OnboardingUserUpdatePublisher:
  TopicName: "qa-user-update-topic"

OnboardingUserUpdateVKYCSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-onboarding-user-update-vkyc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdatePublisher:
  QueueName: "qa-shipping-address-update-queue"

ShippingAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-shipping-address-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdateCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-shipping-address-update-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

VKYCUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-vkyc-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EKYCSuccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-kyc-ekyc-success-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UserAccessRevokeUpdatePublisher:
  TopicName: "qa-user-access-revoke-update-topic"
ShippingAddressUpdateEventPublisher:
  TopicName: "qa-user-shipping-address-update-topic"

VKYC:
  Option:
    VKYC_OPTION_LSO:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 0
      MinAndroidVersion: 132
      MinIOSVersion: 142
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CKYC_O:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 132
      MinIOSVersion: 265
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_ONBOARDING:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 100
      MinAndroidVersion: 173
      MinIOSVersion: 651
      SkipOptionFlag: true
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: true
    VKYC_OPTION_STUDENT:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 132
      MinIOSVersion: 265
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_PARTIAL_KYC_DEDUPE:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 132
      MinIOSVersion: 265
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_EKYC_NUMBER_MISMATCH:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 237
      MinIOSVersion: 1317
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_LOW_QUALITY_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 237
      MinIOSVersion: 1317
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_CC_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 237
      MinIOSVersion: 1317
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_USERS:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 237
      MinIOSVersion: 1317
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CLOSED_ACCOUNT_REOPENING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 237
      MinIOSVersion: 1317
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_NON_RESIDENT_ONBOARDING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FEDERAL_LOANS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
  EnableDemandManagement: false

ConsentEventPublisher:
  TopicName: "qa-consent-topic"

VpaMigrationConsentPublisher:
  QueueName: "qa-upi-vpa-migration-consent-queue"

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "01:30"
  EndTime: "03:30"

CreditReportPresencePublisher:
  QueueName: "qa-user-credit-report-presence-queue"

CreditReportPresenceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-credit-report-presence-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

CreditReportVerificationPublisher:
  QueueName: "qa-user-credit-report-verification-queue"

CreditReportVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-credit-report-verification-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

Screening:
  CreditReportPresenceCheck:
    CreditReportPresenceCheckMaxDuration: 30s
  CreditReportVerification:
    CreditScoreThreshold: 600
    CreditReportVerificationMaxDuration: 30s

UserUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BankCustomerUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-bank-customer-update-event-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

CreditReportVerificationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-onboarding-credit-report-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

LivManualReviewEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-onboarding-liveness-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 2
      TimeUnit: "Second"

AfPurchasePublisher:
  QueueName: "qa-event-af-purchase-queue"

EventsAfPurchaseSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-event-af-purchase-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EventsCompletedTnCPublisher:
  QueueName: "qa-event-completed-tnc-queue"

EventsCompletedTnCSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-event-completed-tnc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UserCacheConfig:
  IsCachingEnabled: true
  UserIdPrefix: "user_id_"
  CacheTTl: "2m"

UserGroupCacheConfig:
  IsCachingEnabled: true
  UserGroupEmailPrefix: "USER_GROUP_MAPPING"
  CacheTTl: "2m"

Events:
  AfPurchasePublishDelay: "20s"
  CompletedTnCPublishDelay: "1m"

CreditReportConfig:
  ExperianConsentConfig:
    ConsentExtension: "2160h"
    ConsentExpiry: "4320h"

CreditReportDerivedAttributesPublisher:
  QueueName: "qa-credit-report-derived-attributes-queue"

CreditReportDerivedAttributesSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-credit-report-derived-attributes-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessOnboardingEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-contact-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessAfuEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-contact-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessDeleteUserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-contact-delete-user-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 20
      TimeUnit: "Second"

UserDevicePropertiesUpdatePublisher:
  TopicName: "qa-user-device-properties-update-topic"

DeleteUserPublisher:
  TopicName: "qa-delete-user-event-topic"

ProcessAccessRevokeCooldownPublisher:
  QueueName: "qa-user-access-revoke-cooldown-queue"

ProcessAccessRevokeCooldownSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-access-revoke-cooldown-queue"
  RetryStrategy:
    RegularInterval:
      BaseInterval: 5
      MaxAttempts: 100
      TimeUnit: "Minute"

SyncOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-sync-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"

ProcessSavingsAccountUpdateEvent:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-onboarding-savings-account-state-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessCardCreationEvent:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-onboarding-card-creation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Minute"

VKYCCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

InHouseVkycCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-user-inhouse-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
