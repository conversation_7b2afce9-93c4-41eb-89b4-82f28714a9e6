// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/search/widget/widgets.proto

package widget

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accessinfo "github.com/epifi/gamma/api/frontend/insights/accessinfo"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accessinfo.AddEmailAccountBanner(0)
)

// Validate checks the field values on DeepLinkElement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeepLinkElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeepLinkElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeepLinkElementMultiError, or nil if none found.
func (m *DeepLinkElement) ValidateAll() error {
	return m.validate(true)
}

func (m *DeepLinkElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeepLinkElementValidationError{
					field:  "Link",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeepLinkElementValidationError{
					field:  "Link",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeepLinkElementValidationError{
				field:  "Link",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Text

	// no validation rules for CtaTheme

	// no validation rules for CtaType

	// no validation rules for DeeplinkElementType

	if len(errors) > 0 {
		return DeepLinkElementMultiError(errors)
	}

	return nil
}

// DeepLinkElementMultiError is an error wrapping multiple validation errors
// returned by DeepLinkElement.ValidateAll() if the designated constraints
// aren't met.
type DeepLinkElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeepLinkElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeepLinkElementMultiError) AllErrors() []error { return m }

// DeepLinkElementValidationError is the validation error returned by
// DeepLinkElement.Validate if the designated constraints aren't met.
type DeepLinkElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeepLinkElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeepLinkElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeepLinkElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeepLinkElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeepLinkElementValidationError) ErrorName() string { return "DeepLinkElementValidationError" }

// Error satisfies the builtin error interface
func (e DeepLinkElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeepLinkElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeepLinkElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeepLinkElementValidationError{}

// Validate checks the field values on UnitTrendingSearch with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnitTrendingSearch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnitTrendingSearch with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnitTrendingSearchMultiError, or nil if none found.
func (m *UnitTrendingSearch) ValidateAll() error {
	return m.validate(true)
}

func (m *UnitTrendingSearch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for Display

	if len(errors) > 0 {
		return UnitTrendingSearchMultiError(errors)
	}

	return nil
}

// UnitTrendingSearchMultiError is an error wrapping multiple validation errors
// returned by UnitTrendingSearch.ValidateAll() if the designated constraints
// aren't met.
type UnitTrendingSearchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnitTrendingSearchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnitTrendingSearchMultiError) AllErrors() []error { return m }

// UnitTrendingSearchValidationError is the validation error returned by
// UnitTrendingSearch.Validate if the designated constraints aren't met.
type UnitTrendingSearchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnitTrendingSearchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnitTrendingSearchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnitTrendingSearchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnitTrendingSearchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnitTrendingSearchValidationError) ErrorName() string {
	return "UnitTrendingSearchValidationError"
}

// Error satisfies the builtin error interface
func (e UnitTrendingSearchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnitTrendingSearch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnitTrendingSearchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnitTrendingSearchValidationError{}

// Validate checks the field values on TrendingSearchWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrendingSearchWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrendingSearchWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrendingSearchWidgetMultiError, or nil if none found.
func (m *TrendingSearchWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *TrendingSearchWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetTrendingSearches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TrendingSearchWidgetValidationError{
						field:  fmt.Sprintf("TrendingSearches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TrendingSearchWidgetValidationError{
						field:  fmt.Sprintf("TrendingSearches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TrendingSearchWidgetValidationError{
					field:  fmt.Sprintf("TrendingSearches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TrendingSearchWidgetMultiError(errors)
	}

	return nil
}

// TrendingSearchWidgetMultiError is an error wrapping multiple validation
// errors returned by TrendingSearchWidget.ValidateAll() if the designated
// constraints aren't met.
type TrendingSearchWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrendingSearchWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrendingSearchWidgetMultiError) AllErrors() []error { return m }

// TrendingSearchWidgetValidationError is the validation error returned by
// TrendingSearchWidget.Validate if the designated constraints aren't met.
type TrendingSearchWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrendingSearchWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrendingSearchWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrendingSearchWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrendingSearchWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrendingSearchWidgetValidationError) ErrorName() string {
	return "TrendingSearchWidgetValidationError"
}

// Error satisfies the builtin error interface
func (e TrendingSearchWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrendingSearchWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrendingSearchWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrendingSearchWidgetValidationError{}

// Validate checks the field values on UnitRecentSearch with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnitRecentSearch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnitRecentSearch with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnitRecentSearchMultiError, or nil if none found.
func (m *UnitRecentSearch) ValidateAll() error {
	return m.validate(true)
}

func (m *UnitRecentSearch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return UnitRecentSearchMultiError(errors)
	}

	return nil
}

// UnitRecentSearchMultiError is an error wrapping multiple validation errors
// returned by UnitRecentSearch.ValidateAll() if the designated constraints
// aren't met.
type UnitRecentSearchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnitRecentSearchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnitRecentSearchMultiError) AllErrors() []error { return m }

// UnitRecentSearchValidationError is the validation error returned by
// UnitRecentSearch.Validate if the designated constraints aren't met.
type UnitRecentSearchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnitRecentSearchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnitRecentSearchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnitRecentSearchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnitRecentSearchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnitRecentSearchValidationError) ErrorName() string { return "UnitRecentSearchValidationError" }

// Error satisfies the builtin error interface
func (e UnitRecentSearchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnitRecentSearch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnitRecentSearchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnitRecentSearchValidationError{}

// Validate checks the field values on RecentSearchWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecentSearchWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecentSearchWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecentSearchWidgetMultiError, or nil if none found.
func (m *RecentSearchWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *RecentSearchWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetRecentSearches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecentSearchWidgetValidationError{
						field:  fmt.Sprintf("RecentSearches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecentSearchWidgetValidationError{
						field:  fmt.Sprintf("RecentSearches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecentSearchWidgetValidationError{
					field:  fmt.Sprintf("RecentSearches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecentSearchWidgetMultiError(errors)
	}

	return nil
}

// RecentSearchWidgetMultiError is an error wrapping multiple validation errors
// returned by RecentSearchWidget.ValidateAll() if the designated constraints
// aren't met.
type RecentSearchWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecentSearchWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecentSearchWidgetMultiError) AllErrors() []error { return m }

// RecentSearchWidgetValidationError is the validation error returned by
// RecentSearchWidget.Validate if the designated constraints aren't met.
type RecentSearchWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecentSearchWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecentSearchWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecentSearchWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecentSearchWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecentSearchWidgetValidationError) ErrorName() string {
	return "RecentSearchWidgetValidationError"
}

// Error satisfies the builtin error interface
func (e RecentSearchWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecentSearchWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecentSearchWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecentSearchWidgetValidationError{}

// Validate checks the field values on RelatedQueriesWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RelatedQueriesWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelatedQueriesWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelatedQueriesWidgetMultiError, or nil if none found.
func (m *RelatedQueriesWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *RelatedQueriesWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetRelatedQueriesFirstSection() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RelatedQueriesWidgetValidationError{
						field:  fmt.Sprintf("RelatedQueriesFirstSection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RelatedQueriesWidgetValidationError{
						field:  fmt.Sprintf("RelatedQueriesFirstSection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RelatedQueriesWidgetValidationError{
					field:  fmt.Sprintf("RelatedQueriesFirstSection[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRelatedQueriesSecondSection() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RelatedQueriesWidgetValidationError{
						field:  fmt.Sprintf("RelatedQueriesSecondSection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RelatedQueriesWidgetValidationError{
						field:  fmt.Sprintf("RelatedQueriesSecondSection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RelatedQueriesWidgetValidationError{
					field:  fmt.Sprintf("RelatedQueriesSecondSection[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RelatedQueriesWidgetMultiError(errors)
	}

	return nil
}

// RelatedQueriesWidgetMultiError is an error wrapping multiple validation
// errors returned by RelatedQueriesWidget.ValidateAll() if the designated
// constraints aren't met.
type RelatedQueriesWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelatedQueriesWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelatedQueriesWidgetMultiError) AllErrors() []error { return m }

// RelatedQueriesWidgetValidationError is the validation error returned by
// RelatedQueriesWidget.Validate if the designated constraints aren't met.
type RelatedQueriesWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelatedQueriesWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelatedQueriesWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelatedQueriesWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelatedQueriesWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelatedQueriesWidgetValidationError) ErrorName() string {
	return "RelatedQueriesWidgetValidationError"
}

// Error satisfies the builtin error interface
func (e RelatedQueriesWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelatedQueriesWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelatedQueriesWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelatedQueriesWidgetValidationError{}

// Validate checks the field values on UnitRelatedQuery with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnitRelatedQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnitRelatedQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnitRelatedQueryMultiError, or nil if none found.
func (m *UnitRelatedQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *UnitRelatedQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetSearchScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnitRelatedQueryValidationError{
					field:  "SearchScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnitRelatedQueryValidationError{
					field:  "SearchScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnitRelatedQueryValidationError{
				field:  "SearchScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnitRelatedQueryMultiError(errors)
	}

	return nil
}

// UnitRelatedQueryMultiError is an error wrapping multiple validation errors
// returned by UnitRelatedQuery.ValidateAll() if the designated constraints
// aren't met.
type UnitRelatedQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnitRelatedQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnitRelatedQueryMultiError) AllErrors() []error { return m }

// UnitRelatedQueryValidationError is the validation error returned by
// UnitRelatedQuery.Validate if the designated constraints aren't met.
type UnitRelatedQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnitRelatedQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnitRelatedQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnitRelatedQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnitRelatedQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnitRelatedQueryValidationError) ErrorName() string { return "UnitRelatedQueryValidationError" }

// Error satisfies the builtin error interface
func (e UnitRelatedQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnitRelatedQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnitRelatedQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnitRelatedQueryValidationError{}

// Validate checks the field values on UnitQuickLink with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnitQuickLink) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnitQuickLink with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnitQuickLinkMultiError, or
// nil if none found.
func (m *UnitQuickLink) ValidateAll() error {
	return m.validate(true)
}

func (m *UnitQuickLink) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageUrl

	// no validation rules for ShortDesc

	if all {
		switch v := interface{}(m.GetLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnitQuickLinkValidationError{
					field:  "Link",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnitQuickLinkValidationError{
					field:  "Link",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnitQuickLinkValidationError{
				field:  "Link",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return UnitQuickLinkMultiError(errors)
	}

	return nil
}

// UnitQuickLinkMultiError is an error wrapping multiple validation errors
// returned by UnitQuickLink.ValidateAll() if the designated constraints
// aren't met.
type UnitQuickLinkMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnitQuickLinkMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnitQuickLinkMultiError) AllErrors() []error { return m }

// UnitQuickLinkValidationError is the validation error returned by
// UnitQuickLink.Validate if the designated constraints aren't met.
type UnitQuickLinkValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnitQuickLinkValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnitQuickLinkValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnitQuickLinkValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnitQuickLinkValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnitQuickLinkValidationError) ErrorName() string { return "UnitQuickLinkValidationError" }

// Error satisfies the builtin error interface
func (e UnitQuickLinkValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnitQuickLink.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnitQuickLinkValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnitQuickLinkValidationError{}

// Validate checks the field values on QuickLinkWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QuickLinkWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuickLinkWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuickLinkWidgetMultiError, or nil if none found.
func (m *QuickLinkWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *QuickLinkWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetQuickLinks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuickLinkWidgetValidationError{
						field:  fmt.Sprintf("QuickLinks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuickLinkWidgetValidationError{
						field:  fmt.Sprintf("QuickLinks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuickLinkWidgetValidationError{
					field:  fmt.Sprintf("QuickLinks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QuickLinkWidgetMultiError(errors)
	}

	return nil
}

// QuickLinkWidgetMultiError is an error wrapping multiple validation errors
// returned by QuickLinkWidget.ValidateAll() if the designated constraints
// aren't met.
type QuickLinkWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuickLinkWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuickLinkWidgetMultiError) AllErrors() []error { return m }

// QuickLinkWidgetValidationError is the validation error returned by
// QuickLinkWidget.Validate if the designated constraints aren't met.
type QuickLinkWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuickLinkWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuickLinkWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuickLinkWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuickLinkWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuickLinkWidgetValidationError) ErrorName() string { return "QuickLinkWidgetValidationError" }

// Error satisfies the builtin error interface
func (e QuickLinkWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuickLinkWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuickLinkWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuickLinkWidgetValidationError{}

// Validate checks the field values on SummaryWidget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SummaryWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SummaryWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SummaryWidgetMultiError, or
// nil if none found.
func (m *SummaryWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *SummaryWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	// no validation rules for ShortDesc

	// no validation rules for BoldText

	if all {
		switch v := interface{}(m.GetTransactionGraph()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SummaryWidgetValidationError{
					field:  "TransactionGraph",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SummaryWidgetValidationError{
					field:  "TransactionGraph",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionGraph()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SummaryWidgetValidationError{
				field:  "TransactionGraph",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClickToCopy

	if all {
		switch v := interface{}(m.GetClickToCopyData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SummaryWidgetValidationError{
					field:  "ClickToCopyData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SummaryWidgetValidationError{
					field:  "ClickToCopyData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClickToCopyData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SummaryWidgetValidationError{
				field:  "ClickToCopyData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SummaryWidgetMultiError(errors)
	}

	return nil
}

// SummaryWidgetMultiError is an error wrapping multiple validation errors
// returned by SummaryWidget.ValidateAll() if the designated constraints
// aren't met.
type SummaryWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SummaryWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SummaryWidgetMultiError) AllErrors() []error { return m }

// SummaryWidgetValidationError is the validation error returned by
// SummaryWidget.Validate if the designated constraints aren't met.
type SummaryWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SummaryWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SummaryWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SummaryWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SummaryWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SummaryWidgetValidationError) ErrorName() string { return "SummaryWidgetValidationError" }

// Error satisfies the builtin error interface
func (e SummaryWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSummaryWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SummaryWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SummaryWidgetValidationError{}

// Validate checks the field values on ClickToCopyData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ClickToCopyData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClickToCopyData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClickToCopyDataMultiError, or nil if none found.
func (m *ClickToCopyData) ValidateAll() error {
	return m.validate(true)
}

func (m *ClickToCopyData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TextToCopy

	// no validation rules for ToastMessage

	if len(errors) > 0 {
		return ClickToCopyDataMultiError(errors)
	}

	return nil
}

// ClickToCopyDataMultiError is an error wrapping multiple validation errors
// returned by ClickToCopyData.ValidateAll() if the designated constraints
// aren't met.
type ClickToCopyDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClickToCopyDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClickToCopyDataMultiError) AllErrors() []error { return m }

// ClickToCopyDataValidationError is the validation error returned by
// ClickToCopyData.Validate if the designated constraints aren't met.
type ClickToCopyDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClickToCopyDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClickToCopyDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClickToCopyDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClickToCopyDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClickToCopyDataValidationError) ErrorName() string { return "ClickToCopyDataValidationError" }

// Error satisfies the builtin error interface
func (e ClickToCopyDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClickToCopyData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClickToCopyDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClickToCopyDataValidationError{}

// Validate checks the field values on TransactionGraph with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TransactionGraph) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionGraph with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionGraphMultiError, or nil if none found.
func (m *TransactionGraph) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionGraph) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetTransactionPlotPoint() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TransactionGraphValidationError{
						field:  fmt.Sprintf("TransactionPlotPoint[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TransactionGraphValidationError{
						field:  fmt.Sprintf("TransactionPlotPoint[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TransactionGraphValidationError{
					field:  fmt.Sprintf("TransactionPlotPoint[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TransactionGraphMultiError(errors)
	}

	return nil
}

// TransactionGraphMultiError is an error wrapping multiple validation errors
// returned by TransactionGraph.ValidateAll() if the designated constraints
// aren't met.
type TransactionGraphMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionGraphMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionGraphMultiError) AllErrors() []error { return m }

// TransactionGraphValidationError is the validation error returned by
// TransactionGraph.Validate if the designated constraints aren't met.
type TransactionGraphValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionGraphValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionGraphValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionGraphValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionGraphValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionGraphValidationError) ErrorName() string { return "TransactionGraphValidationError" }

// Error satisfies the builtin error interface
func (e TransactionGraphValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionGraph.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionGraphValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionGraphValidationError{}

// Validate checks the field values on TransactionPlotPoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionPlotPoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionPlotPoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionPlotPointMultiError, or nil if none found.
func (m *TransactionPlotPoint) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionPlotPoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransactionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionPlotPointValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionPlotPointValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionPlotPointValidationError{
				field:  "TransactionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionPlotPointValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionPlotPointValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionPlotPointValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionPlotPointMultiError(errors)
	}

	return nil
}

// TransactionPlotPointMultiError is an error wrapping multiple validation
// errors returned by TransactionPlotPoint.ValidateAll() if the designated
// constraints aren't met.
type TransactionPlotPointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionPlotPointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionPlotPointMultiError) AllErrors() []error { return m }

// TransactionPlotPointValidationError is the validation error returned by
// TransactionPlotPoint.Validate if the designated constraints aren't met.
type TransactionPlotPointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionPlotPointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionPlotPointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionPlotPointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionPlotPointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionPlotPointValidationError) ErrorName() string {
	return "TransactionPlotPointValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionPlotPointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionPlotPoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionPlotPointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionPlotPointValidationError{}

// Validate checks the field values on TxnCategory with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TxnCategory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TxnCategory with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TxnCategoryMultiError, or
// nil if none found.
func (m *TxnCategory) ValidateAll() error {
	return m.validate(true)
}

func (m *TxnCategory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CategoryName

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return TxnCategoryMultiError(errors)
	}

	return nil
}

// TxnCategoryMultiError is an error wrapping multiple validation errors
// returned by TxnCategory.ValidateAll() if the designated constraints aren't met.
type TxnCategoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TxnCategoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TxnCategoryMultiError) AllErrors() []error { return m }

// TxnCategoryValidationError is the validation error returned by
// TxnCategory.Validate if the designated constraints aren't met.
type TxnCategoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TxnCategoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TxnCategoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TxnCategoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TxnCategoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TxnCategoryValidationError) ErrorName() string { return "TxnCategoryValidationError" }

// Error satisfies the builtin error interface
func (e TxnCategoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTxnCategory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TxnCategoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TxnCategoryValidationError{}

// Validate checks the field values on TransactionView with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TransactionView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionViewMultiError, or nil if none found.
func (m *TransactionView) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	// no validation rules for Title

	// no validation rules for ShortDesc

	// no validation rules for ShortIconUrl

	// no validation rules for Amount

	// no validation rules for AmountBadge

	if all {
		switch v := interface{}(m.GetViewLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "ViewLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "ViewLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionViewValidationError{
				field:  "ViewLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IconColour

	if all {
		switch v := interface{}(m.GetTransactionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionViewValidationError{
				field:  "TransactionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmountColour

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetIconDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "IconDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "IconDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionViewValidationError{
				field:  "IconDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTxnCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TransactionViewValidationError{
						field:  fmt.Sprintf("TxnCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TransactionViewValidationError{
						field:  fmt.Sprintf("TxnCategories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TransactionViewValidationError{
					field:  fmt.Sprintf("TxnCategories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AmountBadgeIconUrl

	if all {
		switch v := interface{}(m.GetRewardsEarnedValueChip()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "RewardsEarnedValueChip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "RewardsEarnedValueChip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardsEarnedValueChip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionViewValidationError{
				field:  "RewardsEarnedValueChip",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPartnerTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionViewValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionViewValidationError{
				field:  "PartnerTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionViewMultiError(errors)
	}

	return nil
}

// TransactionViewMultiError is an error wrapping multiple validation errors
// returned by TransactionView.ValidateAll() if the designated constraints
// aren't met.
type TransactionViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionViewMultiError) AllErrors() []error { return m }

// TransactionViewValidationError is the validation error returned by
// TransactionView.Validate if the designated constraints aren't met.
type TransactionViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionViewValidationError) ErrorName() string { return "TransactionViewValidationError" }

// Error satisfies the builtin error interface
func (e TransactionViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionViewValidationError{}

// Validate checks the field values on UpcomingPaymentsWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpcomingPaymentsWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpcomingPaymentsWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpcomingPaymentsWidgetMultiError, or nil if none found.
func (m *UpcomingPaymentsWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *UpcomingPaymentsWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetScheduledPayments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpcomingPaymentsWidgetValidationError{
						field:  fmt.Sprintf("ScheduledPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpcomingPaymentsWidgetValidationError{
						field:  fmt.Sprintf("ScheduledPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpcomingPaymentsWidgetValidationError{
					field:  fmt.Sprintf("ScheduledPayments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetViewAll()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingPaymentsWidgetValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingPaymentsWidgetValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAll()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingPaymentsWidgetValidationError{
				field:  "ViewAll",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpcomingPaymentsWidgetMultiError(errors)
	}

	return nil
}

// UpcomingPaymentsWidgetMultiError is an error wrapping multiple validation
// errors returned by UpcomingPaymentsWidget.ValidateAll() if the designated
// constraints aren't met.
type UpcomingPaymentsWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpcomingPaymentsWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpcomingPaymentsWidgetMultiError) AllErrors() []error { return m }

// UpcomingPaymentsWidgetValidationError is the validation error returned by
// UpcomingPaymentsWidget.Validate if the designated constraints aren't met.
type UpcomingPaymentsWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpcomingPaymentsWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpcomingPaymentsWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpcomingPaymentsWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpcomingPaymentsWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpcomingPaymentsWidgetValidationError) ErrorName() string {
	return "UpcomingPaymentsWidgetValidationError"
}

// Error satisfies the builtin error interface
func (e UpcomingPaymentsWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpcomingPaymentsWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpcomingPaymentsWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpcomingPaymentsWidgetValidationError{}

// Validate checks the field values on TransactionsWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionsWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionsWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionsWidgetMultiError, or nil if none found.
func (m *TransactionsWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionsWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TransactionsWidgetValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TransactionsWidgetValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TransactionsWidgetValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetViewAll()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionsWidgetValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionsWidgetValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAll()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionsWidgetValidationError{
				field:  "ViewAll",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetViewMore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionsWidgetValidationError{
					field:  "ViewMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionsWidgetValidationError{
					field:  "ViewMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewMore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionsWidgetValidationError{
				field:  "ViewMore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardMonthSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionsWidgetValidationError{
					field:  "RewardMonthSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionsWidgetValidationError{
					field:  "RewardMonthSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardMonthSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionsWidgetValidationError{
				field:  "RewardMonthSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionsWidgetMultiError(errors)
	}

	return nil
}

// TransactionsWidgetMultiError is an error wrapping multiple validation errors
// returned by TransactionsWidget.ValidateAll() if the designated constraints
// aren't met.
type TransactionsWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionsWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionsWidgetMultiError) AllErrors() []error { return m }

// TransactionsWidgetValidationError is the validation error returned by
// TransactionsWidget.Validate if the designated constraints aren't met.
type TransactionsWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionsWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionsWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionsWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionsWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionsWidgetValidationError) ErrorName() string {
	return "TransactionsWidgetValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionsWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionsWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionsWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionsWidgetValidationError{}

// Validate checks the field values on UnitSupportView with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnitSupportView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnitSupportView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnitSupportViewMultiError, or nil if none found.
func (m *UnitSupportView) ValidateAll() error {
	return m.validate(true)
}

func (m *UnitSupportView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Desc

	if all {
		switch v := interface{}(m.GetLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnitSupportViewValidationError{
					field:  "Link",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnitSupportViewValidationError{
					field:  "Link",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnitSupportViewValidationError{
				field:  "Link",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnitSupportViewMultiError(errors)
	}

	return nil
}

// UnitSupportViewMultiError is an error wrapping multiple validation errors
// returned by UnitSupportView.ValidateAll() if the designated constraints
// aren't met.
type UnitSupportViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnitSupportViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnitSupportViewMultiError) AllErrors() []error { return m }

// UnitSupportViewValidationError is the validation error returned by
// UnitSupportView.Validate if the designated constraints aren't met.
type UnitSupportViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnitSupportViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnitSupportViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnitSupportViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnitSupportViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnitSupportViewValidationError) ErrorName() string { return "UnitSupportViewValidationError" }

// Error satisfies the builtin error interface
func (e UnitSupportViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnitSupportView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnitSupportViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnitSupportViewValidationError{}

// Validate checks the field values on SupportWidget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SupportWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupportWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SupportWidgetMultiError, or
// nil if none found.
func (m *SupportWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *SupportWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetSupportTitles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SupportWidgetValidationError{
						field:  fmt.Sprintf("SupportTitles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SupportWidgetValidationError{
						field:  fmt.Sprintf("SupportTitles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SupportWidgetValidationError{
					field:  fmt.Sprintf("SupportTitles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetViewAll()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupportWidgetValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupportWidgetValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAll()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupportWidgetValidationError{
				field:  "ViewAll",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetViewMore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupportWidgetValidationError{
					field:  "ViewMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupportWidgetValidationError{
					field:  "ViewMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewMore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupportWidgetValidationError{
				field:  "ViewMore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SupportWidgetMultiError(errors)
	}

	return nil
}

// SupportWidgetMultiError is an error wrapping multiple validation errors
// returned by SupportWidget.ValidateAll() if the designated constraints
// aren't met.
type SupportWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupportWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupportWidgetMultiError) AllErrors() []error { return m }

// SupportWidgetValidationError is the validation error returned by
// SupportWidget.Validate if the designated constraints aren't met.
type SupportWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupportWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupportWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupportWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupportWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupportWidgetValidationError) ErrorName() string { return "SupportWidgetValidationError" }

// Error satisfies the builtin error interface
func (e SupportWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupportWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupportWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupportWidgetValidationError{}

// Validate checks the field values on PromotionWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PromotionWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PromotionWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PromotionWidgetMultiError, or nil if none found.
func (m *PromotionWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *PromotionWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Display

	// no validation rules for Title

	// no validation rules for Desc

	// no validation rules for ImgUrl

	if all {
		switch v := interface{}(m.GetDeepLinkView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PromotionWidgetValidationError{
					field:  "DeepLinkView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PromotionWidgetValidationError{
					field:  "DeepLinkView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeepLinkView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PromotionWidgetValidationError{
				field:  "DeepLinkView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PromotionWidgetMultiError(errors)
	}

	return nil
}

// PromotionWidgetMultiError is an error wrapping multiple validation errors
// returned by PromotionWidget.ValidateAll() if the designated constraints
// aren't met.
type PromotionWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PromotionWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PromotionWidgetMultiError) AllErrors() []error { return m }

// PromotionWidgetValidationError is the validation error returned by
// PromotionWidget.Validate if the designated constraints aren't met.
type PromotionWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PromotionWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PromotionWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PromotionWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PromotionWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PromotionWidgetValidationError) ErrorName() string { return "PromotionWidgetValidationError" }

// Error satisfies the builtin error interface
func (e PromotionWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPromotionWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PromotionWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PromotionWidgetValidationError{}

// Validate checks the field values on CtaSyncGmailData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CtaSyncGmailData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CtaSyncGmailData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CtaSyncGmailDataMultiError, or nil if none found.
func (m *CtaSyncGmailData) ValidateAll() error {
	return m.validate(true)
}

func (m *CtaSyncGmailData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShowStatus

	// no validation rules for RedirectUrl

	// no validation rules for WindowTitle

	if all {
		switch v := interface{}(m.GetBannerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CtaSyncGmailDataValidationError{
					field:  "BannerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CtaSyncGmailDataValidationError{
					field:  "BannerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBannerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CtaSyncGmailDataValidationError{
				field:  "BannerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CtaSyncGmailDataMultiError(errors)
	}

	return nil
}

// CtaSyncGmailDataMultiError is an error wrapping multiple validation errors
// returned by CtaSyncGmailData.ValidateAll() if the designated constraints
// aren't met.
type CtaSyncGmailDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CtaSyncGmailDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CtaSyncGmailDataMultiError) AllErrors() []error { return m }

// CtaSyncGmailDataValidationError is the validation error returned by
// CtaSyncGmailData.Validate if the designated constraints aren't met.
type CtaSyncGmailDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CtaSyncGmailDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CtaSyncGmailDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CtaSyncGmailDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CtaSyncGmailDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CtaSyncGmailDataValidationError) ErrorName() string { return "CtaSyncGmailDataValidationError" }

// Error satisfies the builtin error interface
func (e CtaSyncGmailDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCtaSyncGmailData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CtaSyncGmailDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CtaSyncGmailDataValidationError{}

// Validate checks the field values on CtaWidget with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CtaWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CtaWidget with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CtaWidgetMultiError, or nil
// if none found.
func (m *CtaWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *CtaWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cta

	if all {
		switch v := interface{}(m.GetDeepLinkView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CtaWidgetValidationError{
					field:  "DeepLinkView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CtaWidgetValidationError{
					field:  "DeepLinkView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeepLinkView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CtaWidgetValidationError{
				field:  "DeepLinkView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.CtaData.(type) {
	case *CtaWidget_CtaSyncGmailData:
		if v == nil {
			err := CtaWidgetValidationError{
				field:  "CtaData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCtaSyncGmailData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CtaWidgetValidationError{
						field:  "CtaSyncGmailData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CtaWidgetValidationError{
						field:  "CtaSyncGmailData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCtaSyncGmailData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CtaWidgetValidationError{
					field:  "CtaSyncGmailData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CtaWidgetMultiError(errors)
	}

	return nil
}

// CtaWidgetMultiError is an error wrapping multiple validation errors returned
// by CtaWidget.ValidateAll() if the designated constraints aren't met.
type CtaWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CtaWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CtaWidgetMultiError) AllErrors() []error { return m }

// CtaWidgetValidationError is the validation error returned by
// CtaWidget.Validate if the designated constraints aren't met.
type CtaWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CtaWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CtaWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CtaWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CtaWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CtaWidgetValidationError) ErrorName() string { return "CtaWidgetValidationError" }

// Error satisfies the builtin error interface
func (e CtaWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCtaWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CtaWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CtaWidgetValidationError{}

// Validate checks the field values on FilterWidget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FilterWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilterWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FilterWidgetMultiError, or
// nil if none found.
func (m *FilterWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *FilterWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilterField

	// no validation rules for IconUrl

	// no validation rules for Title

	// no validation rules for Visible

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FilterWidgetValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FilterWidgetValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FilterWidgetValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FilterWidgetValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FilterWidgetValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FilterWidgetValidationError{
				field:  "Range",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Heading

	// no validation rules for Applicable

	// no validation rules for Searchable

	// no validation rules for Detail

	if len(errors) > 0 {
		return FilterWidgetMultiError(errors)
	}

	return nil
}

// FilterWidgetMultiError is an error wrapping multiple validation errors
// returned by FilterWidget.ValidateAll() if the designated constraints aren't met.
type FilterWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilterWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilterWidgetMultiError) AllErrors() []error { return m }

// FilterWidgetValidationError is the validation error returned by
// FilterWidget.Validate if the designated constraints aren't met.
type FilterWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilterWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilterWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilterWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilterWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilterWidgetValidationError) ErrorName() string { return "FilterWidgetValidationError" }

// Error satisfies the builtin error interface
func (e FilterWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilterWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilterWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilterWidgetValidationError{}

// Validate checks the field values on FilterValue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FilterValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilterValue with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FilterValueMultiError, or
// nil if none found.
func (m *FilterValue) ValidateAll() error {
	return m.validate(true)
}

func (m *FilterValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Selected

	// no validation rules for Visible

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return FilterValueMultiError(errors)
	}

	return nil
}

// FilterValueMultiError is an error wrapping multiple validation errors
// returned by FilterValue.ValidateAll() if the designated constraints aren't met.
type FilterValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilterValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilterValueMultiError) AllErrors() []error { return m }

// FilterValueValidationError is the validation error returned by
// FilterValue.Validate if the designated constraints aren't met.
type FilterValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilterValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilterValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilterValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilterValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilterValueValidationError) ErrorName() string { return "FilterValueValidationError" }

// Error satisfies the builtin error interface
func (e FilterValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilterValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilterValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilterValueValidationError{}

// Validate checks the field values on Range with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Range) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Range with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RangeMultiError, or nil if none found.
func (m *Range) ValidateAll() error {
	return m.validate(true)
}

func (m *Range) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *Range_TimeRange:
		if v == nil {
			err := RangeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTimeRange()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RangeValidationError{
						field:  "TimeRange",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RangeValidationError{
						field:  "TimeRange",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RangeValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Range_AmountRange:
		if v == nil {
			err := RangeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAmountRange()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RangeValidationError{
						field:  "AmountRange",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RangeValidationError{
						field:  "AmountRange",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAmountRange()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RangeValidationError{
					field:  "AmountRange",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RangeMultiError(errors)
	}

	return nil
}

// RangeMultiError is an error wrapping multiple validation errors returned by
// Range.ValidateAll() if the designated constraints aren't met.
type RangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeMultiError) AllErrors() []error { return m }

// RangeValidationError is the validation error returned by Range.Validate if
// the designated constraints aren't met.
type RangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeValidationError) ErrorName() string { return "RangeValidationError" }

// Error satisfies the builtin error interface
func (e RangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeValidationError{}

// Validate checks the field values on TimeRange with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangeMultiError, or nil
// if none found.
func (m *TimeRange) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimeRangeMultiError(errors)
	}

	return nil
}

// TimeRangeMultiError is an error wrapping multiple validation errors returned
// by TimeRange.ValidateAll() if the designated constraints aren't met.
type TimeRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeMultiError) AllErrors() []error { return m }

// TimeRangeValidationError is the validation error returned by
// TimeRange.Validate if the designated constraints aren't met.
type TimeRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeValidationError) ErrorName() string { return "TimeRangeValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeValidationError{}

// Validate checks the field values on AmountRange with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AmountRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AmountRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AmountRangeMultiError, or
// nil if none found.
func (m *AmountRange) ValidateAll() error {
	return m.validate(true)
}

func (m *AmountRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmountRangeValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmountRangeValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmountRangeValidationError{
				field:  "FromAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmountRangeValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmountRangeValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmountRangeValidationError{
				field:  "ToAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AmountRangeMultiError(errors)
	}

	return nil
}

// AmountRangeMultiError is an error wrapping multiple validation errors
// returned by AmountRange.ValidateAll() if the designated constraints aren't met.
type AmountRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmountRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmountRangeMultiError) AllErrors() []error { return m }

// AmountRangeValidationError is the validation error returned by
// AmountRange.Validate if the designated constraints aren't met.
type AmountRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmountRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmountRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmountRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmountRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmountRangeValidationError) ErrorName() string { return "AmountRangeValidationError" }

// Error satisfies the builtin error interface
func (e AmountRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmountRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmountRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmountRangeValidationError{}

// Validate checks the field values on WarningWidget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WarningWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WarningWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WarningWidgetMultiError, or
// nil if none found.
func (m *WarningWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *WarningWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningWidgetValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "HeaderIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "HeaderIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningWidgetValidationError{
				field:  "HeaderIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "Heading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "Heading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningWidgetValidationError{
				field:  "Heading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubheading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "Subheading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "Subheading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubheading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningWidgetValidationError{
				field:  "Subheading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningWidgetValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningWidgetValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WarningWidgetMultiError(errors)
	}

	return nil
}

// WarningWidgetMultiError is an error wrapping multiple validation errors
// returned by WarningWidget.ValidateAll() if the designated constraints
// aren't met.
type WarningWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WarningWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WarningWidgetMultiError) AllErrors() []error { return m }

// WarningWidgetValidationError is the validation error returned by
// WarningWidget.Validate if the designated constraints aren't met.
type WarningWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WarningWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WarningWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WarningWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WarningWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WarningWidgetValidationError) ErrorName() string { return "WarningWidgetValidationError" }

// Error satisfies the builtin error interface
func (e WarningWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWarningWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WarningWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WarningWidgetValidationError{}

// Validate checks the field values on RewardsEarnedValueChip with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardsEarnedValueChip) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardsEarnedValueChip with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardsEarnedValueChipMultiError, or nil if none found.
func (m *RewardsEarnedValueChip) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardsEarnedValueChip) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsEarnedValueChipValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsEarnedValueChipValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsEarnedValueChipValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowWashedOutChip

	if all {
		switch v := interface{}(m.GetAdditionalValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsEarnedValueChipValidationError{
					field:  "AdditionalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsEarnedValueChipValidationError{
					field:  "AdditionalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsEarnedValueChipValidationError{
				field:  "AdditionalValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardsEarnedValueChipMultiError(errors)
	}

	return nil
}

// RewardsEarnedValueChipMultiError is an error wrapping multiple validation
// errors returned by RewardsEarnedValueChip.ValidateAll() if the designated
// constraints aren't met.
type RewardsEarnedValueChipMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardsEarnedValueChipMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardsEarnedValueChipMultiError) AllErrors() []error { return m }

// RewardsEarnedValueChipValidationError is the validation error returned by
// RewardsEarnedValueChip.Validate if the designated constraints aren't met.
type RewardsEarnedValueChipValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardsEarnedValueChipValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardsEarnedValueChipValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardsEarnedValueChipValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardsEarnedValueChipValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardsEarnedValueChipValidationError) ErrorName() string {
	return "RewardsEarnedValueChipValidationError"
}

// Error satisfies the builtin error interface
func (e RewardsEarnedValueChipValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardsEarnedValueChip.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardsEarnedValueChipValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardsEarnedValueChipValidationError{}

// Validate checks the field values on RewardSummaryWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardSummaryWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardSummaryWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardSummaryWidgetMultiError, or nil if none found.
func (m *RewardSummaryWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardSummaryWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidgetValidationError{
					field:  "Heading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidgetValidationError{
					field:  "Heading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidgetValidationError{
				field:  "Heading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardSummaryRows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardSummaryWidgetValidationError{
						field:  fmt.Sprintf("RewardSummaryRows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardSummaryWidgetValidationError{
						field:  fmt.Sprintf("RewardSummaryRows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardSummaryWidgetValidationError{
					field:  fmt.Sprintf("RewardSummaryRows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetIndicatorInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidgetValidationError{
					field:  "IndicatorInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidgetValidationError{
					field:  "IndicatorInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIndicatorInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidgetValidationError{
				field:  "IndicatorInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardSummaryWidgetMultiError(errors)
	}

	return nil
}

// RewardSummaryWidgetMultiError is an error wrapping multiple validation
// errors returned by RewardSummaryWidget.ValidateAll() if the designated
// constraints aren't met.
type RewardSummaryWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardSummaryWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardSummaryWidgetMultiError) AllErrors() []error { return m }

// RewardSummaryWidgetValidationError is the validation error returned by
// RewardSummaryWidget.Validate if the designated constraints aren't met.
type RewardSummaryWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardSummaryWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardSummaryWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardSummaryWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardSummaryWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardSummaryWidgetValidationError) ErrorName() string {
	return "RewardSummaryWidgetValidationError"
}

// Error satisfies the builtin error interface
func (e RewardSummaryWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardSummaryWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardSummaryWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardSummaryWidgetValidationError{}

// Validate checks the field values on RewardSummaryWidget_RewardSummaryRow
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RewardSummaryWidget_RewardSummaryRow) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardSummaryWidget_RewardSummaryRow
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RewardSummaryWidget_RewardSummaryRowMultiError, or nil if none found.
func (m *RewardSummaryWidget_RewardSummaryRow) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardSummaryWidget_RewardSummaryRow) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidget_RewardSummaryRowValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidget_RewardSummaryRowValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEarnedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "EarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "EarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEarnedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidget_RewardSummaryRowValidationError{
				field:  "EarnedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidget_RewardSummaryRowValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardSummaryWidget_RewardSummaryRowValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardSummaryWidget_RewardSummaryRowValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardSummaryWidget_RewardSummaryRowMultiError(errors)
	}

	return nil
}

// RewardSummaryWidget_RewardSummaryRowMultiError is an error wrapping multiple
// validation errors returned by
// RewardSummaryWidget_RewardSummaryRow.ValidateAll() if the designated
// constraints aren't met.
type RewardSummaryWidget_RewardSummaryRowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardSummaryWidget_RewardSummaryRowMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardSummaryWidget_RewardSummaryRowMultiError) AllErrors() []error { return m }

// RewardSummaryWidget_RewardSummaryRowValidationError is the validation error
// returned by RewardSummaryWidget_RewardSummaryRow.Validate if the designated
// constraints aren't met.
type RewardSummaryWidget_RewardSummaryRowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardSummaryWidget_RewardSummaryRowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardSummaryWidget_RewardSummaryRowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardSummaryWidget_RewardSummaryRowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardSummaryWidget_RewardSummaryRowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardSummaryWidget_RewardSummaryRowValidationError) ErrorName() string {
	return "RewardSummaryWidget_RewardSummaryRowValidationError"
}

// Error satisfies the builtin error interface
func (e RewardSummaryWidget_RewardSummaryRowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardSummaryWidget_RewardSummaryRow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardSummaryWidget_RewardSummaryRowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardSummaryWidget_RewardSummaryRowValidationError{}
