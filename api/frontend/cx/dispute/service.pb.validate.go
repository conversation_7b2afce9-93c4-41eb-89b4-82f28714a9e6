// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/cx/dispute/service.proto

package dispute

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetFirstQuestionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFirstQuestionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFirstQuestionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFirstQuestionRequestMultiError, or nil if none found.
func (m *GetFirstQuestionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFirstQuestionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstQuestionRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstQuestionRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstQuestionRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstQuestionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstQuestionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstQuestionRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalTransactionId

	// no validation rules for DisputeType

	if len(errors) > 0 {
		return GetFirstQuestionRequestMultiError(errors)
	}

	return nil
}

// GetFirstQuestionRequestMultiError is an error wrapping multiple validation
// errors returned by GetFirstQuestionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFirstQuestionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFirstQuestionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFirstQuestionRequestMultiError) AllErrors() []error { return m }

// GetFirstQuestionRequestValidationError is the validation error returned by
// GetFirstQuestionRequest.Validate if the designated constraints aren't met.
type GetFirstQuestionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFirstQuestionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFirstQuestionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFirstQuestionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFirstQuestionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFirstQuestionRequestValidationError) ErrorName() string {
	return "GetFirstQuestionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFirstQuestionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFirstQuestionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFirstQuestionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFirstQuestionRequestValidationError{}

// Validate checks the field values on GetFirstQuestionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFirstQuestionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFirstQuestionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFirstQuestionResponseMultiError, or nil if none found.
func (m *GetFirstQuestionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFirstQuestionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstQuestionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstQuestionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstQuestionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFirstQuestionResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFirstQuestionResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFirstQuestionResponseValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFirstQuestionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFirstQuestionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFirstQuestionResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QuestionnaireScreenTitle

	// no validation rules for PreviewScreenTitle

	if len(errors) > 0 {
		return GetFirstQuestionResponseMultiError(errors)
	}

	return nil
}

// GetFirstQuestionResponseMultiError is an error wrapping multiple validation
// errors returned by GetFirstQuestionResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFirstQuestionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFirstQuestionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFirstQuestionResponseMultiError) AllErrors() []error { return m }

// GetFirstQuestionResponseValidationError is the validation error returned by
// GetFirstQuestionResponse.Validate if the designated constraints aren't met.
type GetFirstQuestionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFirstQuestionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFirstQuestionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFirstQuestionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFirstQuestionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFirstQuestionResponseValidationError) ErrorName() string {
	return "GetFirstQuestionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFirstQuestionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFirstQuestionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFirstQuestionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFirstQuestionResponseValidationError{}

// Validate checks the field values on GetNextQuestionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNextQuestionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextQuestionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNextQuestionsRequestMultiError, or nil if none found.
func (m *GetNextQuestionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextQuestionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextQuestionsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextQuestionsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextQuestionsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextQuestionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextQuestionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextQuestionsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QuestionId

	// no validation rules for Answer

	if len(errors) > 0 {
		return GetNextQuestionsRequestMultiError(errors)
	}

	return nil
}

// GetNextQuestionsRequestMultiError is an error wrapping multiple validation
// errors returned by GetNextQuestionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNextQuestionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextQuestionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextQuestionsRequestMultiError) AllErrors() []error { return m }

// GetNextQuestionsRequestValidationError is the validation error returned by
// GetNextQuestionsRequest.Validate if the designated constraints aren't met.
type GetNextQuestionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextQuestionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextQuestionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextQuestionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextQuestionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextQuestionsRequestValidationError) ErrorName() string {
	return "GetNextQuestionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextQuestionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextQuestionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextQuestionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextQuestionsRequestValidationError{}

// Validate checks the field values on GetNextQuestionsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNextQuestionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextQuestionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNextQuestionsResponseMultiError, or nil if none found.
func (m *GetNextQuestionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextQuestionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextQuestionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextQuestionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextQuestionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNextQuestionsResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNextQuestionsResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNextQuestionsResponseValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextQuestionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextQuestionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextQuestionsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNextQuestionsResponseMultiError(errors)
	}

	return nil
}

// GetNextQuestionsResponseMultiError is an error wrapping multiple validation
// errors returned by GetNextQuestionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNextQuestionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextQuestionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextQuestionsResponseMultiError) AllErrors() []error { return m }

// GetNextQuestionsResponseValidationError is the validation error returned by
// GetNextQuestionsResponse.Validate if the designated constraints aren't met.
type GetNextQuestionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextQuestionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextQuestionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextQuestionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextQuestionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextQuestionsResponseValidationError) ErrorName() string {
	return "GetNextQuestionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextQuestionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextQuestionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextQuestionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextQuestionsResponseValidationError{}

// Validate checks the field values on QuestionMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QuestionMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QuestionMetaMultiError, or
// nil if none found.
func (m *QuestionMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Channel

	// no validation rules for QuestionCode

	// no validation rules for ActualQuestion

	// no validation rules for AnswerDataType

	// no validation rules for IsOptional

	// no validation rules for PlaceholderText

	if all {
		switch v := interface{}(m.GetTextValidation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuestionMetaValidationError{
					field:  "TextValidation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuestionMetaValidationError{
					field:  "TextValidation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTextValidation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionMetaValidationError{
				field:  "TextValidation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QuestionMetaMultiError(errors)
	}

	return nil
}

// QuestionMetaMultiError is an error wrapping multiple validation errors
// returned by QuestionMeta.ValidateAll() if the designated constraints aren't met.
type QuestionMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionMetaMultiError) AllErrors() []error { return m }

// QuestionMetaValidationError is the validation error returned by
// QuestionMeta.Validate if the designated constraints aren't met.
type QuestionMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionMetaValidationError) ErrorName() string { return "QuestionMetaValidationError" }

// Error satisfies the builtin error interface
func (e QuestionMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionMetaValidationError{}

// Validate checks the field values on QuestionnaireResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionnaireResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionnaireResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionnaireResultMultiError, or nil if none found.
func (m *QuestionnaireResult) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionnaireResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResultList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionnaireResultValidationError{
						field:  fmt.Sprintf("ResultList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionnaireResultValidationError{
						field:  fmt.Sprintf("ResultList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionnaireResultValidationError{
					field:  fmt.Sprintf("ResultList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QuestionnaireResultMultiError(errors)
	}

	return nil
}

// QuestionnaireResultMultiError is an error wrapping multiple validation
// errors returned by QuestionnaireResult.ValidateAll() if the designated
// constraints aren't met.
type QuestionnaireResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionnaireResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionnaireResultMultiError) AllErrors() []error { return m }

// QuestionnaireResultValidationError is the validation error returned by
// QuestionnaireResult.Validate if the designated constraints aren't met.
type QuestionnaireResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionnaireResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionnaireResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionnaireResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionnaireResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionnaireResultValidationError) ErrorName() string {
	return "QuestionnaireResultValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionnaireResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionnaireResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionnaireResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionnaireResultValidationError{}

// Validate checks the field values on RaiseDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseDisputeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseDisputeRequestMultiError, or nil if none found.
func (m *RaiseDisputeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseDisputeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisputeType

	if all {
		switch v := interface{}(m.GetQuestionResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "QuestionResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeRequestValidationError{
					field:  "QuestionResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestionResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeRequestValidationError{
				field:  "QuestionResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalTransactionId

	if len(errors) > 0 {
		return RaiseDisputeRequestMultiError(errors)
	}

	return nil
}

// RaiseDisputeRequestMultiError is an error wrapping multiple validation
// errors returned by RaiseDisputeRequest.ValidateAll() if the designated
// constraints aren't met.
type RaiseDisputeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseDisputeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseDisputeRequestMultiError) AllErrors() []error { return m }

// RaiseDisputeRequestValidationError is the validation error returned by
// RaiseDisputeRequest.Validate if the designated constraints aren't met.
type RaiseDisputeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseDisputeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseDisputeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseDisputeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseDisputeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseDisputeRequestValidationError) ErrorName() string {
	return "RaiseDisputeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseDisputeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseDisputeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseDisputeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseDisputeRequestValidationError{}

// Validate checks the field values on RaiseDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseDisputeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseDisputeResponseMultiError, or nil if none found.
func (m *RaiseDisputeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseDisputeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisputeId

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseDisputeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseDisputeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RaiseDisputeResponseMultiError(errors)
	}

	return nil
}

// RaiseDisputeResponseMultiError is an error wrapping multiple validation
// errors returned by RaiseDisputeResponse.ValidateAll() if the designated
// constraints aren't met.
type RaiseDisputeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseDisputeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseDisputeResponseMultiError) AllErrors() []error { return m }

// RaiseDisputeResponseValidationError is the validation error returned by
// RaiseDisputeResponse.Validate if the designated constraints aren't met.
type RaiseDisputeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseDisputeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseDisputeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseDisputeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseDisputeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseDisputeResponseValidationError) ErrorName() string {
	return "RaiseDisputeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseDisputeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseDisputeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseDisputeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseDisputeResponseValidationError{}

// Validate checks the field values on GetDisputeBottomSheetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDisputeBottomSheetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDisputeBottomSheetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDisputeBottomSheetRequestMultiError, or nil if none found.
func (m *GetDisputeBottomSheetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeBottomSheetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeBottomSheetRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeBottomSheetRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeBottomSheetRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeBottomSheetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeBottomSheetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeBottomSheetRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalTransactionId

	// no validation rules for DisputeType

	if len(errors) > 0 {
		return GetDisputeBottomSheetRequestMultiError(errors)
	}

	return nil
}

// GetDisputeBottomSheetRequestMultiError is an error wrapping multiple
// validation errors returned by GetDisputeBottomSheetRequest.ValidateAll() if
// the designated constraints aren't met.
type GetDisputeBottomSheetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeBottomSheetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeBottomSheetRequestMultiError) AllErrors() []error { return m }

// GetDisputeBottomSheetRequestValidationError is the validation error returned
// by GetDisputeBottomSheetRequest.Validate if the designated constraints
// aren't met.
type GetDisputeBottomSheetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeBottomSheetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeBottomSheetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeBottomSheetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeBottomSheetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeBottomSheetRequestValidationError) ErrorName() string {
	return "GetDisputeBottomSheetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeBottomSheetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeBottomSheetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeBottomSheetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeBottomSheetRequestValidationError{}

// Validate checks the field values on GetDisputeBottomSheetRespone with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDisputeBottomSheetRespone) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDisputeBottomSheetRespone with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDisputeBottomSheetResponeMultiError, or nil if none found.
func (m *GetDisputeBottomSheetRespone) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeBottomSheetRespone) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeBottomSheetResponeValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeBottomSheetResponeValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeBottomSheetResponeValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisputeBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeBottomSheetResponeValidationError{
					field:  "DisputeBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeBottomSheetResponeValidationError{
					field:  "DisputeBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputeBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeBottomSheetResponeValidationError{
				field:  "DisputeBottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDisputeBottomSheetResponeMultiError(errors)
	}

	return nil
}

// GetDisputeBottomSheetResponeMultiError is an error wrapping multiple
// validation errors returned by GetDisputeBottomSheetRespone.ValidateAll() if
// the designated constraints aren't met.
type GetDisputeBottomSheetResponeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeBottomSheetResponeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeBottomSheetResponeMultiError) AllErrors() []error { return m }

// GetDisputeBottomSheetResponeValidationError is the validation error returned
// by GetDisputeBottomSheetRespone.Validate if the designated constraints
// aren't met.
type GetDisputeBottomSheetResponeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeBottomSheetResponeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeBottomSheetResponeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeBottomSheetResponeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeBottomSheetResponeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeBottomSheetResponeValidationError) ErrorName() string {
	return "GetDisputeBottomSheetResponeValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeBottomSheetResponeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeBottomSheetRespone.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeBottomSheetResponeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeBottomSheetResponeValidationError{}

// Validate checks the field values on BottomSheet with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BottomSheet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BottomSheet with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BottomSheetMultiError, or
// nil if none found.
func (m *BottomSheet) ValidateAll() error {
	return m.validate(true)
}

func (m *BottomSheet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Description

	for idx, item := range m.GetActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BottomSheetValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BottomSheetValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BottomSheetValidationError{
					field:  fmt.Sprintf("Actions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BottomSheetMultiError(errors)
	}

	return nil
}

// BottomSheetMultiError is an error wrapping multiple validation errors
// returned by BottomSheet.ValidateAll() if the designated constraints aren't met.
type BottomSheetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BottomSheetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BottomSheetMultiError) AllErrors() []error { return m }

// BottomSheetValidationError is the validation error returned by
// BottomSheet.Validate if the designated constraints aren't met.
type BottomSheetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BottomSheetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BottomSheetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BottomSheetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BottomSheetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BottomSheetValidationError) ErrorName() string { return "BottomSheetValidationError" }

// Error satisfies the builtin error interface
func (e BottomSheetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBottomSheet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BottomSheetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BottomSheetValidationError{}

// Validate checks the field values on BottomSheetActions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BottomSheetActions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BottomSheetActions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BottomSheetActionsMultiError, or nil if none found.
func (m *BottomSheetActions) ValidateAll() error {
	return m.validate(true)
}

func (m *BottomSheetActions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	// no validation rules for ActionPrecedence

	// no validation rules for DisplayValue

	if len(errors) > 0 {
		return BottomSheetActionsMultiError(errors)
	}

	return nil
}

// BottomSheetActionsMultiError is an error wrapping multiple validation errors
// returned by BottomSheetActions.ValidateAll() if the designated constraints
// aren't met.
type BottomSheetActionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BottomSheetActionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BottomSheetActionsMultiError) AllErrors() []error { return m }

// BottomSheetActionsValidationError is the validation error returned by
// BottomSheetActions.Validate if the designated constraints aren't met.
type BottomSheetActionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BottomSheetActionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BottomSheetActionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BottomSheetActionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BottomSheetActionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BottomSheetActionsValidationError) ErrorName() string {
	return "BottomSheetActionsValidationError"
}

// Error satisfies the builtin error interface
func (e BottomSheetActionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBottomSheetActions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BottomSheetActionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BottomSheetActionsValidationError{}

// Validate checks the field values on GetDisputeDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDisputeDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDisputeDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDisputeDetailsRequestMultiError, or nil if none found.
func (m *GetDisputeDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternalTransactionId

	// no validation rules for RefreshFromSource

	if len(errors) > 0 {
		return GetDisputeDetailsRequestMultiError(errors)
	}

	return nil
}

// GetDisputeDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetDisputeDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDisputeDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeDetailsRequestMultiError) AllErrors() []error { return m }

// GetDisputeDetailsRequestValidationError is the validation error returned by
// GetDisputeDetailsRequest.Validate if the designated constraints aren't met.
type GetDisputeDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeDetailsRequestValidationError) ErrorName() string {
	return "GetDisputeDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeDetailsRequestValidationError{}

// Validate checks the field values on GetDisputeDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDisputeDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDisputeDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDisputeDetailsResponseMultiError, or nil if none found.
func (m *GetDisputeDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisputeTitleText

	if all {
		switch v := interface{}(m.GetOverallStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "OverallStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "OverallStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOverallStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeDetailsResponseValidationError{
				field:  "OverallStage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDisputeStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDisputeDetailsResponseValidationError{
						field:  fmt.Sprintf("DisputeStages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDisputeDetailsResponseValidationError{
						field:  fmt.Sprintf("DisputeStages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDisputeDetailsResponseValidationError{
					field:  fmt.Sprintf("DisputeStages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDisputeCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "DisputeCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "DisputeCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputeCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeDetailsResponseValidationError{
				field:  "DisputeCreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisputeUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "DisputeUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeDetailsResponseValidationError{
					field:  "DisputeUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputeUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeDetailsResponseValidationError{
				field:  "DisputeUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowForceRefreshButton

	if len(errors) > 0 {
		return GetDisputeDetailsResponseMultiError(errors)
	}

	return nil
}

// GetDisputeDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetDisputeDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetDisputeDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeDetailsResponseMultiError) AllErrors() []error { return m }

// GetDisputeDetailsResponseValidationError is the validation error returned by
// GetDisputeDetailsResponse.Validate if the designated constraints aren't met.
type GetDisputeDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeDetailsResponseValidationError) ErrorName() string {
	return "GetDisputeDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeDetailsResponseValidationError{}

// Validate checks the field values on QuestionMeta_TextValidation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionMeta_TextValidation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionMeta_TextValidation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionMeta_TextValidationMultiError, or nil if none found.
func (m *QuestionMeta_TextValidation) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionMeta_TextValidation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinLength

	// no validation rules for MaxLength

	if len(errors) > 0 {
		return QuestionMeta_TextValidationMultiError(errors)
	}

	return nil
}

// QuestionMeta_TextValidationMultiError is an error wrapping multiple
// validation errors returned by QuestionMeta_TextValidation.ValidateAll() if
// the designated constraints aren't met.
type QuestionMeta_TextValidationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionMeta_TextValidationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionMeta_TextValidationMultiError) AllErrors() []error { return m }

// QuestionMeta_TextValidationValidationError is the validation error returned
// by QuestionMeta_TextValidation.Validate if the designated constraints
// aren't met.
type QuestionMeta_TextValidationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionMeta_TextValidationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionMeta_TextValidationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionMeta_TextValidationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionMeta_TextValidationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionMeta_TextValidationValidationError) ErrorName() string {
	return "QuestionMeta_TextValidationValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionMeta_TextValidationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionMeta_TextValidation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionMeta_TextValidationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionMeta_TextValidationValidationError{}

// Validate checks the field values on QuestionnaireResultResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionnaireResultResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionnaireResultResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionnaireResultResultMultiError, or nil if none found.
func (m *QuestionnaireResultResult) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionnaireResultResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QuestionCode

	// no validation rules for Question

	// no validation rules for Answer

	if len(errors) > 0 {
		return QuestionnaireResultResultMultiError(errors)
	}

	return nil
}

// QuestionnaireResultResultMultiError is an error wrapping multiple validation
// errors returned by QuestionnaireResultResult.ValidateAll() if the
// designated constraints aren't met.
type QuestionnaireResultResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionnaireResultResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionnaireResultResultMultiError) AllErrors() []error { return m }

// QuestionnaireResultResultValidationError is the validation error returned by
// QuestionnaireResultResult.Validate if the designated constraints aren't met.
type QuestionnaireResultResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionnaireResultResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionnaireResultResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionnaireResultResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionnaireResultResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionnaireResultResultValidationError) ErrorName() string {
	return "QuestionnaireResultResultValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionnaireResultResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionnaireResultResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionnaireResultResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionnaireResultResultValidationError{}

// Validate checks the field values on GetDisputeDetailsResponse_DisputeStage
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetDisputeDetailsResponse_DisputeStage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDisputeDetailsResponse_DisputeStage with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetDisputeDetailsResponse_DisputeStageMultiError, or nil if none found.
func (m *GetDisputeDetailsResponse_DisputeStage) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeDetailsResponse_DisputeStage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for LocalizedDescription

	if all {
		switch v := interface{}(m.GetExecutionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeDetailsResponse_DisputeStageValidationError{
					field:  "ExecutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeDetailsResponse_DisputeStageValidationError{
					field:  "ExecutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeDetailsResponse_DisputeStageValidationError{
				field:  "ExecutionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DetailedDescription

	if len(errors) > 0 {
		return GetDisputeDetailsResponse_DisputeStageMultiError(errors)
	}

	return nil
}

// GetDisputeDetailsResponse_DisputeStageMultiError is an error wrapping
// multiple validation errors returned by
// GetDisputeDetailsResponse_DisputeStage.ValidateAll() if the designated
// constraints aren't met.
type GetDisputeDetailsResponse_DisputeStageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeDetailsResponse_DisputeStageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeDetailsResponse_DisputeStageMultiError) AllErrors() []error { return m }

// GetDisputeDetailsResponse_DisputeStageValidationError is the validation
// error returned by GetDisputeDetailsResponse_DisputeStage.Validate if the
// designated constraints aren't met.
type GetDisputeDetailsResponse_DisputeStageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeDetailsResponse_DisputeStageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeDetailsResponse_DisputeStageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeDetailsResponse_DisputeStageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeDetailsResponse_DisputeStageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeDetailsResponse_DisputeStageValidationError) ErrorName() string {
	return "GetDisputeDetailsResponse_DisputeStageValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeDetailsResponse_DisputeStageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeDetailsResponse_DisputeStage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeDetailsResponse_DisputeStageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeDetailsResponse_DisputeStageValidationError{}
