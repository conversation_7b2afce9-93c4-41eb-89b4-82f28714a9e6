// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/tiering/service.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/tiering/enums"

	external "github.com/epifi/gamma/api/tiering/external"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CriteriaOptionType(0)

	_ = external.Tier(0)
)

// Validate checks the field values on IsUserEligibleForRewardsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsUserEligibleForRewardsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsUserEligibleForRewardsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IsUserEligibleForRewardsRequestMultiError, or nil if none found.
func (m *IsUserEligibleForRewardsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserEligibleForRewardsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsUserEligibleForRewardsRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsUserEligibleForRewardsRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsUserEligibleForRewardsRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IsUserEligibleForRewardsRequestMultiError(errors)
	}

	return nil
}

// IsUserEligibleForRewardsRequestMultiError is an error wrapping multiple
// validation errors returned by IsUserEligibleForRewardsRequest.ValidateAll()
// if the designated constraints aren't met.
type IsUserEligibleForRewardsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserEligibleForRewardsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserEligibleForRewardsRequestMultiError) AllErrors() []error { return m }

// IsUserEligibleForRewardsRequestValidationError is the validation error
// returned by IsUserEligibleForRewardsRequest.Validate if the designated
// constraints aren't met.
type IsUserEligibleForRewardsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserEligibleForRewardsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsUserEligibleForRewardsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsUserEligibleForRewardsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsUserEligibleForRewardsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsUserEligibleForRewardsRequestValidationError) ErrorName() string {
	return "IsUserEligibleForRewardsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserEligibleForRewardsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserEligibleForRewardsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserEligibleForRewardsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserEligibleForRewardsRequestValidationError{}

// Validate checks the field values on IsUserEligibleForRewardsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IsUserEligibleForRewardsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsUserEligibleForRewardsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IsUserEligibleForRewardsResponseMultiError, or nil if none found.
func (m *IsUserEligibleForRewardsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserEligibleForRewardsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsUserEligibleForRewardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsUserEligibleForRewardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsUserEligibleForRewardsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTierToEligibilityStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IsUserEligibleForRewardsResponseValidationError{
						field:  fmt.Sprintf("TierToEligibilityStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IsUserEligibleForRewardsResponseValidationError{
						field:  fmt.Sprintf("TierToEligibilityStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IsUserEligibleForRewardsResponseValidationError{
					field:  fmt.Sprintf("TierToEligibilityStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return IsUserEligibleForRewardsResponseMultiError(errors)
	}

	return nil
}

// IsUserEligibleForRewardsResponseMultiError is an error wrapping multiple
// validation errors returned by
// IsUserEligibleForRewardsResponse.ValidateAll() if the designated
// constraints aren't met.
type IsUserEligibleForRewardsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserEligibleForRewardsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserEligibleForRewardsResponseMultiError) AllErrors() []error { return m }

// IsUserEligibleForRewardsResponseValidationError is the validation error
// returned by IsUserEligibleForRewardsResponse.Validate if the designated
// constraints aren't met.
type IsUserEligibleForRewardsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserEligibleForRewardsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsUserEligibleForRewardsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsUserEligibleForRewardsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsUserEligibleForRewardsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsUserEligibleForRewardsResponseValidationError) ErrorName() string {
	return "IsUserEligibleForRewardsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserEligibleForRewardsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserEligibleForRewardsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserEligibleForRewardsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserEligibleForRewardsResponseValidationError{}

// Validate checks the field values on GetActorDistinctTiersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorDistinctTiersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorDistinctTiersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorDistinctTiersRequestMultiError, or nil if none found.
func (m *GetActorDistinctTiersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorDistinctTiersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetActorDistinctTiersRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTimeSince()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorDistinctTiersRequestValidationError{
					field:  "TimeSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorDistinctTiersRequestValidationError{
					field:  "TimeSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeSince()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorDistinctTiersRequestValidationError{
				field:  "TimeSince",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActorDistinctTiersRequestMultiError(errors)
	}

	return nil
}

// GetActorDistinctTiersRequestMultiError is an error wrapping multiple
// validation errors returned by GetActorDistinctTiersRequest.ValidateAll() if
// the designated constraints aren't met.
type GetActorDistinctTiersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorDistinctTiersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorDistinctTiersRequestMultiError) AllErrors() []error { return m }

// GetActorDistinctTiersRequestValidationError is the validation error returned
// by GetActorDistinctTiersRequest.Validate if the designated constraints
// aren't met.
type GetActorDistinctTiersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorDistinctTiersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorDistinctTiersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorDistinctTiersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorDistinctTiersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorDistinctTiersRequestValidationError) ErrorName() string {
	return "GetActorDistinctTiersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorDistinctTiersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorDistinctTiersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorDistinctTiersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorDistinctTiersRequestValidationError{}

// Validate checks the field values on GetActorDistinctTiersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorDistinctTiersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorDistinctTiersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActorDistinctTiersResponseMultiError, or nil if none found.
func (m *GetActorDistinctTiersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorDistinctTiersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorDistinctTiersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorDistinctTiersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorDistinctTiersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActorDistinctTiersResponseMultiError(errors)
	}

	return nil
}

// GetActorDistinctTiersResponseMultiError is an error wrapping multiple
// validation errors returned by GetActorDistinctTiersResponse.ValidateAll()
// if the designated constraints aren't met.
type GetActorDistinctTiersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorDistinctTiersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorDistinctTiersResponseMultiError) AllErrors() []error { return m }

// GetActorDistinctTiersResponseValidationError is the validation error
// returned by GetActorDistinctTiersResponse.Validate if the designated
// constraints aren't met.
type GetActorDistinctTiersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorDistinctTiersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorDistinctTiersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorDistinctTiersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorDistinctTiersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorDistinctTiersResponseValidationError) ErrorName() string {
	return "GetActorDistinctTiersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorDistinctTiersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorDistinctTiersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorDistinctTiersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorDistinctTiersResponseValidationError{}

// Validate checks the field values on GetTieringPitchV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTieringPitchV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTieringPitchV2Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTieringPitchV2ResponseMultiError, or nil if none found.
func (m *GetTieringPitchV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTieringPitchV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringPitchV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringPitchV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringPitchV2ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentTier

	for idx, item := range m.GetMovementDetailsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTieringPitchV2ResponseValidationError{
						field:  fmt.Sprintf("MovementDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTieringPitchV2ResponseValidationError{
						field:  fmt.Sprintf("MovementDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTieringPitchV2ResponseValidationError{
					field:  fmt.Sprintf("MovementDetailsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetLastUpgradeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringPitchV2ResponseValidationError{
					field:  "LastUpgradeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringPitchV2ResponseValidationError{
					field:  "LastUpgradeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpgradeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringPitchV2ResponseValidationError{
				field:  "LastUpgradeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastDowngradeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringPitchV2ResponseValidationError{
					field:  "LastDowngradeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringPitchV2ResponseValidationError{
					field:  "LastDowngradeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastDowngradeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringPitchV2ResponseValidationError{
				field:  "LastDowngradeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorBaseTier

	// no validation rules for EntryCriteriaOptionType

	// no validation rules for CurrentCriteriaOptionType

	if len(errors) > 0 {
		return GetTieringPitchV2ResponseMultiError(errors)
	}

	return nil
}

// GetTieringPitchV2ResponseMultiError is an error wrapping multiple validation
// errors returned by GetTieringPitchV2Response.ValidateAll() if the
// designated constraints aren't met.
type GetTieringPitchV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTieringPitchV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTieringPitchV2ResponseMultiError) AllErrors() []error { return m }

// GetTieringPitchV2ResponseValidationError is the validation error returned by
// GetTieringPitchV2Response.Validate if the designated constraints aren't met.
type GetTieringPitchV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTieringPitchV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTieringPitchV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTieringPitchV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTieringPitchV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTieringPitchV2ResponseValidationError) ErrorName() string {
	return "GetTieringPitchV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTieringPitchV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTieringPitchV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTieringPitchV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTieringPitchV2ResponseValidationError{}

// Validate checks the field values on GetTieringPitchV2Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTieringPitchV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTieringPitchV2Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTieringPitchV2RequestMultiError, or nil if none found.
func (m *GetTieringPitchV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTieringPitchV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTieringPitchV2RequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTieringPitchV2RequestMultiError(errors)
	}

	return nil
}

// GetTieringPitchV2RequestMultiError is an error wrapping multiple validation
// errors returned by GetTieringPitchV2Request.ValidateAll() if the designated
// constraints aren't met.
type GetTieringPitchV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTieringPitchV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTieringPitchV2RequestMultiError) AllErrors() []error { return m }

// GetTieringPitchV2RequestValidationError is the validation error returned by
// GetTieringPitchV2Request.Validate if the designated constraints aren't met.
type GetTieringPitchV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTieringPitchV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTieringPitchV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTieringPitchV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTieringPitchV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTieringPitchV2RequestValidationError) ErrorName() string {
	return "GetTieringPitchV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTieringPitchV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTieringPitchV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTieringPitchV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTieringPitchV2RequestValidationError{}

// Validate checks the field values on RecordComponentShownToActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordComponentShownToActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordComponentShownToActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordComponentShownToActorRequestMultiError, or nil if none found.
func (m *RecordComponentShownToActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordComponentShownToActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := RecordComponentShownToActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Component

	if len(errors) > 0 {
		return RecordComponentShownToActorRequestMultiError(errors)
	}

	return nil
}

// RecordComponentShownToActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecordComponentShownToActorRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordComponentShownToActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordComponentShownToActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordComponentShownToActorRequestMultiError) AllErrors() []error { return m }

// RecordComponentShownToActorRequestValidationError is the validation error
// returned by RecordComponentShownToActorRequest.Validate if the designated
// constraints aren't met.
type RecordComponentShownToActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordComponentShownToActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordComponentShownToActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordComponentShownToActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordComponentShownToActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordComponentShownToActorRequestValidationError) ErrorName() string {
	return "RecordComponentShownToActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordComponentShownToActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordComponentShownToActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordComponentShownToActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordComponentShownToActorRequestValidationError{}

// Validate checks the field values on RecordComponentShownToActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordComponentShownToActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordComponentShownToActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordComponentShownToActorResponseMultiError, or nil if none found.
func (m *RecordComponentShownToActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordComponentShownToActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordComponentShownToActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordComponentShownToActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordComponentShownToActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordComponentShownToActorResponseMultiError(errors)
	}

	return nil
}

// RecordComponentShownToActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordComponentShownToActorResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordComponentShownToActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordComponentShownToActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordComponentShownToActorResponseMultiError) AllErrors() []error { return m }

// RecordComponentShownToActorResponseValidationError is the validation error
// returned by RecordComponentShownToActorResponse.Validate if the designated
// constraints aren't met.
type RecordComponentShownToActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordComponentShownToActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordComponentShownToActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordComponentShownToActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordComponentShownToActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordComponentShownToActorResponseValidationError) ErrorName() string {
	return "RecordComponentShownToActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordComponentShownToActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordComponentShownToActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordComponentShownToActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordComponentShownToActorResponseValidationError{}

// Validate checks the field values on GetTieringPitchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTieringPitchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTieringPitchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTieringPitchRequestMultiError, or nil if none found.
func (m *GetTieringPitchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTieringPitchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTieringPitchRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TieringPitchFlow

	if len(errors) > 0 {
		return GetTieringPitchRequestMultiError(errors)
	}

	return nil
}

// GetTieringPitchRequestMultiError is an error wrapping multiple validation
// errors returned by GetTieringPitchRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTieringPitchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTieringPitchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTieringPitchRequestMultiError) AllErrors() []error { return m }

// GetTieringPitchRequestValidationError is the validation error returned by
// GetTieringPitchRequest.Validate if the designated constraints aren't met.
type GetTieringPitchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTieringPitchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTieringPitchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTieringPitchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTieringPitchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTieringPitchRequestValidationError) ErrorName() string {
	return "GetTieringPitchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTieringPitchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTieringPitchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTieringPitchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTieringPitchRequestValidationError{}

// Validate checks the field values on GetTieringPitchResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTieringPitchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTieringPitchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTieringPitchResponseMultiError, or nil if none found.
func (m *GetTieringPitchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTieringPitchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringPitchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringPitchResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringPitchResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsPitchEnabled

	switch v := m.TieringPitchDetails.(type) {
	case *GetTieringPitchResponse_AddFundsDetails:
		if v == nil {
			err := GetTieringPitchResponseValidationError{
				field:  "TieringPitchDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAddFundsDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTieringPitchResponseValidationError{
						field:  "AddFundsDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTieringPitchResponseValidationError{
						field:  "AddFundsDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAddFundsDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTieringPitchResponseValidationError{
					field:  "AddFundsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTieringPitchResponse_ProfileSectionDetails:
		if v == nil {
			err := GetTieringPitchResponseValidationError{
				field:  "TieringPitchDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProfileSectionDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTieringPitchResponseValidationError{
						field:  "ProfileSectionDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTieringPitchResponseValidationError{
						field:  "ProfileSectionDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProfileSectionDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTieringPitchResponseValidationError{
					field:  "ProfileSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTieringPitchResponseMultiError(errors)
	}

	return nil
}

// GetTieringPitchResponseMultiError is an error wrapping multiple validation
// errors returned by GetTieringPitchResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTieringPitchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTieringPitchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTieringPitchResponseMultiError) AllErrors() []error { return m }

// GetTieringPitchResponseValidationError is the validation error returned by
// GetTieringPitchResponse.Validate if the designated constraints aren't met.
type GetTieringPitchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTieringPitchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTieringPitchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTieringPitchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTieringPitchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTieringPitchResponseValidationError) ErrorName() string {
	return "GetTieringPitchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTieringPitchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTieringPitchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTieringPitchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTieringPitchResponseValidationError{}

// Validate checks the field values on TieringPitchAddFundsDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringPitchAddFundsDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringPitchAddFundsDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringPitchAddFundsDetailsMultiError, or nil if none found.
func (m *TieringPitchAddFundsDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringPitchAddFundsDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentTier

	if all {
		switch v := interface{}(m.GetCurrentTierMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "CurrentTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "CurrentTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentTierMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchAddFundsDetailsValidationError{
				field:  "CurrentTierMinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextTier

	if all {
		switch v := interface{}(m.GetNextTierMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "NextTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "NextTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextTierMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchAddFundsDetailsValidationError{
				field:  "NextTierMinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentBalanceAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "CurrentBalanceAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "CurrentBalanceAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentBalanceAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchAddFundsDetailsValidationError{
				field:  "CurrentBalanceAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchAddFundsDetailsValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSuggestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "SuggestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchAddFundsDetailsValidationError{
					field:  "SuggestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSuggestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchAddFundsDetailsValidationError{
				field:  "SuggestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRetentionPitch

	if len(errors) > 0 {
		return TieringPitchAddFundsDetailsMultiError(errors)
	}

	return nil
}

// TieringPitchAddFundsDetailsMultiError is an error wrapping multiple
// validation errors returned by TieringPitchAddFundsDetails.ValidateAll() if
// the designated constraints aren't met.
type TieringPitchAddFundsDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringPitchAddFundsDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringPitchAddFundsDetailsMultiError) AllErrors() []error { return m }

// TieringPitchAddFundsDetailsValidationError is the validation error returned
// by TieringPitchAddFundsDetails.Validate if the designated constraints
// aren't met.
type TieringPitchAddFundsDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringPitchAddFundsDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringPitchAddFundsDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringPitchAddFundsDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringPitchAddFundsDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringPitchAddFundsDetailsValidationError) ErrorName() string {
	return "TieringPitchAddFundsDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e TieringPitchAddFundsDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringPitchAddFundsDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringPitchAddFundsDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringPitchAddFundsDetailsValidationError{}

// Validate checks the field values on TieringPitchProfileSectionDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TieringPitchProfileSectionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringPitchProfileSectionDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TieringPitchProfileSectionDetailsMultiError, or nil if none found.
func (m *TieringPitchProfileSectionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringPitchProfileSectionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentTier

	if all {
		switch v := interface{}(m.GetCurrentTierMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "CurrentTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "CurrentTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentTierMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchProfileSectionDetailsValidationError{
				field:  "CurrentTierMinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextTier

	if all {
		switch v := interface{}(m.GetNextTierMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "NextTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "NextTierMinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextTierMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchProfileSectionDetailsValidationError{
				field:  "NextTierMinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentBalanceAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "CurrentBalanceAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "CurrentBalanceAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentBalanceAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchProfileSectionDetailsValidationError{
				field:  "CurrentBalanceAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsUserInGracePeriod

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchProfileSectionDetailsValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGracePeriodExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "GracePeriodExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TieringPitchProfileSectionDetailsValidationError{
					field:  "GracePeriodExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGracePeriodExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TieringPitchProfileSectionDetailsValidationError{
				field:  "GracePeriodExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TieringPitchProfileSectionDetailsMultiError(errors)
	}

	return nil
}

// TieringPitchProfileSectionDetailsMultiError is an error wrapping multiple
// validation errors returned by
// TieringPitchProfileSectionDetails.ValidateAll() if the designated
// constraints aren't met.
type TieringPitchProfileSectionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringPitchProfileSectionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringPitchProfileSectionDetailsMultiError) AllErrors() []error { return m }

// TieringPitchProfileSectionDetailsValidationError is the validation error
// returned by TieringPitchProfileSectionDetails.Validate if the designated
// constraints aren't met.
type TieringPitchProfileSectionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringPitchProfileSectionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringPitchProfileSectionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringPitchProfileSectionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringPitchProfileSectionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringPitchProfileSectionDetailsValidationError) ErrorName() string {
	return "TieringPitchProfileSectionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e TieringPitchProfileSectionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringPitchProfileSectionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringPitchProfileSectionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringPitchProfileSectionDetailsValidationError{}

// Validate checks the field values on GetTierAtTimeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierAtTimeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierAtTimeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierAtTimeRequestMultiError, or nil if none found.
func (m *GetTierAtTimeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierAtTimeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTierAtTimeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTierTimestamp() == nil {
		err := GetTierAtTimeRequestValidationError{
			field:  "TierTimestamp",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTierAtTimeRequestMultiError(errors)
	}

	return nil
}

// GetTierAtTimeRequestMultiError is an error wrapping multiple validation
// errors returned by GetTierAtTimeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTierAtTimeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierAtTimeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierAtTimeRequestMultiError) AllErrors() []error { return m }

// GetTierAtTimeRequestValidationError is the validation error returned by
// GetTierAtTimeRequest.Validate if the designated constraints aren't met.
type GetTierAtTimeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierAtTimeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierAtTimeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierAtTimeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierAtTimeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierAtTimeRequestValidationError) ErrorName() string {
	return "GetTierAtTimeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierAtTimeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierAtTimeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierAtTimeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierAtTimeRequestValidationError{}

// Validate checks the field values on GetTierAtTimeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierAtTimeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierAtTimeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierAtTimeResponseMultiError, or nil if none found.
func (m *GetTierAtTimeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierAtTimeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAtTimeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAtTimeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAtTimeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTierInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAtTimeResponseValidationError{
					field:  "TierInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAtTimeResponseValidationError{
					field:  "TierInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTierInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAtTimeResponseValidationError{
				field:  "TierInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTierAtTimeResponseMultiError(errors)
	}

	return nil
}

// GetTierAtTimeResponseMultiError is an error wrapping multiple validation
// errors returned by GetTierAtTimeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTierAtTimeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierAtTimeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierAtTimeResponseMultiError) AllErrors() []error { return m }

// GetTierAtTimeResponseValidationError is the validation error returned by
// GetTierAtTimeResponse.Validate if the designated constraints aren't met.
type GetTierAtTimeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierAtTimeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierAtTimeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierAtTimeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierAtTimeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierAtTimeResponseValidationError) ErrorName() string {
	return "GetTierAtTimeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierAtTimeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierAtTimeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierAtTimeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierAtTimeResponseValidationError{}

// Validate checks the field values on IsTieringEnabledForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsTieringEnabledForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsTieringEnabledForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IsTieringEnabledForActorRequestMultiError, or nil if none found.
func (m *IsTieringEnabledForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsTieringEnabledForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := IsTieringEnabledForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IsTieringEnabledForActorRequestMultiError(errors)
	}

	return nil
}

// IsTieringEnabledForActorRequestMultiError is an error wrapping multiple
// validation errors returned by IsTieringEnabledForActorRequest.ValidateAll()
// if the designated constraints aren't met.
type IsTieringEnabledForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsTieringEnabledForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsTieringEnabledForActorRequestMultiError) AllErrors() []error { return m }

// IsTieringEnabledForActorRequestValidationError is the validation error
// returned by IsTieringEnabledForActorRequest.Validate if the designated
// constraints aren't met.
type IsTieringEnabledForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsTieringEnabledForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsTieringEnabledForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsTieringEnabledForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsTieringEnabledForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsTieringEnabledForActorRequestValidationError) ErrorName() string {
	return "IsTieringEnabledForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsTieringEnabledForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsTieringEnabledForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsTieringEnabledForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsTieringEnabledForActorRequestValidationError{}

// Validate checks the field values on IsTieringEnabledForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IsTieringEnabledForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsTieringEnabledForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IsTieringEnabledForActorResponseMultiError, or nil if none found.
func (m *IsTieringEnabledForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsTieringEnabledForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTieringEnabledForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTieringEnabledForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTieringEnabledForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEnabled

	if len(errors) > 0 {
		return IsTieringEnabledForActorResponseMultiError(errors)
	}

	return nil
}

// IsTieringEnabledForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// IsTieringEnabledForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type IsTieringEnabledForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsTieringEnabledForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsTieringEnabledForActorResponseMultiError) AllErrors() []error { return m }

// IsTieringEnabledForActorResponseValidationError is the validation error
// returned by IsTieringEnabledForActorResponse.Validate if the designated
// constraints aren't met.
type IsTieringEnabledForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsTieringEnabledForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsTieringEnabledForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsTieringEnabledForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsTieringEnabledForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsTieringEnabledForActorResponseValidationError) ErrorName() string {
	return "IsTieringEnabledForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsTieringEnabledForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsTieringEnabledForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsTieringEnabledForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsTieringEnabledForActorResponseValidationError{}

// Validate checks the field values on UpgradeRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpgradeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpgradeRequestMultiError,
// or nil if none found.
func (m *UpgradeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := UpgradeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UpgradeRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := UpgradeRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpgradeRequestMultiError(errors)
	}

	return nil
}

// UpgradeRequestMultiError is an error wrapping multiple validation errors
// returned by UpgradeRequest.ValidateAll() if the designated constraints
// aren't met.
type UpgradeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeRequestMultiError) AllErrors() []error { return m }

// UpgradeRequestValidationError is the validation error returned by
// UpgradeRequest.Validate if the designated constraints aren't met.
type UpgradeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeRequestValidationError) ErrorName() string { return "UpgradeRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpgradeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeRequestValidationError{}

var _UpgradeRequest_Provenance_NotInLookup = map[enums.Provenance]struct{}{
	0: {},
}

// Validate checks the field values on UpgradeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpgradeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeResponseMultiError, or nil if none found.
func (m *UpgradeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FromTier

	// no validation rules for ToTier

	if len(errors) > 0 {
		return UpgradeResponseMultiError(errors)
	}

	return nil
}

// UpgradeResponseMultiError is an error wrapping multiple validation errors
// returned by UpgradeResponse.ValidateAll() if the designated constraints
// aren't met.
type UpgradeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeResponseMultiError) AllErrors() []error { return m }

// UpgradeResponseValidationError is the validation error returned by
// UpgradeResponse.Validate if the designated constraints aren't met.
type UpgradeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeResponseValidationError) ErrorName() string { return "UpgradeResponseValidationError" }

// Error satisfies the builtin error interface
func (e UpgradeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeResponseValidationError{}

// Validate checks the field values on ShowComponentToActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShowComponentToActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShowComponentToActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShowComponentToActorRequestMultiError, or nil if none found.
func (m *ShowComponentToActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ShowComponentToActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ShowComponentToActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ComponentName

	// no validation rules for DisplayComponent

	if len(errors) > 0 {
		return ShowComponentToActorRequestMultiError(errors)
	}

	return nil
}

// ShowComponentToActorRequestMultiError is an error wrapping multiple
// validation errors returned by ShowComponentToActorRequest.ValidateAll() if
// the designated constraints aren't met.
type ShowComponentToActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShowComponentToActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShowComponentToActorRequestMultiError) AllErrors() []error { return m }

// ShowComponentToActorRequestValidationError is the validation error returned
// by ShowComponentToActorRequest.Validate if the designated constraints
// aren't met.
type ShowComponentToActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShowComponentToActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShowComponentToActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShowComponentToActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShowComponentToActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShowComponentToActorRequestValidationError) ErrorName() string {
	return "ShowComponentToActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ShowComponentToActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShowComponentToActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShowComponentToActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShowComponentToActorRequestValidationError{}

// Validate checks the field values on ShowComponentToActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShowComponentToActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShowComponentToActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShowComponentToActorResponseMultiError, or nil if none found.
func (m *ShowComponentToActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ShowComponentToActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShowComponentToActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShowComponentToActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShowComponentToActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowComponent

	if len(errors) > 0 {
		return ShowComponentToActorResponseMultiError(errors)
	}

	return nil
}

// ShowComponentToActorResponseMultiError is an error wrapping multiple
// validation errors returned by ShowComponentToActorResponse.ValidateAll() if
// the designated constraints aren't met.
type ShowComponentToActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShowComponentToActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShowComponentToActorResponseMultiError) AllErrors() []error { return m }

// ShowComponentToActorResponseValidationError is the validation error returned
// by ShowComponentToActorResponse.Validate if the designated constraints
// aren't met.
type ShowComponentToActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShowComponentToActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShowComponentToActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShowComponentToActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShowComponentToActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShowComponentToActorResponseValidationError) ErrorName() string {
	return "ShowComponentToActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ShowComponentToActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShowComponentToActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShowComponentToActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShowComponentToActorResponseValidationError{}

// Validate checks the field values on GetCriteriaForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCriteriaForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCriteriaForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCriteriaForActorRequestMultiError, or nil if none found.
func (m *GetCriteriaForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCriteriaForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetCriteriaForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCriteriaForActorRequestMultiError(errors)
	}

	return nil
}

// GetCriteriaForActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetCriteriaForActorRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCriteriaForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCriteriaForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCriteriaForActorRequestMultiError) AllErrors() []error { return m }

// GetCriteriaForActorRequestValidationError is the validation error returned
// by GetCriteriaForActorRequest.Validate if the designated constraints aren't met.
type GetCriteriaForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCriteriaForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCriteriaForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCriteriaForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCriteriaForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCriteriaForActorRequestValidationError) ErrorName() string {
	return "GetCriteriaForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCriteriaForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCriteriaForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCriteriaForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCriteriaForActorRequestValidationError{}

// Validate checks the field values on GetCriteriaForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCriteriaForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCriteriaForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCriteriaForActorResponseMultiError, or nil if none found.
func (m *GetCriteriaForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCriteriaForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCriteriaForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCriteriaForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCriteriaForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExternalCriteria() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCriteriaForActorResponseValidationError{
						field:  fmt.Sprintf("ExternalCriteria[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCriteriaForActorResponseValidationError{
						field:  fmt.Sprintf("ExternalCriteria[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCriteriaForActorResponseValidationError{
					field:  fmt.Sprintf("ExternalCriteria[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCriteriaForActorResponseMultiError(errors)
	}

	return nil
}

// GetCriteriaForActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetCriteriaForActorResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCriteriaForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCriteriaForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCriteriaForActorResponseMultiError) AllErrors() []error { return m }

// GetCriteriaForActorResponseValidationError is the validation error returned
// by GetCriteriaForActorResponse.Validate if the designated constraints
// aren't met.
type GetCriteriaForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCriteriaForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCriteriaForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCriteriaForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCriteriaForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCriteriaForActorResponseValidationError) ErrorName() string {
	return "GetCriteriaForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCriteriaForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCriteriaForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCriteriaForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCriteriaForActorResponseValidationError{}

// Validate checks the field values on IsActorEligibleForMovementRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IsActorEligibleForMovementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsActorEligibleForMovementRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IsActorEligibleForMovementRequestMultiError, or nil if none found.
func (m *IsActorEligibleForMovementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsActorEligibleForMovementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := IsActorEligibleForMovementRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MovementType

	// no validation rules for FromTier

	// no validation rules for ToTier

	if len(errors) > 0 {
		return IsActorEligibleForMovementRequestMultiError(errors)
	}

	return nil
}

// IsActorEligibleForMovementRequestMultiError is an error wrapping multiple
// validation errors returned by
// IsActorEligibleForMovementRequest.ValidateAll() if the designated
// constraints aren't met.
type IsActorEligibleForMovementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsActorEligibleForMovementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsActorEligibleForMovementRequestMultiError) AllErrors() []error { return m }

// IsActorEligibleForMovementRequestValidationError is the validation error
// returned by IsActorEligibleForMovementRequest.Validate if the designated
// constraints aren't met.
type IsActorEligibleForMovementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsActorEligibleForMovementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsActorEligibleForMovementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsActorEligibleForMovementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsActorEligibleForMovementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsActorEligibleForMovementRequestValidationError) ErrorName() string {
	return "IsActorEligibleForMovementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsActorEligibleForMovementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsActorEligibleForMovementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsActorEligibleForMovementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsActorEligibleForMovementRequestValidationError{}

// Validate checks the field values on IsActorEligibleForMovementResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IsActorEligibleForMovementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsActorEligibleForMovementResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IsActorEligibleForMovementResponseMultiError, or nil if none found.
func (m *IsActorEligibleForMovementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsActorEligibleForMovementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsActorEligibleForMovementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsActorEligibleForMovementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsActorEligibleForMovementResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEligible

	if len(errors) > 0 {
		return IsActorEligibleForMovementResponseMultiError(errors)
	}

	return nil
}

// IsActorEligibleForMovementResponseMultiError is an error wrapping multiple
// validation errors returned by
// IsActorEligibleForMovementResponse.ValidateAll() if the designated
// constraints aren't met.
type IsActorEligibleForMovementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsActorEligibleForMovementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsActorEligibleForMovementResponseMultiError) AllErrors() []error { return m }

// IsActorEligibleForMovementResponseValidationError is the validation error
// returned by IsActorEligibleForMovementResponse.Validate if the designated
// constraints aren't met.
type IsActorEligibleForMovementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsActorEligibleForMovementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsActorEligibleForMovementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsActorEligibleForMovementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsActorEligibleForMovementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsActorEligibleForMovementResponseValidationError) ErrorName() string {
	return "IsActorEligibleForMovementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsActorEligibleForMovementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsActorEligibleForMovementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsActorEligibleForMovementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsActorEligibleForMovementResponseValidationError{}

// Validate checks the field values on IsUserInGracePeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsUserInGracePeriodRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsUserInGracePeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsUserInGracePeriodRequestMultiError, or nil if none found.
func (m *IsUserInGracePeriodRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserInGracePeriodRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := IsUserInGracePeriodRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IsUserInGracePeriodRequestMultiError(errors)
	}

	return nil
}

// IsUserInGracePeriodRequestMultiError is an error wrapping multiple
// validation errors returned by IsUserInGracePeriodRequest.ValidateAll() if
// the designated constraints aren't met.
type IsUserInGracePeriodRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserInGracePeriodRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserInGracePeriodRequestMultiError) AllErrors() []error { return m }

// IsUserInGracePeriodRequestValidationError is the validation error returned
// by IsUserInGracePeriodRequest.Validate if the designated constraints aren't met.
type IsUserInGracePeriodRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserInGracePeriodRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsUserInGracePeriodRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsUserInGracePeriodRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsUserInGracePeriodRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsUserInGracePeriodRequestValidationError) ErrorName() string {
	return "IsUserInGracePeriodRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserInGracePeriodRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserInGracePeriodRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserInGracePeriodRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserInGracePeriodRequestValidationError{}

// Validate checks the field values on IsUserInGracePeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsUserInGracePeriodResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsUserInGracePeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsUserInGracePeriodResponseMultiError, or nil if none found.
func (m *IsUserInGracePeriodResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserInGracePeriodResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsUserInGracePeriodResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsUserInGracePeriodResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsUserInGracePeriodResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsInGracePeriod

	if len(errors) > 0 {
		return IsUserInGracePeriodResponseMultiError(errors)
	}

	return nil
}

// IsUserInGracePeriodResponseMultiError is an error wrapping multiple
// validation errors returned by IsUserInGracePeriodResponse.ValidateAll() if
// the designated constraints aren't met.
type IsUserInGracePeriodResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserInGracePeriodResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserInGracePeriodResponseMultiError) AllErrors() []error { return m }

// IsUserInGracePeriodResponseValidationError is the validation error returned
// by IsUserInGracePeriodResponse.Validate if the designated constraints
// aren't met.
type IsUserInGracePeriodResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserInGracePeriodResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsUserInGracePeriodResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsUserInGracePeriodResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsUserInGracePeriodResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsUserInGracePeriodResponseValidationError) ErrorName() string {
	return "IsUserInGracePeriodResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserInGracePeriodResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserInGracePeriodResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserInGracePeriodResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserInGracePeriodResponseValidationError{}

// Validate checks the field values on GetDetailsForCxRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDetailsForCxRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailsForCxRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDetailsForCxRequestMultiError, or nil if none found.
func (m *GetDetailsForCxRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailsForCxRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetDetailsForCxRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetDetailsForCxRequestMultiError(errors)
	}

	return nil
}

// GetDetailsForCxRequestMultiError is an error wrapping multiple validation
// errors returned by GetDetailsForCxRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDetailsForCxRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailsForCxRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailsForCxRequestMultiError) AllErrors() []error { return m }

// GetDetailsForCxRequestValidationError is the validation error returned by
// GetDetailsForCxRequest.Validate if the designated constraints aren't met.
type GetDetailsForCxRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailsForCxRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailsForCxRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailsForCxRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailsForCxRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailsForCxRequestValidationError) ErrorName() string {
	return "GetDetailsForCxRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailsForCxRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailsForCxRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailsForCxRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailsForCxRequestValidationError{}

// Validate checks the field values on GetDetailsForCxResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDetailsForCxResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailsForCxResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDetailsForCxResponseMultiError, or nil if none found.
func (m *GetDetailsForCxResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailsForCxResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailsForCxResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentTier

	// no validation rules for IsUserInGrace

	if all {
		switch v := interface{}(m.GetGracePeriodTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "GracePeriodTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "GracePeriodTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGracePeriodTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailsForCxResponseValidationError{
				field:  "GracePeriodTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsUserInCoolOff

	if all {
		switch v := interface{}(m.GetCoolOffPeriodTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "CoolOffPeriodTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "CoolOffPeriodTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCoolOffPeriodTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailsForCxResponseValidationError{
				field:  "CoolOffPeriodTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMovementHistories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDetailsForCxResponseValidationError{
						field:  fmt.Sprintf("MovementHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDetailsForCxResponseValidationError{
						field:  fmt.Sprintf("MovementHistories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDetailsForCxResponseValidationError{
					field:  fmt.Sprintf("MovementHistories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsARewardsAbuserUser

	if all {
		switch v := interface{}(m.GetTierMovementCriterias()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "TierMovementCriterias",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailsForCxResponseValidationError{
					field:  "TierMovementCriterias",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTierMovementCriterias()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailsForCxResponseValidationError{
				field:  "TierMovementCriterias",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentAmb

	// no validation rules for RequiredAmb

	// no validation rules for Shortfall

	for idx, item := range m.GetAmbHistory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDetailsForCxResponseValidationError{
						field:  fmt.Sprintf("AmbHistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDetailsForCxResponseValidationError{
						field:  fmt.Sprintf("AmbHistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDetailsForCxResponseValidationError{
					field:  fmt.Sprintf("AmbHistory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDetailsForCxResponseMultiError(errors)
	}

	return nil
}

// GetDetailsForCxResponseMultiError is an error wrapping multiple validation
// errors returned by GetDetailsForCxResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDetailsForCxResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailsForCxResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailsForCxResponseMultiError) AllErrors() []error { return m }

// GetDetailsForCxResponseValidationError is the validation error returned by
// GetDetailsForCxResponse.Validate if the designated constraints aren't met.
type GetDetailsForCxResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailsForCxResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailsForCxResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailsForCxResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailsForCxResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailsForCxResponseValidationError) ErrorName() string {
	return "GetDetailsForCxResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailsForCxResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailsForCxResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailsForCxResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailsForCxResponseValidationError{}

// Validate checks the field values on AmbDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AmbDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AmbDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AmbDetailsMultiError, or
// nil if none found.
func (m *AmbDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AmbDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Dates

	// no validation rules for Plan

	// no validation rules for AmbMaintained

	if len(errors) > 0 {
		return AmbDetailsMultiError(errors)
	}

	return nil
}

// AmbDetailsMultiError is an error wrapping multiple validation errors
// returned by AmbDetails.ValidateAll() if the designated constraints aren't met.
type AmbDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmbDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmbDetailsMultiError) AllErrors() []error { return m }

// AmbDetailsValidationError is the validation error returned by
// AmbDetails.Validate if the designated constraints aren't met.
type AmbDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmbDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmbDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmbDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmbDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmbDetailsValidationError) ErrorName() string { return "AmbDetailsValidationError" }

// Error satisfies the builtin error interface
func (e AmbDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmbDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmbDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmbDetailsValidationError{}

// Validate checks the field values on OverrideGracePeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OverrideGracePeriodRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OverrideGracePeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OverrideGracePeriodRequestMultiError, or nil if none found.
func (m *OverrideGracePeriodRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OverrideGracePeriodRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := OverrideGracePeriodRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOverrideTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OverrideGracePeriodRequestValidationError{
					field:  "OverrideTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OverrideGracePeriodRequestValidationError{
					field:  "OverrideTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOverrideTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OverrideGracePeriodRequestValidationError{
				field:  "OverrideTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OverrideGracePeriodRequestMultiError(errors)
	}

	return nil
}

// OverrideGracePeriodRequestMultiError is an error wrapping multiple
// validation errors returned by OverrideGracePeriodRequest.ValidateAll() if
// the designated constraints aren't met.
type OverrideGracePeriodRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OverrideGracePeriodRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OverrideGracePeriodRequestMultiError) AllErrors() []error { return m }

// OverrideGracePeriodRequestValidationError is the validation error returned
// by OverrideGracePeriodRequest.Validate if the designated constraints aren't met.
type OverrideGracePeriodRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OverrideGracePeriodRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OverrideGracePeriodRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OverrideGracePeriodRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OverrideGracePeriodRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OverrideGracePeriodRequestValidationError) ErrorName() string {
	return "OverrideGracePeriodRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OverrideGracePeriodRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOverrideGracePeriodRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OverrideGracePeriodRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OverrideGracePeriodRequestValidationError{}

// Validate checks the field values on OverrideGracePeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OverrideGracePeriodResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OverrideGracePeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OverrideGracePeriodResponseMultiError, or nil if none found.
func (m *OverrideGracePeriodResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OverrideGracePeriodResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OverrideGracePeriodResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OverrideGracePeriodResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OverrideGracePeriodResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OverrideGracePeriodResponseMultiError(errors)
	}

	return nil
}

// OverrideGracePeriodResponseMultiError is an error wrapping multiple
// validation errors returned by OverrideGracePeriodResponse.ValidateAll() if
// the designated constraints aren't met.
type OverrideGracePeriodResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OverrideGracePeriodResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OverrideGracePeriodResponseMultiError) AllErrors() []error { return m }

// OverrideGracePeriodResponseValidationError is the validation error returned
// by OverrideGracePeriodResponse.Validate if the designated constraints
// aren't met.
type OverrideGracePeriodResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OverrideGracePeriodResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OverrideGracePeriodResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OverrideGracePeriodResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OverrideGracePeriodResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OverrideGracePeriodResponseValidationError) ErrorName() string {
	return "OverrideGracePeriodResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OverrideGracePeriodResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOverrideGracePeriodResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OverrideGracePeriodResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OverrideGracePeriodResponseValidationError{}

// Validate checks the field values on OverrideCoolOffPeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OverrideCoolOffPeriodRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OverrideCoolOffPeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OverrideCoolOffPeriodRequestMultiError, or nil if none found.
func (m *OverrideCoolOffPeriodRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OverrideCoolOffPeriodRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := OverrideCoolOffPeriodRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOverrideTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OverrideCoolOffPeriodRequestValidationError{
					field:  "OverrideTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OverrideCoolOffPeriodRequestValidationError{
					field:  "OverrideTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOverrideTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OverrideCoolOffPeriodRequestValidationError{
				field:  "OverrideTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OverrideCoolOffPeriodRequestMultiError(errors)
	}

	return nil
}

// OverrideCoolOffPeriodRequestMultiError is an error wrapping multiple
// validation errors returned by OverrideCoolOffPeriodRequest.ValidateAll() if
// the designated constraints aren't met.
type OverrideCoolOffPeriodRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OverrideCoolOffPeriodRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OverrideCoolOffPeriodRequestMultiError) AllErrors() []error { return m }

// OverrideCoolOffPeriodRequestValidationError is the validation error returned
// by OverrideCoolOffPeriodRequest.Validate if the designated constraints
// aren't met.
type OverrideCoolOffPeriodRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OverrideCoolOffPeriodRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OverrideCoolOffPeriodRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OverrideCoolOffPeriodRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OverrideCoolOffPeriodRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OverrideCoolOffPeriodRequestValidationError) ErrorName() string {
	return "OverrideCoolOffPeriodRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OverrideCoolOffPeriodRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOverrideCoolOffPeriodRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OverrideCoolOffPeriodRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OverrideCoolOffPeriodRequestValidationError{}

// Validate checks the field values on OverrideCoolOffPeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OverrideCoolOffPeriodResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OverrideCoolOffPeriodResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OverrideCoolOffPeriodResponseMultiError, or nil if none found.
func (m *OverrideCoolOffPeriodResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OverrideCoolOffPeriodResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OverrideCoolOffPeriodResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OverrideCoolOffPeriodResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OverrideCoolOffPeriodResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OverrideCoolOffPeriodResponseMultiError(errors)
	}

	return nil
}

// OverrideCoolOffPeriodResponseMultiError is an error wrapping multiple
// validation errors returned by OverrideCoolOffPeriodResponse.ValidateAll()
// if the designated constraints aren't met.
type OverrideCoolOffPeriodResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OverrideCoolOffPeriodResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OverrideCoolOffPeriodResponseMultiError) AllErrors() []error { return m }

// OverrideCoolOffPeriodResponseValidationError is the validation error
// returned by OverrideCoolOffPeriodResponse.Validate if the designated
// constraints aren't met.
type OverrideCoolOffPeriodResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OverrideCoolOffPeriodResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OverrideCoolOffPeriodResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OverrideCoolOffPeriodResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OverrideCoolOffPeriodResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OverrideCoolOffPeriodResponseValidationError) ErrorName() string {
	return "OverrideCoolOffPeriodResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OverrideCoolOffPeriodResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOverrideCoolOffPeriodResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OverrideCoolOffPeriodResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OverrideCoolOffPeriodResponseValidationError{}

// Validate checks the field values on GetConfigParamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConfigParamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConfigParamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetConfigParamsRequestMultiError, or nil if none found.
func (m *GetConfigParamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConfigParamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetConfigParamsRequestMultiError(errors)
	}

	return nil
}

// GetConfigParamsRequestMultiError is an error wrapping multiple validation
// errors returned by GetConfigParamsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetConfigParamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConfigParamsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConfigParamsRequestMultiError) AllErrors() []error { return m }

// GetConfigParamsRequestValidationError is the validation error returned by
// GetConfigParamsRequest.Validate if the designated constraints aren't met.
type GetConfigParamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConfigParamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConfigParamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConfigParamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConfigParamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConfigParamsRequestValidationError) ErrorName() string {
	return "GetConfigParamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConfigParamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConfigParamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConfigParamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConfigParamsRequestValidationError{}

// Validate checks the field values on GetConfigParamsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConfigParamsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConfigParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetConfigParamsResponseMultiError, or nil if none found.
func (m *GetConfigParamsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConfigParamsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConfigParamsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDowngradeWindowDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "DowngradeWindowDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "DowngradeWindowDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDowngradeWindowDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConfigParamsResponseValidationError{
				field:  "DowngradeWindowDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGraceWindowDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "GraceWindowDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "GraceWindowDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGraceWindowDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConfigParamsResponseValidationError{
				field:  "GraceWindowDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGraceInitialWindowDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "GraceInitialWindowDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "GraceInitialWindowDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGraceInitialWindowDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConfigParamsResponseValidationError{
				field:  "GraceInitialWindowDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRegularTierEnabledForActor

	if all {
		switch v := interface{}(m.GetRegularTierConfigParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "RegularTierConfigParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "RegularTierConfigParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRegularTierConfigParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConfigParamsResponseValidationError{
				field:  "RegularTierConfigParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsMultipleWaysToEnterTieringEnabledForActor

	if all {
		switch v := interface{}(m.GetCriteriaSegmentExclusionConfigs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "CriteriaSegmentExclusionConfigs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConfigParamsResponseValidationError{
					field:  "CriteriaSegmentExclusionConfigs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCriteriaSegmentExclusionConfigs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConfigParamsResponseValidationError{
				field:  "CriteriaSegmentExclusionConfigs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConfigParamsResponseMultiError(errors)
	}

	return nil
}

// GetConfigParamsResponseMultiError is an error wrapping multiple validation
// errors returned by GetConfigParamsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetConfigParamsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConfigParamsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConfigParamsResponseMultiError) AllErrors() []error { return m }

// GetConfigParamsResponseValidationError is the validation error returned by
// GetConfigParamsResponse.Validate if the designated constraints aren't met.
type GetConfigParamsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConfigParamsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConfigParamsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConfigParamsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConfigParamsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConfigParamsResponseValidationError) ErrorName() string {
	return "GetConfigParamsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConfigParamsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConfigParamsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConfigParamsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConfigParamsResponseValidationError{}

// Validate checks the field values on CriteriaSegmentExclusionConfigs with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CriteriaSegmentExclusionConfigs) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaSegmentExclusionConfigs with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CriteriaSegmentExclusionConfigsMultiError, or nil if none found.
func (m *CriteriaSegmentExclusionConfigs) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaSegmentExclusionConfigs) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CriteriaSegmentExclusionConfigsMultiError(errors)
	}

	return nil
}

// CriteriaSegmentExclusionConfigsMultiError is an error wrapping multiple
// validation errors returned by CriteriaSegmentExclusionConfigs.ValidateAll()
// if the designated constraints aren't met.
type CriteriaSegmentExclusionConfigsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaSegmentExclusionConfigsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaSegmentExclusionConfigsMultiError) AllErrors() []error { return m }

// CriteriaSegmentExclusionConfigsValidationError is the validation error
// returned by CriteriaSegmentExclusionConfigs.Validate if the designated
// constraints aren't met.
type CriteriaSegmentExclusionConfigsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaSegmentExclusionConfigsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaSegmentExclusionConfigsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaSegmentExclusionConfigsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaSegmentExclusionConfigsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaSegmentExclusionConfigsValidationError) ErrorName() string {
	return "CriteriaSegmentExclusionConfigsValidationError"
}

// Error satisfies the builtin error interface
func (e CriteriaSegmentExclusionConfigsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaSegmentExclusionConfigs.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaSegmentExclusionConfigsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaSegmentExclusionConfigsValidationError{}

// Validate checks the field values on RegularTierConfigParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegularTierConfigParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegularTierConfigParams with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegularTierConfigParamsMultiError, or nil if none found.
func (m *RegularTierConfigParams) ValidateAll() error {
	return m.validate(true)
}

func (m *RegularTierConfigParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMinBalanceForRegularTier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegularTierConfigParamsValidationError{
					field:  "MinBalanceForRegularTier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegularTierConfigParamsValidationError{
					field:  "MinBalanceForRegularTier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinBalanceForRegularTier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegularTierConfigParamsValidationError{
				field:  "MinBalanceForRegularTier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinBalancePenaltyForRegularTier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegularTierConfigParamsValidationError{
					field:  "MinBalancePenaltyForRegularTier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegularTierConfigParamsValidationError{
					field:  "MinBalancePenaltyForRegularTier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinBalancePenaltyForRegularTier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegularTierConfigParamsValidationError{
				field:  "MinBalancePenaltyForRegularTier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegularTierConfigParamsMultiError(errors)
	}

	return nil
}

// RegularTierConfigParamsMultiError is an error wrapping multiple validation
// errors returned by RegularTierConfigParams.ValidateAll() if the designated
// constraints aren't met.
type RegularTierConfigParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegularTierConfigParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegularTierConfigParamsMultiError) AllErrors() []error { return m }

// RegularTierConfigParamsValidationError is the validation error returned by
// RegularTierConfigParams.Validate if the designated constraints aren't met.
type RegularTierConfigParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegularTierConfigParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegularTierConfigParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegularTierConfigParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegularTierConfigParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegularTierConfigParamsValidationError) ErrorName() string {
	return "RegularTierConfigParamsValidationError"
}

// Error satisfies the builtin error interface
func (e RegularTierConfigParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegularTierConfigParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegularTierConfigParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegularTierConfigParamsValidationError{}

// Validate checks the field values on EvaluateTierForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvaluateTierForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvaluateTierForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvaluateTierForActorRequestMultiError, or nil if none found.
func (m *EvaluateTierForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EvaluateTierForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := EvaluateTierForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvaluateTierForActorRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvaluateTierForActorRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvaluateTierForActorRequestValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EvaluateTierForActorRequestMultiError(errors)
	}

	return nil
}

// EvaluateTierForActorRequestMultiError is an error wrapping multiple
// validation errors returned by EvaluateTierForActorRequest.ValidateAll() if
// the designated constraints aren't met.
type EvaluateTierForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvaluateTierForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvaluateTierForActorRequestMultiError) AllErrors() []error { return m }

// EvaluateTierForActorRequestValidationError is the validation error returned
// by EvaluateTierForActorRequest.Validate if the designated constraints
// aren't met.
type EvaluateTierForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvaluateTierForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvaluateTierForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvaluateTierForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvaluateTierForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvaluateTierForActorRequestValidationError) ErrorName() string {
	return "EvaluateTierForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EvaluateTierForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvaluateTierForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvaluateTierForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvaluateTierForActorRequestValidationError{}

// Validate checks the field values on EvaluateTierForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvaluateTierForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvaluateTierForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvaluateTierForActorResponseMultiError, or nil if none found.
func (m *EvaluateTierForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EvaluateTierForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvaluateTierForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvaluateTierForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvaluateTierForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EvaluatedTier

	// no validation rules for CriteriaReferenceId

	if len(errors) > 0 {
		return EvaluateTierForActorResponseMultiError(errors)
	}

	return nil
}

// EvaluateTierForActorResponseMultiError is an error wrapping multiple
// validation errors returned by EvaluateTierForActorResponse.ValidateAll() if
// the designated constraints aren't met.
type EvaluateTierForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvaluateTierForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvaluateTierForActorResponseMultiError) AllErrors() []error { return m }

// EvaluateTierForActorResponseValidationError is the validation error returned
// by EvaluateTierForActorResponse.Validate if the designated constraints
// aren't met.
type EvaluateTierForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvaluateTierForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvaluateTierForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvaluateTierForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvaluateTierForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvaluateTierForActorResponseValidationError) ErrorName() string {
	return "EvaluateTierForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EvaluateTierForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvaluateTierForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvaluateTierForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvaluateTierForActorResponseValidationError{}

// Validate checks the field values on
// CheckIfActorIsEligibleForCashbackRewardRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckIfActorIsEligibleForCashbackRewardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckIfActorIsEligibleForCashbackRewardRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CheckIfActorIsEligibleForCashbackRewardRequestMultiError, or nil if none found.
func (m *CheckIfActorIsEligibleForCashbackRewardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckIfActorIsEligibleForCashbackRewardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := CheckIfActorIsEligibleForCashbackRewardRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Tier

	if all {
		switch v := interface{}(m.GetRewardMonth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfActorIsEligibleForCashbackRewardRequestValidationError{
					field:  "RewardMonth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfActorIsEligibleForCashbackRewardRequestValidationError{
					field:  "RewardMonth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardMonth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfActorIsEligibleForCashbackRewardRequestValidationError{
				field:  "RewardMonth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckIfActorIsEligibleForCashbackRewardRequestMultiError(errors)
	}

	return nil
}

// CheckIfActorIsEligibleForCashbackRewardRequestMultiError is an error
// wrapping multiple validation errors returned by
// CheckIfActorIsEligibleForCashbackRewardRequest.ValidateAll() if the
// designated constraints aren't met.
type CheckIfActorIsEligibleForCashbackRewardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckIfActorIsEligibleForCashbackRewardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckIfActorIsEligibleForCashbackRewardRequestMultiError) AllErrors() []error { return m }

// CheckIfActorIsEligibleForCashbackRewardRequestValidationError is the
// validation error returned by
// CheckIfActorIsEligibleForCashbackRewardRequest.Validate if the designated
// constraints aren't met.
type CheckIfActorIsEligibleForCashbackRewardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckIfActorIsEligibleForCashbackRewardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckIfActorIsEligibleForCashbackRewardRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckIfActorIsEligibleForCashbackRewardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckIfActorIsEligibleForCashbackRewardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckIfActorIsEligibleForCashbackRewardRequestValidationError) ErrorName() string {
	return "CheckIfActorIsEligibleForCashbackRewardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckIfActorIsEligibleForCashbackRewardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckIfActorIsEligibleForCashbackRewardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckIfActorIsEligibleForCashbackRewardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckIfActorIsEligibleForCashbackRewardRequestValidationError{}

// Validate checks the field values on
// CheckIfActorIsEligibleForCashbackRewardResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckIfActorIsEligibleForCashbackRewardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckIfActorIsEligibleForCashbackRewardResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CheckIfActorIsEligibleForCashbackRewardResponseMultiError, or nil if none found.
func (m *CheckIfActorIsEligibleForCashbackRewardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckIfActorIsEligibleForCashbackRewardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIfActorIsEligibleForCashbackRewardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIfActorIsEligibleForCashbackRewardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIfActorIsEligibleForCashbackRewardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEligible

	if len(errors) > 0 {
		return CheckIfActorIsEligibleForCashbackRewardResponseMultiError(errors)
	}

	return nil
}

// CheckIfActorIsEligibleForCashbackRewardResponseMultiError is an error
// wrapping multiple validation errors returned by
// CheckIfActorIsEligibleForCashbackRewardResponse.ValidateAll() if the
// designated constraints aren't met.
type CheckIfActorIsEligibleForCashbackRewardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckIfActorIsEligibleForCashbackRewardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckIfActorIsEligibleForCashbackRewardResponseMultiError) AllErrors() []error { return m }

// CheckIfActorIsEligibleForCashbackRewardResponseValidationError is the
// validation error returned by
// CheckIfActorIsEligibleForCashbackRewardResponse.Validate if the designated
// constraints aren't met.
type CheckIfActorIsEligibleForCashbackRewardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckIfActorIsEligibleForCashbackRewardResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckIfActorIsEligibleForCashbackRewardResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckIfActorIsEligibleForCashbackRewardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckIfActorIsEligibleForCashbackRewardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckIfActorIsEligibleForCashbackRewardResponseValidationError) ErrorName() string {
	return "CheckIfActorIsEligibleForCashbackRewardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckIfActorIsEligibleForCashbackRewardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckIfActorIsEligibleForCashbackRewardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckIfActorIsEligibleForCashbackRewardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckIfActorIsEligibleForCashbackRewardResponseValidationError{}

// Validate checks the field values on GetTierTimeRangesForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTierTimeRangesForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierTimeRangesForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTierTimeRangesForActorRequestMultiError, or nil if none found.
func (m *GetTierTimeRangesForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierTimeRangesForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTierTimeRangesForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetTiers()) < 1 {
		err := GetTierTimeRangesForActorRequestValidationError{
			field:  "Tiers",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFilterFrom() == nil {
		err := GetTierTimeRangesForActorRequestValidationError{
			field:  "FilterFrom",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTierTimeRangesForActorRequestMultiError(errors)
	}

	return nil
}

// GetTierTimeRangesForActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTierTimeRangesForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTierTimeRangesForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierTimeRangesForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierTimeRangesForActorRequestMultiError) AllErrors() []error { return m }

// GetTierTimeRangesForActorRequestValidationError is the validation error
// returned by GetTierTimeRangesForActorRequest.Validate if the designated
// constraints aren't met.
type GetTierTimeRangesForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierTimeRangesForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierTimeRangesForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierTimeRangesForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierTimeRangesForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierTimeRangesForActorRequestValidationError) ErrorName() string {
	return "GetTierTimeRangesForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierTimeRangesForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierTimeRangesForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierTimeRangesForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierTimeRangesForActorRequestValidationError{}

// Validate checks the field values on GetTierTimeRangesForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTierTimeRangesForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierTimeRangesForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTierTimeRangesForActorResponseMultiError, or nil if none found.
func (m *GetTierTimeRangesForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierTimeRangesForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierTimeRangesForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierTimeRangesForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierTimeRangesForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetTierTimeRangesMap()))
		i := 0
		for key := range m.GetTierTimeRangesMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetTierTimeRangesMap()[key]
			_ = val

			// no validation rules for TierTimeRangesMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetTierTimeRangesForActorResponseValidationError{
							field:  fmt.Sprintf("TierTimeRangesMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetTierTimeRangesForActorResponseValidationError{
							field:  fmt.Sprintf("TierTimeRangesMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetTierTimeRangesForActorResponseValidationError{
						field:  fmt.Sprintf("TierTimeRangesMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetTierTimeRangesForActorResponseMultiError(errors)
	}

	return nil
}

// GetTierTimeRangesForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTierTimeRangesForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTierTimeRangesForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierTimeRangesForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierTimeRangesForActorResponseMultiError) AllErrors() []error { return m }

// GetTierTimeRangesForActorResponseValidationError is the validation error
// returned by GetTierTimeRangesForActorResponse.Validate if the designated
// constraints aren't met.
type GetTierTimeRangesForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierTimeRangesForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierTimeRangesForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierTimeRangesForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierTimeRangesForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierTimeRangesForActorResponseValidationError) ErrorName() string {
	return "GetTierTimeRangesForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierTimeRangesForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierTimeRangesForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierTimeRangesForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierTimeRangesForActorResponseValidationError{}

// Validate checks the field values on TimeRanges with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRanges) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRanges with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangesMultiError, or
// nil if none found.
func (m *TimeRanges) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRanges) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTimeRanges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TimeRangesValidationError{
						field:  fmt.Sprintf("TimeRanges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TimeRangesValidationError{
						field:  fmt.Sprintf("TimeRanges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TimeRangesValidationError{
					field:  fmt.Sprintf("TimeRanges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TimeRangesMultiError(errors)
	}

	return nil
}

// TimeRangesMultiError is an error wrapping multiple validation errors
// returned by TimeRanges.ValidateAll() if the designated constraints aren't met.
type TimeRangesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangesMultiError) AllErrors() []error { return m }

// TimeRangesValidationError is the validation error returned by
// TimeRanges.Validate if the designated constraints aren't met.
type TimeRangesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangesValidationError) ErrorName() string { return "TimeRangesValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRanges.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangesValidationError{}

// Validate checks the field values on TimeRange with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangeMultiError, or nil
// if none found.
func (m *TimeRange) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimeRangeMultiError(errors)
	}

	return nil
}

// TimeRangeMultiError is an error wrapping multiple validation errors returned
// by TimeRange.ValidateAll() if the designated constraints aren't met.
type TimeRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeMultiError) AllErrors() []error { return m }

// TimeRangeValidationError is the validation error returned by
// TimeRange.Validate if the designated constraints aren't met.
type TimeRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeValidationError) ErrorName() string { return "TimeRangeValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeValidationError{}

// Validate checks the field values on GetActorScreenInteractionDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetActorScreenInteractionDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActorScreenInteractionDetailsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetActorScreenInteractionDetailsRequestMultiError, or nil if none found.
func (m *GetActorScreenInteractionDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorScreenInteractionDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetActorScreenInteractionDetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetActorScreenInteractionDetailsRequest_Screen_NotInLookup[m.GetScreen()]; ok {
		err := GetActorScreenInteractionDetailsRequestValidationError{
			field:  "Screen",
			reason: "value must not be in list [TIERING_SCREEN_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetActorScreenInteractionDetailsRequest_RequestType_NotInLookup[m.GetRequestType()]; ok {
		err := GetActorScreenInteractionDetailsRequestValidationError{
			field:  "RequestType",
			reason: "value must not be in list [REQUEST_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetActorScreenInteractionDetailsRequestMultiError(errors)
	}

	return nil
}

// GetActorScreenInteractionDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetActorScreenInteractionDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActorScreenInteractionDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorScreenInteractionDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorScreenInteractionDetailsRequestMultiError) AllErrors() []error { return m }

// GetActorScreenInteractionDetailsRequestValidationError is the validation
// error returned by GetActorScreenInteractionDetailsRequest.Validate if the
// designated constraints aren't met.
type GetActorScreenInteractionDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorScreenInteractionDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorScreenInteractionDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorScreenInteractionDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorScreenInteractionDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorScreenInteractionDetailsRequestValidationError) ErrorName() string {
	return "GetActorScreenInteractionDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorScreenInteractionDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorScreenInteractionDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorScreenInteractionDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorScreenInteractionDetailsRequestValidationError{}

var _GetActorScreenInteractionDetailsRequest_Screen_NotInLookup = map[enums.TieringScreen]struct{}{
	0: {},
}

var _GetActorScreenInteractionDetailsRequest_RequestType_NotInLookup = map[GetActorScreenInteractionDetailsRequest_RequestType]struct{}{
	0: {},
}

// Validate checks the field values on GetActorScreenInteractionDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetActorScreenInteractionDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActorScreenInteractionDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetActorScreenInteractionDetailsResponseMultiError, or nil if none found.
func (m *GetActorScreenInteractionDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorScreenInteractionDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorScreenInteractionDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorScreenInteractionDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorScreenInteractionDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Response

	if len(errors) > 0 {
		return GetActorScreenInteractionDetailsResponseMultiError(errors)
	}

	return nil
}

// GetActorScreenInteractionDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetActorScreenInteractionDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActorScreenInteractionDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorScreenInteractionDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorScreenInteractionDetailsResponseMultiError) AllErrors() []error { return m }

// GetActorScreenInteractionDetailsResponseValidationError is the validation
// error returned by GetActorScreenInteractionDetailsResponse.Validate if the
// designated constraints aren't met.
type GetActorScreenInteractionDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorScreenInteractionDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorScreenInteractionDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorScreenInteractionDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorScreenInteractionDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorScreenInteractionDetailsResponseValidationError) ErrorName() string {
	return "GetActorScreenInteractionDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorScreenInteractionDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorScreenInteractionDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorScreenInteractionDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorScreenInteractionDetailsResponseValidationError{}

// Validate checks the field values on GetDropOffBottomSheetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDropOffBottomSheetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDropOffBottomSheetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDropOffBottomSheetRequestMultiError, or nil if none found.
func (m *GetDropOffBottomSheetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDropOffBottomSheetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for TieringPitchMethod

	// no validation rules for TierToPitch

	if len(errors) > 0 {
		return GetDropOffBottomSheetRequestMultiError(errors)
	}

	return nil
}

// GetDropOffBottomSheetRequestMultiError is an error wrapping multiple
// validation errors returned by GetDropOffBottomSheetRequest.ValidateAll() if
// the designated constraints aren't met.
type GetDropOffBottomSheetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDropOffBottomSheetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDropOffBottomSheetRequestMultiError) AllErrors() []error { return m }

// GetDropOffBottomSheetRequestValidationError is the validation error returned
// by GetDropOffBottomSheetRequest.Validate if the designated constraints
// aren't met.
type GetDropOffBottomSheetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDropOffBottomSheetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDropOffBottomSheetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDropOffBottomSheetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDropOffBottomSheetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDropOffBottomSheetRequestValidationError) ErrorName() string {
	return "GetDropOffBottomSheetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDropOffBottomSheetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDropOffBottomSheetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDropOffBottomSheetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDropOffBottomSheetRequestValidationError{}

// Validate checks the field values on GetDropOffBottomSheetResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDropOffBottomSheetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDropOffBottomSheetResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDropOffBottomSheetResponseMultiError, or nil if none found.
func (m *GetDropOffBottomSheetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDropOffBottomSheetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDropOffBottomSheetResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDropOffBottomSheetResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDropOffBottomSheetResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDropOffBottomSheetResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDropOffBottomSheetResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDropOffBottomSheetResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDropOffBottomSheetResponseMultiError(errors)
	}

	return nil
}

// GetDropOffBottomSheetResponseMultiError is an error wrapping multiple
// validation errors returned by GetDropOffBottomSheetResponse.ValidateAll()
// if the designated constraints aren't met.
type GetDropOffBottomSheetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDropOffBottomSheetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDropOffBottomSheetResponseMultiError) AllErrors() []error { return m }

// GetDropOffBottomSheetResponseValidationError is the validation error
// returned by GetDropOffBottomSheetResponse.Validate if the designated
// constraints aren't met.
type GetDropOffBottomSheetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDropOffBottomSheetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDropOffBottomSheetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDropOffBottomSheetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDropOffBottomSheetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDropOffBottomSheetResponseValidationError) ErrorName() string {
	return "GetDropOffBottomSheetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDropOffBottomSheetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDropOffBottomSheetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDropOffBottomSheetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDropOffBottomSheetResponseValidationError{}

// Validate checks the field values on GetAMBInfoRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAMBInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAMBInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAMBInfoRequestMultiError, or nil if none found.
func (m *GetAMBInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAMBInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAMBInfoRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAMBInfoRequestMultiError(errors)
	}

	return nil
}

// GetAMBInfoRequestMultiError is an error wrapping multiple validation errors
// returned by GetAMBInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAMBInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAMBInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAMBInfoRequestMultiError) AllErrors() []error { return m }

// GetAMBInfoRequestValidationError is the validation error returned by
// GetAMBInfoRequest.Validate if the designated constraints aren't met.
type GetAMBInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAMBInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAMBInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAMBInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAMBInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAMBInfoRequestValidationError) ErrorName() string {
	return "GetAMBInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAMBInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAMBInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAMBInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAMBInfoRequestValidationError{}

// Validate checks the field values on GetAMBInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAMBInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAMBInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAMBInfoResponseMultiError, or nil if none found.
func (m *GetAMBInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAMBInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentAmb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "CurrentAmb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "CurrentAmb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAmb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBInfoResponseValidationError{
				field:  "CurrentAmb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTargetAmb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "TargetAmb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "TargetAmb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetAmb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBInfoResponseValidationError{
				field:  "TargetAmb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentTier

	if all {
		switch v := interface{}(m.GetShortfallAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "ShortfallAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBInfoResponseValidationError{
					field:  "ShortfallAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShortfallAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBInfoResponseValidationError{
				field:  "ShortfallAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAMBInfoResponseMultiError(errors)
	}

	return nil
}

// GetAMBInfoResponseMultiError is an error wrapping multiple validation errors
// returned by GetAMBInfoResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAMBInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAMBInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAMBInfoResponseMultiError) AllErrors() []error { return m }

// GetAMBInfoResponseValidationError is the validation error returned by
// GetAMBInfoResponse.Validate if the designated constraints aren't met.
type GetAMBInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAMBInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAMBInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAMBInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAMBInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAMBInfoResponseValidationError) ErrorName() string {
	return "GetAMBInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAMBInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAMBInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAMBInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAMBInfoResponseValidationError{}

// Validate checks the field values on
// IsUserEligibleForRewardsResponse_TierToEligibilityStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IsUserEligibleForRewardsResponse_TierToEligibilityStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsUserEligibleForRewardsResponse_TierToEligibilityStatusMultiError, or nil
// if none found.
func (m *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TierName

	// no validation rules for IsEligible

	if len(errors) > 0 {
		return IsUserEligibleForRewardsResponse_TierToEligibilityStatusMultiError(errors)
	}

	return nil
}

// IsUserEligibleForRewardsResponse_TierToEligibilityStatusMultiError is an
// error wrapping multiple validation errors returned by
// IsUserEligibleForRewardsResponse_TierToEligibilityStatus.ValidateAll() if
// the designated constraints aren't met.
type IsUserEligibleForRewardsResponse_TierToEligibilityStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserEligibleForRewardsResponse_TierToEligibilityStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserEligibleForRewardsResponse_TierToEligibilityStatusMultiError) AllErrors() []error {
	return m
}

// IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError is
// the validation error returned by
// IsUserEligibleForRewardsResponse_TierToEligibilityStatus.Validate if the
// designated constraints aren't met.
type IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError) ErrorName() string {
	return "IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserEligibleForRewardsResponse_TierToEligibilityStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserEligibleForRewardsResponse_TierToEligibilityStatusValidationError{}

// Validate checks the field values on EvaluateTierForActorRequest_Options with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EvaluateTierForActorRequest_Options) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvaluateTierForActorRequest_Options
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// EvaluateTierForActorRequest_OptionsMultiError, or nil if none found.
func (m *EvaluateTierForActorRequest_Options) ValidateAll() error {
	return m.validate(true)
}

func (m *EvaluateTierForActorRequest_Options) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ToEvalOnRealTimeData

	// no validation rules for ToEvalForMultipleWays

	// no validation rules for ToSkipAppAccessCheck

	if len(errors) > 0 {
		return EvaluateTierForActorRequest_OptionsMultiError(errors)
	}

	return nil
}

// EvaluateTierForActorRequest_OptionsMultiError is an error wrapping multiple
// validation errors returned by
// EvaluateTierForActorRequest_Options.ValidateAll() if the designated
// constraints aren't met.
type EvaluateTierForActorRequest_OptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvaluateTierForActorRequest_OptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvaluateTierForActorRequest_OptionsMultiError) AllErrors() []error { return m }

// EvaluateTierForActorRequest_OptionsValidationError is the validation error
// returned by EvaluateTierForActorRequest_Options.Validate if the designated
// constraints aren't met.
type EvaluateTierForActorRequest_OptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvaluateTierForActorRequest_OptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvaluateTierForActorRequest_OptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvaluateTierForActorRequest_OptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvaluateTierForActorRequest_OptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvaluateTierForActorRequest_OptionsValidationError) ErrorName() string {
	return "EvaluateTierForActorRequest_OptionsValidationError"
}

// Error satisfies the builtin error interface
func (e EvaluateTierForActorRequest_OptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvaluateTierForActorRequest_Options.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvaluateTierForActorRequest_OptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvaluateTierForActorRequest_OptionsValidationError{}
