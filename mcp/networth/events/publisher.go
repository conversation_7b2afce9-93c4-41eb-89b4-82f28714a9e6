package events

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events"
)

// EventPublisher provides methods to publish MCP events
type EventPublisher struct {
	eventBroker events.Broker
}

func NewEventPublisher(eventBroker events.Broker) *EventPublisher {
	return &EventPublisher{
		eventBroker: eventBroker,
	}
}

// PublishChatEvent publishes session-level chat events with tool usage
func (p *EventPublisher) PublishChatEvent(ctx context.Context, actorId, sessionId string, creditReportUsed, netWorthUsed, transactionsUsed bool) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPChat(actorId, sessionId, creditReportUsed, netWorthUsed, transactionsUsed))
	})
}
