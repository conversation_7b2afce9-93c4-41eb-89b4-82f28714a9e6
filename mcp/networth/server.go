package networth

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"

	sessionPb "github.com/epifi/gamma/api/auth/session"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/mcp/networth/middlewares"
	"github.com/epifi/gamma/mcp/networth/tools"
	mcpEvents "github.com/epifi/gamma/mcp/networth/events"
)

func CreateMcpServer(
	netWorthClient networthPb.NetWorthClient,
	sessionManagerClient sessionPb.SessionManagerClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	creditReportClient creditReportPb.CreditReportManagerClient,
	mfExternalOrdersClient mfExternalPb.MFExternalOrdersClient,
	epfClient beEpfPb.EpfClient,
	eventBroker events.Broker) *server.MCPServer {

	eventPublisher := mcpEvents.NewEventPublisher(eventBroker)
	authMiddleware := middlewares.NewAuthMiddleware(sessionManagerClient)
	// Create a new MCP server
	s := server.NewMCPServer(
		"Fi MCP",
		"0.1.0",
		// Notifies clients when new tools gets added or any changes in tools
		server.WithInstructions("A financial portfolio management server that provides secure access to users' financial data through the Fi Money platform. This server enables users to:\n- Access comprehensive net worth analysis with asset/liability breakdowns\n- Retrieve detailed transaction histories for mutual funds, stocks and EPF accounts  \n- View credit reports with scores, loan details, and account histories\n- Perform financial analysis and portfolio optimizations\nData is retrieved securely with proper authentication and follows financial data privacy standards."),
		server.WithToolCapabilities(true),
		server.WithResourceCapabilities(true, true),
		server.WithLogging(),
		server.WithToolHandlerMiddleware(authMiddleware.AuthMiddleware),
	)

	fetchNetWorthHandler := tools.NewFetchNetWorthHandler(netWorthClient, variableGeneratorClient, connectedAccountClient, eventPublisher)
	fetchTransactionsHandler := tools.NewFetchTransactionsHandler(mfExternalOrdersClient, eventPublisher)
	fetchCreditReportHandler := tools.NewFetchCreditReportHandler(creditReportClient, eventPublisher)
	fetchEpfHandler := tools.NewFetchEpfHandler(epfClient)

	// Register fetch net worth tool
	s.AddTool(fetchNetWorthHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		result, fetchNetWorthErr := fetchNetWorthHandler.Handle(ctx, request)
		if fetchNetWorthErr != nil {
			logger.Error(ctx, "error fetching net worth", zap.Error(fetchNetWorthErr))
		} else {
			logger.Info(ctx, "net worth fetched successfully")
		}
		return result, fetchNetWorthErr
	})

	// Register fetch credit report details tool
	s.AddTool(fetchCreditReportHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		creditReportResult, fetchCreditReportErr := fetchCreditReportHandler.Handle(ctx, request)
		if fetchCreditReportErr != nil {
			logger.Error(ctx, "error fetching credit report details", zap.Error(fetchCreditReportErr))
		} else {
			logger.Info(ctx, "credit report details fetched successfully")
		}
		return creditReportResult, fetchCreditReportErr
	})

	// Register fetch portfolio transactions details tool
	s.AddTool(fetchTransactionsHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		portfolioTxnsResult, fetchTxnsErr := fetchTransactionsHandler.Handle(ctx, request)
		if fetchTxnsErr != nil {
			logger.Error(ctx, "error fetching transaction details", zap.Error(fetchTxnsErr))
		} else {
			logger.Info(ctx, "all transaction fetched successfully")
		}
		return portfolioTxnsResult, fetchTxnsErr
	})

	// Register fetch portfolio transactions details tool
	s.AddTool(fetchEpfHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		epfTxnResult, epfTxnErr := fetchEpfHandler.Handle(ctx, request)
		if epfTxnErr != nil {
			logger.Error(ctx, "error fetching epf details", zap.Error(epfTxnErr))
		} else {
			logger.Info(ctx, "epf details fetched successfully")
		}
		return epfTxnResult, epfTxnErr
	})

	return s
}
