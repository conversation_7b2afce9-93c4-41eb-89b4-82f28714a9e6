package tools

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	protoJson "google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	networthTools "github.com/epifi/gamma/api/mcp/networth"
	mcpEvents "github.com/epifi/gamma/mcp/networth/events"
	"github.com/epifi/gamma/pkg/obfuscator"
)

var (
	fetchTransactionsErrMsg = mcp.NewToolResultText("something went wrong in fetching portfolio transactions")
)

const (
	mfTransactionLimit   = 50
	mfOrderFetchPageSize = 1000
	mfTransactionDesc    = "We currently support the latest 50 mutual fund transactions, support for older than 50 transactions is coming soon..."
)

type FetchTransactionsHandler struct {
	mfExternalOrdersClient mfExternalPb.MFExternalOrdersClient
	eventPublisher         *mcpEvents.EventPublisher
}

func NewFetchTransactionsHandler(mfExternalOrdersClient mfExternalPb.MFExternalOrdersClient, eventPublisher *mcpEvents.EventPublisher) *FetchTransactionsHandler {
	return &FetchTransactionsHandler{
		mfExternalOrdersClient: mfExternalOrdersClient,
		eventPublisher:         eventPublisher,
	}
}

// GetTool returns tool with name, description and other metadata which gets used by mcp host
func (t *FetchTransactionsHandler) GetTool() mcp.Tool {
	return mcp.NewTool("fetch_transactions",
		mcp.WithDescription("Retrieve detailed transaction history across all accounts including mutual funds, stocks, and bank accounts with analysis capabilities."),
	)
}

// Handle is the main handler call that gets called on each tool call
// nolint:dupl
func (t *FetchTransactionsHandler) Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	sessionId := server.ClientSessionFromContext(ctx).SessionID()
	obfuscatedSessId := obfuscator.Hashed(sessionId)
	actorId := epificontext.ActorIdFromContext(ctx)
	if actorId == epificontext.UnknownId {
		logger.Error(ctx, "actorId is not populated for all transaction handler", zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchTransactionsErrMsg, nil
	}
	logger.Debug(ctx, "Received fetch all transactions call", zap.String(logger.SESSION_ID, obfuscatedSessId))

	allTxnsRes, allTxnsErr := t.fetchAllTransactionsApiCall(ctx, actorId)
	if allTxnsErr != nil {
		logger.Error(ctx, "error in fetching all transactions", zap.Error(allTxnsErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchTransactionsErrMsg, nil
	}

	// Instrument MCPChat event: transactions tool used
	if t.eventPublisher != nil {
		t.eventPublisher.PublishChatEvent(ctx, actorId, sessionId, false, false, true)
	}

	return mcp.NewToolResultText(allTxnsRes), nil
}

func (t *FetchTransactionsHandler) fetchAllTransactionsApiCall(ctx context.Context, actorId string) (string, error) {
	externalOrderRes, err := t.mfExternalOrdersClient.FetchAllExternalMFOrders(ctx, &mfExternalPb.FetchAllExternalMFOrdersRequest{
		ActorId: actorId,
		PageContextRequest: &rpcPb.PageContextRequest{
			PageSize: mfOrderFetchPageSize,
		},
	})
	if rpcErr := epifigrpc.RPCError(externalOrderRes, err); rpcErr != nil && !externalOrderRes.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("error while fetching external mf orders for networth mcp: %w", rpcErr)
	}

	orders := make([]*mfExternalPb.MutualFundExternalOrder, 0)
	for _, extOrder := range externalOrderRes.GetMutualFundExternalOrders() {
		if extOrder.GetExternalOrderType() == mfExternalPb.ExternalOrderType_BUY || extOrder.GetExternalOrderType() == mfExternalPb.ExternalOrderType_SELL {
			orders = append(orders, extOrder.MinimalRespForLLM())
		}
	}

	var mfTransactionDescription string
	if len(orders) > mfTransactionLimit {
		orders = orders[:mfTransactionLimit]
		mfTransactionDescription = mfTransactionDesc
	}

	mutualFundTransactionList := &networthTools.MutualFundTransactionList{
		TransactionDescription: mfTransactionDescription,
		Transactions:           orders,
	}
	byteResp, marshalErr := protoJson.Marshal(mutualFundTransactionList)
	if marshalErr != nil {
		return "", errors.Wrap(marshalErr, "error marshalling transaction details")
	}
	return string(byteResp), nil
}
