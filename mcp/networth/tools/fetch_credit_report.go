package tools

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	mcpEvents "github.com/epifi/gamma/mcp/networth/events"
	"github.com/epifi/gamma/pkg/obfuscator"
)

var (
	fetchCreditReportErrMsg = mcp.NewToolResultText("something went wrong in fetching credit report")
)

type FetchCreditReportHandler struct {
	creditReportClient creditReportPb.CreditReportManagerClient
	eventPublisher     *mcpEvents.EventPublisher
}

func NewFetchCreditReportHandler(creditReportClient creditReportPb.CreditReportManagerClient, eventPublisher *mcpEvents.EventPublisher) *FetchCreditReportHandler {
	return &FetchCreditReportHandler{
		creditReportClient: creditReportClient,
		eventPublisher:     eventPublisher,
	}
}

// GetTool returns tool with name, description and other metadata which gets used by mcp host
func (c *FetchCreditReportHandler) GetTool() mcp.Tool {
	return mcp.NewTool("fetch_credit_report",
		mcp.WithDescription("Retrieve comprehensive credit report including CIBIL/Experian scores, active loans, credit card utilization, payment history, and recent inquiries. If no credit score data is found for the user, ask them to connect credit score on the app and try again."),
	)
}

// Handle is the main handler call that gets called on each tool call
// nolint:dupl
func (c *FetchCreditReportHandler) Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	sessionId := server.ClientSessionFromContext(ctx).SessionID()
	obfuscatedSessId := obfuscator.Hashed(sessionId)
	actorId := epificontext.ActorIdFromContext(ctx)
	if actorId == epificontext.UnknownId {
		logger.Error(ctx, "actorId is not populated for credit report handler", zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchCreditReportErrMsg, nil
	}
	logger.Debug(ctx, "Received fetch credit report call", zap.String(logger.SESSION_ID, obfuscatedSessId))

	creditReportRes, fetchCreditReportErr := c.fetchCreditReportDetailsApiCall(ctx, actorId)
	if fetchCreditReportErr != nil {
		logger.Error(ctx, "error in fetching credit report", zap.Error(fetchCreditReportErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchCreditReportErrMsg, nil
	}

	if c.eventPublisher != nil {
		c.eventPublisher.PublishChatEvent(ctx, actorId, sessionId, true, false, false)
	}
	return mcp.NewToolResultText(creditReportRes), nil
}

func (c *FetchCreditReportHandler) fetchCreditReportDetailsApiCall(ctx context.Context, actorId string) (string, error) {
	creditReportRes, err := c.creditReportClient.GetCreditReports(ctx, &creditReportPb.GetCreditReportsRequest{
		ActorId: actorId,
		Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
		Limit:   1,
	})
	if rpcErr := epifigrpc.RPCError(creditReportRes, err); rpcErr != nil && !creditReportRes.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("failed to fetch credit report: %w", rpcErr)
	}
	// if credit report is not available ask user to connect
	if creditReportRes.GetStatus().IsRecordNotFound() || !checkIfAnyCreditReportDataIsPresent(creditReportRes) {
		return "connect your credit report to view this data", nil
	}
	byteResp, marshalErr := protojson.Marshal(creditReportRes.MinimalRespForLLM())
	if marshalErr != nil {
		return "", errors.Wrap(marshalErr, "error marshalling credit report response")
	}
	return string(byteResp), nil
}

func checkIfAnyCreditReportDataIsPresent(creditReportRes *creditReportPb.GetCreditReportsResponse) bool {
	for _, creditReport := range creditReportRes.GetCreditReports() {
		if creditReport.GetCreditReportData() != nil || creditReport.GetCreditReportRaw() != nil {
			return true
		}
	}
	return false
}
