package tools

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	creditReportMocks "github.com/epifi/gamma/api/creditreportv2/mocks"
	"github.com/epifi/gamma/api/vendorgateway/credit_report"
)

// mockSession implements server.ClientSession interface for testing
type mockSession struct {
	sessionID string
}

func (m *mockSession) Initialize()                                         {}
func (m *mockSession) Initialized() bool                                   { return true }
func (m *mockSession) NotificationChannel() chan<- mcp.JSONRPCNotification { return nil }
func (m *mockSession) SessionID() string                                   { return m.sessionID }

// createTestContext creates a context with actor ID and dummy session for testing
func createTestContext(actorId string) context.Context {
	ctx := epificontext.CtxWithActorId(context.Background(), actorId)

	// Create a mock MCP server and session to avoid nil pointer dereference
	mcpServer := server.NewMCPServer("test", "1.0.0")
	session := &mockSession{sessionID: "test-session-123"}

	return mcpServer.WithContext(ctx, session)
}

type fetchCreditReportMocks struct {
	mockCreditReportClient *creditReportMocks.MockCreditReportManagerClient
}

func initFetchCreditReportMocks(ctrl *gomock.Controller) *fetchCreditReportMocks {
	return &fetchCreditReportMocks{
		mockCreditReportClient: creditReportMocks.NewMockCreditReportManagerClient(ctrl),
	}
}

func TestNewFetchCreditReportHandler(t *testing.T) {
	t.Parallel()
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	m := initFetchCreditReportMocks(ctl)

	handler := NewFetchCreditReportHandler(m.mockCreditReportClient)

	assert.NotNil(t, handler)
	assert.Equal(t, m.mockCreditReportClient, handler.creditReportClient)
}

func TestFetchCreditReportHandler_GetTool(t *testing.T) {
	t.Parallel()
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	m := initFetchCreditReportMocks(ctl)

	handler := NewFetchCreditReportHandler(m.mockCreditReportClient)
	tool := handler.GetTool()

	assert.Equal(t, "fetch_credit_report", tool.Name)
	assert.Equal(t, "Retrieve comprehensive credit report including CIBIL/Experian scores, active loans, credit card utilization, payment history, and recent inquiries. If no credit score data is found for the user, ask them to connect credit score on the app and try again.", tool.Description)
}

func TestFetchCreditReportHandler_Handle(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()

	tests := []struct {
		name           string
		actorId        string
		setupMocks     func(m *fetchCreditReportMocks)
		expectError    bool
		expectedResult *mcp.CallToolResult
	}{
		{
			name:           "Missing actor ID",
			actorId:        epificontext.UnknownId,
			setupMocks:     func(m *fetchCreditReportMocks) {},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching credit report"),
		},
		{
			name:    "Successful fetch credit report",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusOk(),
					CreditReports: []*creditReportPb.CreditReportDownloadDetails{
						{
							Vendor: commonvgpb.Vendor_EXPERIAN,
							CreditReportData: &credit_report.CreditReportData{
								// Add some basic credit report data to make it valid
								Score: &credit_report.Score{
									BureauScore: "750",
								},
							},
						},
					},
				}, nil)
			},
			expectError:    false,
			expectedResult: nil, // Success case returns dynamic JSON, skip exact comparison
		},
		{
			name:    "Credit report RPC failure",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching credit report"),
		},
		{
			name:    "Credit report RPC returns nil response with error",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(nil, assert.AnError)
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching credit report"),
		},
		{
			name:    "Credit report record not found",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("connect your credit report to view this data"),
		},
		{
			name:    "Credit report has no data",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusOk(),
					CreditReports: []*creditReportPb.CreditReportDownloadDetails{
						{
							Vendor: commonvgpb.Vendor_EXPERIAN,
							// No CreditReportData or CreditReportRaw - empty report
						},
					},
				}, nil)
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("connect your credit report to view this data"),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initFetchCreditReportMocks(ctl)
			tt.setupMocks(m)

			handler := NewFetchCreditReportHandler(m.mockCreditReportClient)

			ctx := createTestContext(tt.actorId)
			result, err := handler.Handle(ctx, mcp.CallToolRequest{})

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, result)
				if tt.expectedResult != nil {
					assert.Equal(t, tt.expectedResult, result)
				} else {
					// For success cases where expectedResult is nil, verify we got valid content
					assert.NotNil(t, result.Content)
					assert.NotEmpty(t, result.Content)

					// Check what type we actually got for debugging
					if len(result.Content) > 0 && result.Content[0] != nil {
						// The result should be text content (JSON is returned as text)
						textContent, ok := result.Content[0].(mcp.TextContent)
						assert.True(t, ok, "Expected text content, got: %T", result.Content[0])
						if ok {
							assert.NotEmpty(t, textContent.Text, "Expected non-empty text result")
							// For JSON results, verify it starts with { and ends with }
							if len(textContent.Text) > 0 && textContent.Text[0] == '{' {
								assert.True(t, len(textContent.Text) > 2 && textContent.Text[len(textContent.Text)-1] == '}', "Expected valid JSON format")
							}
						}
					} else {
						assert.Fail(t, "result.Content is empty or nil")
					}
				}
			}
		})
	}
}

func TestFetchCreditReportHandler_fetchCreditReportDetailsApiCall(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()

	tests := []struct {
		name       string
		actorId    string
		setupMocks func(m *fetchCreditReportMocks)
		wantErr    bool
	}{
		{
			name:    "Successful API call",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusOk(),
					CreditReports: []*creditReportPb.CreditReportDownloadDetails{
						{
							Vendor: commonvgpb.Vendor_EXPERIAN,
						},
						{
							Vendor: commonvgpb.Vendor_EXPERIAN,
						},
					},
				}, nil)
			},
			wantErr: false,
		},
		{
			name:    "Credit report RPC returns internal error",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name:    "Credit report RPC returns network error",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
		{
			name:    "Credit report record not found - should not return error",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name:    "Empty credit reports list",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchCreditReportMocks) {
				m.mockCreditReportClient.EXPECT().GetCreditReports(gomock.Any(), &creditReportPb.GetCreditReportsRequest{
					ActorId: "test_actor_id",
					Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
					Limit:   1,
				}).Return(&creditReportPb.GetCreditReportsResponse{
					Status:        rpc.StatusOk(),
					CreditReports: []*creditReportPb.CreditReportDownloadDetails{},
				}, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initFetchCreditReportMocks(ctl)
			tt.setupMocks(m)

			handler := NewFetchCreditReportHandler(m.mockCreditReportClient)
			result, err := handler.fetchCreditReportDetailsApiCall(context.Background(), tt.actorId)

			if tt.wantErr {
				require.Error(t, err)
				assert.Empty(t, result)
			} else {
				require.NoError(t, err)
				assert.NotEmpty(t, result)
				// Verify that the result is valid JSON
				assert.NotEmpty(t, result)
			}
		})
	}
}
