package tools

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	analyserVariableMocks "github.com/epifi/gamma/api/analyser/variables/mocks"
	"github.com/epifi/gamma/api/analyser/variables/mutualfund"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	connectedAccountMocks "github.com/epifi/gamma/api/connected_account/mocks"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthMocks "github.com/epifi/gamma/api/insights/networth/mocks"
)

type fetchNetWorthMocks struct {
	mockNetWorthClient          *networthMocks.MockNetWorthClient
	mockVariableGeneratorClient *analyserVariableMocks.MockVariableGeneratorClient
	mockConnectedAccountClient  *connectedAccountMocks.MockConnectedAccountClient
}

func initFetchNetWorthMocks(ctrl *gomock.Controller) *fetchNetWorthMocks {
	return &fetchNetWorthMocks{
		mockNetWorthClient:          networthMocks.NewMockNetWorthClient(ctrl),
		mockVariableGeneratorClient: analyserVariableMocks.NewMockVariableGeneratorClient(ctrl),
		mockConnectedAccountClient:  connectedAccountMocks.NewMockConnectedAccountClient(ctrl),
	}
}

func TestNewFetchNetWorthHandler(t *testing.T) {
	t.Parallel()
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	m := initFetchNetWorthMocks(ctl)

	handler := NewFetchNetWorthHandler(m.mockNetWorthClient, m.mockVariableGeneratorClient, m.mockConnectedAccountClient, nil)

	assert.NotNil(t, handler)
	assert.Equal(t, m.mockNetWorthClient, handler.netWorthClient)
	assert.Equal(t, m.mockVariableGeneratorClient, handler.variableGeneratorClient)
	assert.Equal(t, m.mockConnectedAccountClient, handler.connectedAccountClient)
}

func TestFetchNetWorthHandler_GetTool(t *testing.T) {
	t.Parallel()
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	m := initFetchNetWorthMocks(ctl)

	handler := NewFetchNetWorthHandler(m.mockNetWorthClient, m.mockVariableGeneratorClient, m.mockConnectedAccountClient, nil)
	tool := handler.GetTool()

	assert.Equal(t, "fetch_net_worth", tool.Name)
	assert.Equal(t, "Calculate comprehensive net worth with detailed asset and liability breakdown including investments (mutual funds, stocks, EPF, etc.), real estate, bank accounts, loans, and credit card debt.", tool.Description)
}

func TestFetchNetWorthHandler_Handle(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()

	tests := []struct {
		name           string
		actorId        string
		setupMocks     func(m *fetchNetWorthMocks)
		expectError    bool
		expectedResult *mcp.CallToolResult
	}{
		{
			name:           "Missing actor ID",
			actorId:        epificontext.UnknownId,
			setupMocks:     func(m *fetchNetWorthMocks) {},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching net worth"),
		},
		{
			name:    "Successful fetch net worth",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchNetWorthMocks) {
				// Mock NetWorth client
				m.mockNetWorthClient.EXPECT().GetNetWorthValue(gomock.Any(), &networthPb.GetNetWorthValueRequest{
					ActorId: "test_actor_id",
				}).Return(&networthPb.GetNetWorthValueResponse{
					Status: rpc.StatusOk(),
				}, nil)

				// Mock Variable Generator client
				m.mockVariableGeneratorClient.EXPECT().GetAnalysisVariables(gomock.Any(), &analyserVariablePb.GetAnalysisVariablesRequest{
					ActorId:               "test_actor_id",
					AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
				}).Return(&analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{
						"ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS": {
							Variable: &analyserVariablePb.AnalysisVariable_MfSecretsSchemeAnalytics{
								MfSecretsSchemeAnalytics: &mutualfund.MfSchemeAnalytics{},
							},
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccounts
				m.mockConnectedAccountClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
					ActorId: "test_actor_id",
					AccountFilterList: []connectedAccountExternalPb.AccountFilter{
						connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
				}).Return(&connectedAccountPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*connectedAccountExternalPb.AccountDetails{
						{
							AccountId: "account_1",
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccountDetailsBulk
				m.mockConnectedAccountClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), &connectedAccountPb.GetAccountDetailsBulkRequest{
					AccountIdList: []string{"account_1"},
					AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
					},
				}).Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			expectError:    false,
			expectedResult: nil, // Success case returns dynamic JSON, skip exact comparison
		},
		{
			name:    "NetWorth RPC failure",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchNetWorthMocks) {
				// Mock NetWorth client with error
				m.mockNetWorthClient.EXPECT().GetNetWorthValue(gomock.Any(), &networthPb.GetNetWorthValueRequest{
					ActorId: "test_actor_id",
				}).Return(&networthPb.GetNetWorthValueResponse{
					Status: rpc.StatusInternal(),
				}, nil)

				// Mock Variable Generator client
				m.mockVariableGeneratorClient.EXPECT().GetAnalysisVariables(gomock.Any(), &analyserVariablePb.GetAnalysisVariablesRequest{
					ActorId:               "test_actor_id",
					AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
				}).Return(&analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{
						"ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS": {
							Variable: &analyserVariablePb.AnalysisVariable_MfSecretsSchemeAnalytics{
								MfSecretsSchemeAnalytics: &mutualfund.MfSchemeAnalytics{},
							},
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccounts
				m.mockConnectedAccountClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
					ActorId: "test_actor_id",
					AccountFilterList: []connectedAccountExternalPb.AccountFilter{
						connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
				}).Return(&connectedAccountPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*connectedAccountExternalPb.AccountDetails{
						{
							AccountId: "account_1",
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccountDetailsBulk
				m.mockConnectedAccountClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), &connectedAccountPb.GetAccountDetailsBulkRequest{
					AccountIdList: []string{"account_1"},
					AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
					},
				}).Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching net worth"),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initFetchNetWorthMocks(ctl)
			tt.setupMocks(m)

			handler := NewFetchNetWorthHandler(m.mockNetWorthClient, m.mockVariableGeneratorClient, m.mockConnectedAccountClient, nil)

			ctx := createTestContext(tt.actorId)
			result, err := handler.Handle(ctx, mcp.CallToolRequest{})

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, result)
				if tt.expectedResult != nil {
					assert.Equal(t, tt.expectedResult, result)
				} else {
					// For success cases where expectedResult is nil, verify we got valid content
					assert.NotNil(t, result.Content)
					assert.NotEmpty(t, result.Content)

					// Check what type we actually got for debugging
					if len(result.Content) > 0 && result.Content[0] != nil {
						// The result should be text content (JSON is returned as text)
						textContent, ok := result.Content[0].(mcp.TextContent)
						assert.True(t, ok, "Expected text content, got: %T", result.Content[0])
						if ok {
							assert.NotEmpty(t, textContent.Text, "Expected non-empty text result")
							// For JSON results, verify it starts with { and ends with }
							if len(textContent.Text) > 0 && textContent.Text[0] == '{' {
								assert.True(t, len(textContent.Text) > 2 && textContent.Text[len(textContent.Text)-1] == '}', "Expected valid JSON format")
							}
						}
					} else {
						assert.Fail(t, "result.Content is empty or nil")
					}
				}
			}
		})
	}
}

func TestFetchNetWorthHandler_fetchNetWorthAndHoldingsApiCall(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()

	tests := []struct {
		name       string
		actorId    string
		setupMocks func(m *fetchNetWorthMocks)
		wantErr    bool
	}{
		{
			name:    "Successful API call with all services returning data",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchNetWorthMocks) {
				// Mock NetWorth client
				m.mockNetWorthClient.EXPECT().GetNetWorthValue(gomock.Any(), &networthPb.GetNetWorthValueRequest{
					ActorId: "test_actor_id",
				}).Return(&networthPb.GetNetWorthValueResponse{
					Status: rpc.StatusOk(),
				}, nil)

				// Mock Variable Generator client
				m.mockVariableGeneratorClient.EXPECT().GetAnalysisVariables(gomock.Any(), &analyserVariablePb.GetAnalysisVariablesRequest{
					ActorId:               "test_actor_id",
					AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
				}).Return(&analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{
						"ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS": {
							Variable: &analyserVariablePb.AnalysisVariable_MfSecretsSchemeAnalytics{
								MfSecretsSchemeAnalytics: &mutualfund.MfSchemeAnalytics{},
							},
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccounts
				m.mockConnectedAccountClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
					ActorId: "test_actor_id",
					AccountFilterList: []connectedAccountExternalPb.AccountFilter{
						connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
				}).Return(&connectedAccountPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*connectedAccountExternalPb.AccountDetails{
						{
							AccountId: "account_1",
						},
						{
							AccountId: "account_2",
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccountDetailsBulk
				m.mockConnectedAccountClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), &connectedAccountPb.GetAccountDetailsBulkRequest{
					AccountIdList: []string{"account_1", "account_2"},
					AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
					},
				}).Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name:    "NetWorth RPC returns error",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchNetWorthMocks) {
				// Mock NetWorth client with error
				m.mockNetWorthClient.EXPECT().GetNetWorthValue(gomock.Any(), &networthPb.GetNetWorthValueRequest{
					ActorId: "test_actor_id",
				}).Return(nil, assert.AnError)

				// Mock Variable Generator client
				m.mockVariableGeneratorClient.EXPECT().GetAnalysisVariables(gomock.Any(), &analyserVariablePb.GetAnalysisVariablesRequest{
					ActorId:               "test_actor_id",
					AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
				}).Return(&analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{
						"ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS": {
							Variable: &analyserVariablePb.AnalysisVariable_MfSecretsSchemeAnalytics{
								MfSecretsSchemeAnalytics: &mutualfund.MfSchemeAnalytics{},
							},
						},
					},
				}, nil)

				// Mock Connected Account client - GetAccounts (may or may not be called due to concurrent execution)
				m.mockConnectedAccountClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
					ActorId: "test_actor_id",
					AccountFilterList: []connectedAccountExternalPb.AccountFilter{
						connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
				}).Return(&connectedAccountPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*connectedAccountExternalPb.AccountDetails{
						{
							AccountId: "account_1",
						},
					},
				}, nil).AnyTimes()

				// Mock Connected Account client - GetAccountDetailsBulk (may be called due to concurrent execution)
				m.mockConnectedAccountClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), &connectedAccountPb.GetAccountDetailsBulkRequest{
					AccountIdList: []string{"account_1"},
					AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
						connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
					},
				}).Return(&connectedAccountPb.GetAccountDetailsBulkResponse{
					Status: rpc.StatusOk(),
				}, nil).AnyTimes()
			},
			wantErr: true,
		},
		{
			name:    "No accounts found - record not found",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchNetWorthMocks) {
				// Mock NetWorth client
				m.mockNetWorthClient.EXPECT().GetNetWorthValue(gomock.Any(), &networthPb.GetNetWorthValueRequest{
					ActorId: "test_actor_id",
				}).Return(&networthPb.GetNetWorthValueResponse{
					Status: rpc.StatusOk(),
				}, nil)

				// Mock Variable Generator client without MF analytics
				m.mockVariableGeneratorClient.EXPECT().GetAnalysisVariables(gomock.Any(), &analyserVariablePb.GetAnalysisVariablesRequest{
					ActorId:               "test_actor_id",
					AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
				}).Return(&analyserVariablePb.GetAnalysisVariablesResponse{
					Status:              rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{},
				}, nil)

				// Mock Connected Account client - GetAccounts returns record not found
				m.mockConnectedAccountClient.EXPECT().GetAccounts(gomock.Any(), &connectedAccountPb.GetAccountsRequest{
					ActorId: "test_actor_id",
					AccountFilterList: []connectedAccountExternalPb.AccountFilter{
						connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
				}).Return(&connectedAccountPb.GetAccountsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initFetchNetWorthMocks(ctl)
			tt.setupMocks(m)

			handler := NewFetchNetWorthHandler(m.mockNetWorthClient, m.mockVariableGeneratorClient, m.mockConnectedAccountClient, nil)
			_, err := handler.fetchNetWorthAndHoldingsApiCall(context.Background(), tt.actorId)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
