package tools

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	beEpfMocks "github.com/epifi/gamma/api/insights/epf/mocks"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	mfExternalMocks "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
)

type mocks struct {
	mockEpfClient   *beEpfMocks.MockEpfClient
	mockMfTxnClient *mfExternalMocks.MockMFExternalOrdersClient
}

func initMocks(ctrl *gomock.Controller) *mocks {
	return &mocks{
		mockEpfClient:   beEpfMocks.NewMockEpfClient(ctrl),
		mockMfTxnClient: mfExternalMocks.NewMockMFExternalOrdersClient(ctrl),
	}
}

func TestFetchTransactionsHandler_fetchAllTransactionsApiCall(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()
	tests := []struct {
		name       string
		setupMocks func(m *mocks)
		wantErr    bool
	}{
		{
			name: "successfully get all mf transactions",
			setupMocks: func(m *mocks) {
				m.mockMfTxnClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), &mfExternalPb.FetchAllExternalMFOrdersRequest{
					ActorId:            "actor_id",
					PageContextRequest: &rpc.PageContextRequest{PageSize: 1000},
				}).Return(&mfExternalPb.FetchAllExternalMFOrdersResponse{
					Status:                   rpc.StatusOk(),
					MutualFundExternalOrders: []*mfExternalPb.MutualFundExternalOrder{{Id: "mf_txn_1"}},
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "MFTransactions returns non-RecordNotFound error",
			setupMocks: func(m *mocks) {
				m.mockMfTxnClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), &mfExternalPb.FetchAllExternalMFOrdersRequest{
					ActorId:            "actor_id",
					PageContextRequest: &rpc.PageContextRequest{PageSize: 1000},
				}).Return(&mfExternalPb.FetchAllExternalMFOrdersResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "MFTransactions returns RecordNotFound status",
			setupMocks: func(m *mocks) {
				m.mockMfTxnClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), &mfExternalPb.FetchAllExternalMFOrdersRequest{
					ActorId:            "actor_id",
					PageContextRequest: &rpc.PageContextRequest{PageSize: 1000},
				}).Return(&mfExternalPb.FetchAllExternalMFOrdersResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "MFTransactions returns nil response and error",
			setupMocks: func(m *mocks) {
				m.mockMfTxnClient.EXPECT().FetchAllExternalMFOrders(gomock.Any(), &mfExternalPb.FetchAllExternalMFOrdersRequest{
					ActorId:            "actor_id",
					PageContextRequest: &rpc.PageContextRequest{PageSize: 1000},
				}).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.setupMocks(m)
			h := NewFetchTransactionsHandler(m.mockMfTxnClient, nil)
			_, err := h.fetchAllTransactionsApiCall(context.Background(), "actor_id")
			if (err != nil) != tt.wantErr {
				t.Errorf("fetchAllTransactionsApiCall() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
