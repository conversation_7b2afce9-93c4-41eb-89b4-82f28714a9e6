// nolint
package networth

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	analyserVariableMocks "github.com/epifi/gamma/api/analyser/variables/mocks"
	sessionMocks "github.com/epifi/gamma/api/auth/session/mocks"
	connectedAccountMocks "github.com/epifi/gamma/api/connected_account/mocks"
	creditReportMocks "github.com/epifi/gamma/api/creditreportv2/mocks"
	beEpfMocks "github.com/epifi/gamma/api/insights/epf/mocks"
	networthMocks "github.com/epifi/gamma/api/insights/networth/mocks"
	mfExternalMocks "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
)

func TestCreateMcpServer(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize mock clients
	mockNetWorthClient := networthMocks.NewMockNetWorthClient(ctrl)
	mockSessionManagerClient := sessionMocks.NewMockSessionManagerClient(ctrl)
	mockVariableGeneratorClient := analyserVariableMocks.NewMockVariableGeneratorClient(ctrl)
	mockConnectedAccountClient := connectedAccountMocks.NewMockConnectedAccountClient(ctrl)
	mockCreditReportClient := creditReportMocks.NewMockCreditReportManagerClient(ctrl)
	mockMfExternalOrdersClient := mfExternalMocks.NewMockMFExternalOrdersClient(ctrl)
	mockEpfClient := beEpfMocks.NewMockEpfClient(ctrl)

	// Create MCP server
	server := CreateMcpServer(
		mockNetWorthClient,
		mockSessionManagerClient,
		mockVariableGeneratorClient,
		mockConnectedAccountClient,
		mockCreditReportClient,
		mockMfExternalOrdersClient,
		mockEpfClient,
		nil,
	)

	// Assert server is created
	require.NotNil(t, server)

	// Basic verification that server is created with proper components
	// MCP server doesn't expose metadata methods like GetServerInfo()
	assert.NotNil(t, server)
}

func TestCreateMcpServerWithNilClients(t *testing.T) {
	t.Parallel()

	// Test with nil clients - should not panic
	server := CreateMcpServer(nil, nil, nil, nil, nil, nil, nil, nil)
	require.NotNil(t, server)
}
