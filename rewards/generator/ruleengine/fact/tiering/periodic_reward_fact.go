package tiering

import (
	"fmt"

	"github.com/Knetic/govaluate"

	"github.com/epifi/be-common/pkg/epifigrpc"

	datacollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/helper"
)

type TieringPeriodicRewardFact struct {
	*common.CommonFact
	AccountTieringClient       tieringPb.TieringClient
	TieringPeriodicRewardEvent *datacollectorPb.TieringPeriodicRewardEvent
}

func (t *TieringPeriodicRewardFact) GetExpressionFunctionMap() map[string]govaluate.ExpressionFunction {
	expressionFunctionMap := map[string]govaluate.ExpressionFunction{
		// IsUserEligibleForCashbackReward returns true if the user is eligible for tiering cashback reward
		"IsUserEligibleForCashbackReward": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("IsUserEligibleForCashbackReward: expected 1 argument, got %d", len(args))
			}
			tierStr, ok := args[0].(string)
			if !ok {
				return nil, fmt.Errorf("IsUserEligibleForCashbackReward: incorrect argument type: %T", args[0])
			}
			tier := external.Tier(external.Tier_value[tierStr])
			if tier == external.Tier_TIER_UNSPECIFIED {
				return nil, fmt.Errorf("IsUserEligibleForCashbackReward: invalid tier received: %s", tierStr)
			}
			isUserEligibleRes, err := t.AccountTieringClient.CheckIfActorIsEligibleForCashbackReward(t.GetCtx(), &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
				ActorId: t.GetActorId(),
				Tier:    tier,
			})
			if rpcErr := epifigrpc.RPCError(isUserEligibleRes, err); rpcErr != nil {
				return nil, fmt.Errorf("error while calling accountTieringClient.CheckIfActorIsEligibleForCashbackReward(), err: %w", rpcErr)
			}

			return isUserEligibleRes.GetIsEligible(), nil
		},
	}
	return helper.MergeExpressionFunctionMap(expressionFunctionMap, t.CommonFact.GetExpressionFunctionMap())
}

func (t *TieringPeriodicRewardFact) GetExpressionParametersMap() map[string]interface{} {
	return map[string]interface{}{}
}
