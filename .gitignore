# Generic entries to exclude compiled files
.DS_Store
*.[56789ao]
*.a[56789o]
*.so
*.pyc
._*
.nfs.*
[56789a].out
*~
*.orig
*.rej
*.exe
.*.swp
*.ipynb
core

# gamma dev binaries pattern
*_bin


# For artifacts of Go build that should not be checked in.
# Go specific binaries
*.cgo*.go
*.cgo*.c
_cgo_*
_obj
_test
_testmain.go
vendor/
*.test

/VERSION.cache
/bin/
/build.out
/doc/articles/wiki/*.bin
/goinstall.log
/last-change
/misc/cgo/life/run.out
/misc/cgo/stdio/run.out
/misc/cgo/testso/main
/src/*.*/
/src/cmd/cgo/zdefaultcc.go
/src/cmd/dist/dist
/src/cmd/go/internal/cfg/zdefaultcc.go
/src/cmd/go/internal/cfg/zosarch.go
/src/cmd/internal/objabi/zbootstrap.go
/src/go/build/zcgo.go
/src/go/doc/headscan
/src/runtime/internal/sys/zversion.go
/src/unicode/maketables
/test.out
/test/garbage/*.out
/test/pass.out
/test/run.out
/test/times.out

# Custom ignored files
CODEOWNERS

# For files created by specific development environment (e.g. editor),
.code/
.vscode/
*.code-workspace
.idea/

# Bazel files
bazel-*

# Local cockroach DB files
*/cockroach-data/*
cockroach-data/*
cockroach-v23.2.20.darwin-10.9-amd64/*

# Crdb secrets folder
crdb/

# Proto files (should only be part of protos repo)
*.proto

# Build Output
output/

# Android project files
android/.project
android/.settings

# Code coverage files
code-coverage.*

# Pem files
*.pem

# search indexing data
search/indexer/data

# Acceptance output
testing/integration/output

.spr.yml

# Script outputs
/scripts/fit/cricket/roanuz/cricket_token.txt
/scripts/fit/cricket/roanuz/football_token.txt
/usstocks/scripts/logos/api_key.go
/usstocks/scripts/logos/api_key.txt
/usstocks/scripts/logos/csv/**
scratch.json
