package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	"github.com/google/wire"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	authTokenPb "github.com/epifi/gamma/api/auth/token"
	"github.com/epifi/gamma/auth/dao/model"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

//go:generate mockgen -source=./dao.go -destination=./mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .
var WireSet = wire.NewSet(
	NewTokenStoresPgdb,
	NewTokenStoresCache,
	ProvideTokenStoresDao,
	ProvideTokenStoresDBDao,
	ProvideTokenStoresDBDaoV2,
	ProvideDeviceIntegrityNonceDao,
	ProvideDeviceIntegrityNonceCacheDao,
	NewVendorOtpDao, wire.Bind(new(VendorOtpDao), new(*VendorOtpDaoCrdb)))
var DeviceAttestationDaoWireSet = wire.NewSet(NewIosDeviceAttestationDaoCrdb, wire.Bind(new(IosDeviceAttestationDao), new(*IosDeviceAttestationDaoCrdb)))
var OAuthSignupDataDaoWireSet = wire.NewSet(NewOAuthSignupDataDaoCRDB, wire.Bind(new(OAuthSignupDataDao), new(*OAuthSignupDataDaoCRDB)))
var DeviceIntegrityResultDaoWireSet = wire.NewSet(NewDeviceIntegrityResultDaoCrdb, wire.Bind(new(DeviceIntegrityResultDao), new(*DeviceIntegrityResultDaoCrdb)))
var AuthAttemptsDaoWireSet = wire.NewSet(NewAuthAttemptsCrdb, wire.Bind(new(AuthAttemptsDao), new(*AuthAttemptsCrdb)))
var AuthFactorUpdateDaoWireSet = wire.NewSet(NewAfuCrdbDao, wire.Bind(new(AuthFactorUpdateDao), new(*AfuCrdbDao)))
var DeviceRegistrationDaoWireSet = wire.NewSet(NewDeviceRegistrationDaoCrdb, NewDeviceRegistrationCache, wire.Bind(new(DeviceRegistrationDao), new(*DeviceRegistrationCache)))

type AuthAttemptsDao interface {
	Create(ctx context.Context, authAttemptData *authPb.AuthAttemptData) (*authPb.AuthAttemptData, error)
	GetAuthAttemptsByPhoneNumber(ctx context.Context, phoneNumber *commontypes.PhoneNumber, toTime *time.Time, count int64) ([]*authPb.AuthAttemptData, error)
}

type AuthFactorUpdateDao interface {
	Create(ctx context.Context, attempt *afu.AuthFactorUpdate) (*afu.AuthFactorUpdate, error)
	GetById(ctx context.Context, id string, opts ...interface{}) (*afu.AuthFactorUpdate, error)
	GetByIdWithLock(ctx context.Context, id string) (*afu.AuthFactorUpdate, error)
	UpdateContextAndStatus(ctx context.Context, id string, c *afu.Context, s afu.OverallStatus) (*afu.AuthFactorUpdate, error)
	DeleteByActor(ctx context.Context, actorId string) error
	GetByActor(ctx context.Context, actorId string, limit int, filters ...storageV2.FilterOption) ([]*afu.AuthFactorUpdate, error)
	GetByDeviceReRegRequestId(ctx context.Context, reqId string) (*afu.AuthFactorUpdate, error)
	UpdateContextAndVendorContext(ctx context.Context, afuId string, afuCtx *afu.Context, vendorCtx *afu.VendorContext) (*afu.AuthFactorUpdate, error)
	UpdateVendorContext(ctx context.Context, afuId string, vendorCtx *afu.VendorContext) (*afu.AuthFactorUpdate, error)
	UpdateVendorContextAndStatus(ctx context.Context, afuId string, vendorCtx *afu.VendorContext, status afu.OverallStatus) (*afu.AuthFactorUpdate, error)
	GetByStatusAndAuthFactors(ctx context.Context, status afu.OverallStatus, phone, device, email string) ([]*afu.AuthFactorUpdate, error)
	UpdateStatusAndFailureReasonByAFUIDs(ctx context.Context, status afu.OverallStatus, failureReason afu.FailureReason, afuIDs []string) error
	UpdateAFUByFields(ctx context.Context, o *afu.AuthFactorUpdate, updateMask []afu.AuthFactorUpdateFieldMask) error
}

type AFUCache interface {
	GetAFUSummaries(ctx context.Context, actorId string) ([]*afu.AFUSummary, error)
	UpdateAFUSummaries(ctx context.Context, actorId string, summary *afu.AFUSummary) error
}

type DeviceRegistrationAttemptsDao interface {
	Create(ctx context.Context, attempt *authPb.DeviceRegistrationAttempt) (attemptPb *authPb.DeviceRegistrationAttempt, err error)
	GetLastAttemptByActor(ctx context.Context, actorId string) (*authPb.DeviceRegistrationAttempt, error)
	GetAttemptById(ctx context.Context, attemptId string) (*authPb.DeviceRegistrationAttempt, error)
	UpdateDeviceRegistrationStatus(ctx context.Context, attemptId string, status authPb.DeviceRegistrationStatus) error
	GetAttemptsByActorId(ctx context.Context, actorId string) ([]*authPb.DeviceRegistrationAttempt, error)
	UpdateSmsInfo(ctx context.Context, actorId string, smsInfo *authPb.DeviceRegistrationAttempt_SmsInfo) error
}

type DeviceIntegrityNonceDao interface {
	CreateNonce(ctx context.Context, deviceId, manufacturer, nonce string) error
	GetActiveNonce(ctx context.Context, deviceId, manufacturer, nonce string) (*model.DeviceIntegrityNonce, error)
	UpdateNonceStatus(ctx context.Context, nonce, manufacturer, deviceId string, status authPb.NonceStatus) error
}

type DeviceIntegrityResultDao interface {
	Create(ctx context.Context, res *authPb.DeviceIntegrityResult) (*authPb.DeviceIntegrityResult, error)
	GetResultsByDeviceId(ctx context.Context, deviceId string, limit uint) ([]*authPb.DeviceIntegrityResult, error)
}

type DeviceRegistrationDao interface {
	CreateRegistrationEntry(ctx context.Context, d *authPb.DeviceRegistration) error
	UpdateDeviceRegistrationByFilter(ctx context.Context, d *authPb.DeviceRegistration, updateMask []authPb.DeviceRegisterationUpdateFieldMask) error
	GetDeviceRegistration(ctx context.Context, actorId, deviceId string, opts ...*model.OptionalParams) (*authPb.DeviceRegistration, error)
	GetDeviceRegistrations(ctx context.Context, actorId, deviceId string) ([]*authPb.DeviceRegistration, error)
	GetDeletedDeviceRegistration(ctx context.Context, actorId, deviceId string) (*authPb.DeviceRegistration, error)
	GetDeviceRegInLast1Day(ctx context.Context, deviceId string) (int64, error)
	GetFirstDeviceRegAfterTime(ctx context.Context, actorId string, fromTime time.Time) (*authPb.DeviceRegistration, error)
	DeleteDeviceRegistration(ctx context.Context, actorId, deviceId string) error
}

type IosDeviceAttestationDao interface {
	CreateIosAttestationData(ctx context.Context, attstData *authPb.IosDeviceAttestationData) (*authPb.IosDeviceAttestationData, error)
	GetIosAttestationDataEntryByDeviceId(ctx context.Context, deviceId string) (*authPb.IosDeviceAttestationData, error)
	GetIosAttestationDataEntriesByDeviceId(ctx context.Context, deviceId string, includeDeleted bool, limit int) ([]*authPb.IosDeviceAttestationData, error)
	GetIosAttestationDataEntryByPublicKey(ctx context.Context, publicKey []byte) (*authPb.IosDeviceAttestationData, error)
	SoftDeleteAttestationDataByDeviceId(ctx context.Context, deviceId string) error
	UpdateAttestationCounter(ctx context.Context, deviceId string, publicKey []byte, counter int32) error
}

type OAuthSignupDataDao interface {
	Create(ctx context.Context, signupData *authPb.OAuthSignupData) (*authPb.OAuthSignupData, error)
	GetByOauthUserIdAndOAuthProvider(ctx context.Context, oauthUserId string, oauthProvider authPb.OAuthProvider) (*authPb.OAuthSignupData, error)
	SoftDeleteOAuthSignUpRecord(ctx context.Context, oauthUserId string) error
	UpdateRefreshTokenValidatedTime(ctx context.Context, oauthUserId string) error
	GetByOAuthEmail(ctx context.Context, oauthEmail string, recentRecords int, softDeleted bool) ([]*authPb.OAuthSignupData, error)
}

//go:generate dao_metrics_gen /
type TokenStoresDao interface {
	StoreToken(ctx context.Context, t *model.TokenStore, params ...*model.StoreTokenParams) error
	GetTokenById(ctx context.Context, id string) (*model.TokenStore, error)
	GetLatestTokenByActorIdAndTokenType(ctx context.Context, actorId string, tokenType authPb.TokenType, includeDeleted bool) (*model.TokenStore, error)
	GetTokensByPhoneNumber(ctx context.Context, ph *commontypes.PhoneNumber, tokenType []authPb.TokenType, includeDeleted bool, size uint) ([]*model.TokenStore, error)
	ExpireTokenByActorId(ctx context.Context, actorId string, idNotToBeExpired string, tokenType authPb.TokenType, deletionReason authPb.TokenDeletionReason, deviceRegStatuses ...authPb.DeviceRegistrationStatus) ([]*model.TokenStore, error)
	ExpireTokenByPhoneNumber(ctx context.Context, tokenType authPb.TokenType, status model.TokenStatus, phoneNum *commontypes.PhoneNumber, deletionReason authPb.TokenDeletionReason, exceptIds ...string) ([]*model.TokenStore, error)
	ExpireToken(ctx context.Context, id string, deletionReason authPb.TokenDeletionReason) (*model.TokenStore, error)
}

type TokenStoresDBDao interface {
	TokenStoresDao
}

type TokenStoresCacheDao interface {
	TokenStoresDao
}

type DeviceIntegrityNonceCacheDao interface {
	DeviceIntegrityNonceDao
}

type VendorOtpDao interface {
	CreateVendorOtp(ctx context.Context, vendorOtp *authPb.VendorOtp) (*authPb.VendorOtp, error)
}

// TokenStoresDaoV2 - data layer for Token Stores
// This dao interface defines methods which interact with Cache/DB/cache-only/DB-only/(Cache+DB)
// Each method param will dictate whether the call will use cache only, db only, or both
// Default behaviour for each method related to which data storage it accesses will also be different
type TokenStoresDaoV2 interface {
	// StoreTokenById - Store token in db and cache by id
	StoreTokenById(ctx context.Context, t *model.TokenStore, params ...*model.StoreTokenParams) error
	GetTokenById(ctx context.Context, id string, params ...*model.GetTokenByIdParams) (*model.TokenStore, error)
	ExpireTokenById(ctx context.Context, id string, deletionReason authPb.TokenDeletionReason, params ...*model.ExpireTokenByIdParams) (*model.TokenStore, error)

	// StoreTokenBySubject - Store token in db and cache by subject
	StoreTokenBySubject(ctx context.Context, t *model.TokenStore, params ...*model.StoreTokenParams) error
	GetTokenBySubjectAndTokenType(ctx context.Context, subject string, subjectType authTokenPb.SubjectType, tokenType authPb.TokenType, params ...*model.GetTokenBySubjectAndTokenTypeParams) (*model.TokenStore, error)
	ExpireTokenBySubject(ctx context.Context, subject string, subjectType authTokenPb.SubjectType, tokenType authPb.TokenType, deletionReason authPb.TokenDeletionReason, params ...*model.ExpireTokensBySubjectParams) ([]*model.TokenStore, error)

	GetLatestTokenByActorIdAndTokenType(ctx context.Context, actorId string, tokenType authPb.TokenType, includeDeleted bool, params ...*model.GetLatestTokenByActorIdAndTokenTypeParams) (*model.TokenStore, error)
	GetTokensByPhoneNumber(ctx context.Context, ph *commontypes.PhoneNumber, tokenType []authPb.TokenType, includeDeleted bool, size uint, params ...*model.GetTokensByPhoneNumberParams) ([]*model.TokenStore, error)

	ExpireTokenByActorId(ctx context.Context, actorId string, idNotToBeExpired string, tokenType authPb.TokenType, deletionReason authPb.TokenDeletionReason, deviceRegStatuses []authPb.DeviceRegistrationStatus, params ...*model.ExpireTokensByActorIdParams) ([]*model.TokenStore, error)
	ExpireTokenByPhoneNumber(ctx context.Context, tokenType authPb.TokenType, status model.TokenStatus, phoneNum *commontypes.PhoneNumber, deletionReason authPb.TokenDeletionReason, exceptIds []string, params ...*model.ExpireTokensByPhoneNumberParams) ([]*model.TokenStore, error)
}

type TokenStoresDBDaoV2 interface {
	StoreToken(ctx context.Context, t *model.TokenStore, params ...*model.StoreTokenParams) error

	GetTokenById(ctx context.Context, id string) (*model.TokenStore, error)
	GetLatestTokenByActorIdAndTokenType(ctx context.Context, actorId string, tokenType authPb.TokenType, includeDeleted bool) (*model.TokenStore, error)
	GetTokensByPhoneNumber(ctx context.Context, ph *commontypes.PhoneNumber, tokenType []authPb.TokenType, includeDeleted bool, size uint) ([]*model.TokenStore, error)

	ExpireToken(ctx context.Context, id string, deletionReason authPb.TokenDeletionReason) (*model.TokenStore, error)
	ExpireTokenByActorId(ctx context.Context, actorId string, idNotToBeExpired string, tokenType authPb.TokenType, deletionReason authPb.TokenDeletionReason, deviceRegStatuses ...authPb.DeviceRegistrationStatus) ([]*model.TokenStore, error)
	ExpireTokenByPhoneNumber(ctx context.Context, tokenType authPb.TokenType, status model.TokenStatus, phoneNum *commontypes.PhoneNumber, deletionReason authPb.TokenDeletionReason, exceptIds ...string) ([]*model.TokenStore, error)
	ExpireTokenBySubject(ctx context.Context, subject string, subjectType authTokenPb.SubjectType, tokenType authPb.TokenType, deletionReason authPb.TokenDeletionReason) ([]*model.TokenStore, error)
}

type TokenStoresSubjectCacheDao interface {
	StoreToken(context.Context, *model.TokenStore, ...*model.StoreTokenParams) error
	GetToken(ctx context.Context, subject string, subjectType authTokenPb.SubjectType, tokenType authPb.TokenType) (*model.TokenStore, error)
	ExpireToken(ctx context.Context, subject string, subjectType authTokenPb.SubjectType, tokenType authPb.TokenType) error
}

type TokenStoresIdCacheDao interface {
	StoreToken(context.Context, *model.TokenStore, ...*model.StoreTokenParams) error
	GetToken(ctx context.Context, id string) (*model.TokenStore, error)
	ExpireToken(ctx context.Context, id string) error
}

func ProvideTokenStoresDao(cache *TokenStoresCache) TokenStoresDao {
	return cache
}

func ProvideDeviceIntegrityNonceCacheDao(cache *DeviceIntegrityNonceCache) DeviceIntegrityNonceCacheDao {
	return cache
}

func ProvideDeviceIntegrityNonceDao(cache DeviceIntegrityNonceCacheDao) DeviceIntegrityNonceDao {
	return cache
}

func ProvideTokenStoresDBDao(pgdb *TokenStoresPgdb) TokenStoresDBDao {
	return pgdb
}

func ProvideTokenStoresDBDaoV2(pgdb *TokenStoresPgdb) TokenStoresDBDaoV2 {
	return pgdb
}
