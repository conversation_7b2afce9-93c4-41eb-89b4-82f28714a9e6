package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/auth/dao/model"
)

type DeviceRegistrationAttemptsCrdb struct {
	Db dbTypes.EpifiCRDB
}

func NewDeviceRegistrationAttempts(db dbTypes.EpifiCRDB) *DeviceRegistrationAttemptsCrdb {
	return &DeviceRegistrationAttemptsCrdb{Db: db}
}

var _ DeviceRegistrationAttemptsDao = &DeviceRegistrationAttemptsCrdb{}

var DeviceRegistrationAttemptGormDaoWireSet = wire.NewSet(NewDeviceRegistrationAttempts, wire.Bind(new(DeviceRegistrationAttemptsDao), new(*DeviceRegistrationAttemptsCrdb)))

func (d *DeviceRegistrationAttemptsCrdb) Create(ctx context.Context, attemptPb *authPb.DeviceRegistrationAttempt) (*authPb.DeviceRegistrationAttempt, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceRegistrationAttemptsCrdb", "Create", time.Now())
	attemptModel := Marshal(attemptPb)
	db := gormctxv2.FromContextOrDefault(ctx, d.Db)
	res := db.Create(attemptModel)
	if res.Error != nil {
		logger.Error(ctx, "failed to create device registration attempt", zap.Error(res.Error))
		return nil, res.Error
	}
	return Unmarshal(res.Statement.Model.(*model.DeviceRegAttempt)), nil
}

func (d *DeviceRegistrationAttemptsCrdb) GetLastAttemptByActor(ctx context.Context, actorId string) (*authPb.DeviceRegistrationAttempt, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceRegistrationAttemptsCrdb", "GetLastAttemptByActor", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.Db)
	if actorId == "" {
		return nil, fmt.Errorf("actor id cannot be empty")
	}
	attemptPb := &model.DeviceRegAttempt{}
	if err := db.Model(&model.DeviceRegAttempt{}).Where(&model.DeviceRegAttempt{ActorId: actorId}).Order("updated_at desc").First(attemptPb).Error; err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return nil, err
		}
		logger.Error(ctx, "failed to get device registration attempt of actorId", zap.Error(err))
		return nil, err
	}
	return Unmarshal(attemptPb), nil
}

func (d *DeviceRegistrationAttemptsCrdb) GetAttemptById(ctx context.Context, attemptId string) (*authPb.DeviceRegistrationAttempt, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceRegistrationAttemptsCrdb", "GetAttemptById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.Db)
	if attemptId == "" {
		return nil, fmt.Errorf("attempt id cannot be empty")
	}
	attemptPb := &model.DeviceRegAttempt{}
	if err := db.Model(&model.DeviceRegAttempt{}).Where(&model.DeviceRegAttempt{AttemptId: attemptId}).Take(attemptPb).Error; err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return nil, err
		}
		logger.Error(ctx, "failed to get device registration attempt of attemptId", zap.Error(err))
		return nil, err
	}
	return Unmarshal(attemptPb), nil
}

func (d *DeviceRegistrationAttemptsCrdb) UpdateDeviceRegistrationStatus(ctx context.Context, attemptId string, status authPb.DeviceRegistrationStatus) error {
	defer metric_util.TrackDuration("auth/dao", "DeviceRegistrationAttemptsCrdb", "UpdateDeviceRegistrationStatus", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.Db)
	if attemptId == "" {
		return fmt.Errorf("attempt id cannot be empty")
	}
	if err := db.Model(&model.DeviceRegAttempt{}).Where(&model.DeviceRegAttempt{AttemptId: attemptId}).
		Select("status").Updates(&model.DeviceRegAttempt{Status: status}).Error; err != nil {
		logger.Error(ctx, "failed to update registration status in device registration attempt", zap.Error(err))
		return err
	}
	return nil
}

func (d *DeviceRegistrationAttemptsCrdb) UpdateSmsInfo(ctx context.Context, attemptId string, smsInfo *authPb.DeviceRegistrationAttempt_SmsInfo) error {
	defer metric_util.TrackDuration("auth/dao", "DeviceRegistrationAttemptsCrdb", "UpdateSmsInfo", time.Now())
	if attemptId == "" || smsInfo == nil {
		return epifierrors.ErrInvalidArgument
	}
	db := gormctxv2.FromContextOrDefault(ctx, d.Db)
	result := db.Model(&model.DeviceRegAttempt{}).Where(&model.DeviceRegAttempt{AttemptId: attemptId}).Select("sms_info").
		Updates(&model.DeviceRegAttempt{SmsInfo: smsInfo})

	if errors.Is(result.Error, gormV2.ErrRecordNotFound) || result.RowsAffected == 0 {
		return epifierrors.ErrRecordNotFound
	}
	return result.Error

}

func (d *DeviceRegistrationAttemptsCrdb) GetAttemptsByActorId(ctx context.Context, actorId string) ([]*authPb.DeviceRegistrationAttempt, error) {
	defer metric_util.TrackDuration("auth/dao", "DeviceRegistrationAttemptsCrdb", "GetAttemptsByActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.Db)
	if actorId == "" {
		return nil, fmt.Errorf("attempt id cannot be empty")
	}
	var attemptsOfActor []*model.DeviceRegAttempt
	if err := db.Model(&model.DeviceRegAttempt{}).Where(&model.DeviceRegAttempt{ActorId: actorId}).Find(&attemptsOfActor).Error; err != nil {
		logger.Error(ctx, "failed to get device registration attempts by actor id", zap.Error(err))
		return nil, err
	}

	var attemptsPb []*authPb.DeviceRegistrationAttempt
	for _, attemptModel := range attemptsOfActor {
		attemptsPb = append(attemptsPb, Unmarshal(attemptModel))
	}
	return attemptsPb, nil
}

func Marshal(attemptPb *authPb.DeviceRegistrationAttempt) *model.DeviceRegAttempt {
	return &model.DeviceRegAttempt{
		AttemptId:   attemptPb.GetAttemptId(),
		DeviceId:    attemptPb.GetDeviceId(),
		ActorId:     attemptPb.GetActorId(),
		PhoneNumber: attemptPb.GetPhoneNumber(),
		RequestId:   attemptPb.GetRequestId(),
		Status:      attemptPb.GetStatus(),
		SmsInfo:     attemptPb.GetSmsInfo(),
	}
}

func Unmarshal(attemptModel *model.DeviceRegAttempt) *authPb.DeviceRegistrationAttempt {
	return &authPb.DeviceRegistrationAttempt{
		AttemptId:   attemptModel.AttemptId,
		DeviceId:    attemptModel.DeviceId,
		ActorId:     attemptModel.ActorId,
		PhoneNumber: attemptModel.PhoneNumber,
		RequestId:   attemptModel.RequestId,
		Status:      attemptModel.Status,
		SmsInfo:     attemptModel.SmsInfo,
		CreatedAt:   timestamp.New(attemptModel.CreatedAt),
		UpdatedAt:   timestamp.New(attemptModel.UpdatedAt),
	}
}
