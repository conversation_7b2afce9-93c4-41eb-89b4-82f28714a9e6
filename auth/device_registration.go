// nolint: gosec
package auth

import (
	"bytes"
	"context"
	"crypto/aes"
	"encoding/hex"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	pkgcfg "github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	authConsumerPb "github.com/epifi/gamma/api/auth/consumer"
	"github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	vgAuth "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vgHeader "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	federalPb "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/auth/dao/model"
	events2 "github.com/epifi/gamma/auth/events"
	"github.com/epifi/gamma/auth/metrics"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/pkg/vendorstore"
	vendorAuth "github.com/epifi/gamma/vendorgateway/openbanking/auth"
	"github.com/epifi/gamma/vendornotification/client"
)

var (
	errUserRegOnOtherDevice = errors.New("user already registered with other device")
	errDeviceRegToOtherUser = errors.New("device registered with other user")
)

// nolint:funlen
func (s *Service) RegisterDevice(ctx context.Context, req *authPb.RegisterDeviceRequest) (*authPb.RegisterDeviceResponse, error) {
	var (
		ack, wait bool
	)
	// cloning the context because we want to capture the response received from vendor gateway
	// as the client cancels the context in 30s
	ctx = epificontext.CloneCtx(ctx)
	// device registration timeout
	ctx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	res := &authPb.RegisterDeviceResponse{}

	// validate if user allowed to do device registration
	isAllowed, err := s.validateDeviceRegCount(ctx, req.GetDevice().GetDeviceId())
	if err != nil {
		logger.Error(ctx, "registerDevice error in validateDeviceRegCount", zap.Error(err))
		return nil, fmt.Errorf("error in validateDeviceRegCount: %v", err)
	}
	if !isAllowed {
		res.Status = rpc.NewStatusWithoutDebug(uint32(authPb.ReRegisterDeviceResponse_REGISTRATION_LIMIT), "device registration limit exceeded")
		return res, nil
	}

	isDeviceRegAllowed, checkErr := s.isDeviceRegistrationAllowedOnIOS(ctx, req.GetDevice())
	switch {
	case checkErr != nil:
		res.Status = rpc.StatusInternalWithDebugMsg(checkErr.Error())
		return res, nil
	case !isDeviceRegAllowed:
		res.Status = rpc.NewStatusWithoutDebug(uint32(authPb.RegisterDeviceResponse_IOS_OS_NOT_ALLOWED), "")
		return res, nil
	}

	// generate user profile id and request id
	rand.Seed(time.Now().UnixNano())
	userProfileId := strconv.FormatInt(int64(rand.Intn(int(math.Pow10(9)))), 10)
	requestId := idgen.FederalRandomSequence(DeviceRegistrationRequestPrefix, 5)

	var record *authPb.DeviceRegistration

	record, err = s.getOrCreateDeviceReg(ctx, &authPb.DeviceRegistration{
		DeviceId:      req.Device.DeviceId,
		Device:        req.Device,
		UserProfileId: userProfileId,
		Status:        authPb.DeviceRegistrationStatus_UNREGISTERED,
		ActorId:       req.ActorId,
		Metadata: &authPb.DeviceRegistrationMetadata{
			RequestId: requestId,
		},
		SimId: req.GetSimId(),
	})

	if err != nil {
		switch {
		case errors.Is(err, errUserRegOnOtherDevice):
			logger.Info(ctx, "device reg user already reg on other device",
				zap.String(logger.DEVICE_ID, req.Device.DeviceId),
			)
			res.Status = rpc.StatusPermissionDeniedWithDebugMsg(err.Error())
			return res, nil

		case errors.Is(err, errDeviceRegToOtherUser):
			logger.Info(ctx, "device reg to other user", zap.String(logger.DEVICE_ID, req.Device.DeviceId))
			res.Status = rpc.StatusPermissionDeniedWithDebugMsg(err.Error())
			return res, nil

		default:
			logger.Error(ctx, "error in executing create device reg txn", zap.Error(err))
			return nil, fmt.Errorf("error in create device reg: %v", err)
		}
	}

	// Gracefully handling case when device is already registered
	// update access token status and return success
	if record.Status == authPb.DeviceRegistrationStatus_REGISTERED {
		logger.Debug(ctx, "device already registered")

		res.Status = rpc.StatusOk()
		res.DeviceToken = record.DeviceToken
		return res, nil
	}

	var (
		deviceToken    string
		responseStatus *rpc.Status
	)
	s.logInitRegisterDevice(ctx, req.GetActorId())

	// Creating new device registration attempt and Update request id in device registration table
	if record.GetMetadata().GetRequestId() == "" {
		record.Metadata = &authPb.DeviceRegistrationMetadata{}
	}

	// Create new device registration attempt entry, and update new request id in table
	record.GetMetadata().RequestId = requestId
	if err = s.UpdateNewDeviceRegistrationAttempt(ctx, record, req.GetRecipientPhoneNumber()); err != nil {
		logger.Error(ctx, "error in updating new attempt in device registration", zap.Error(err))
		return nil, err
	}

	if s.Conf.Flags().EnableSMSAckListener() {
		ack, wait = s.isSMSAcknowledged(ctx, req.GetActorId())
		if !ack && wait {
			res.Status = rpc.NewStatusWithoutDebug(uint32(authPb.RegisterDeviceResponse_RETRYABLE_ERROR), "sms not ack yet")
			return res, nil
		}
	}
	payload, pErr := getPayloadWithoutPrefix(ctx, req.GetPayload())
	if pErr != nil {
		return &authPb.RegisterDeviceResponse{
			Status: rpc.StatusInternalWithDebugMsg(pErr.Error()),
		}, nil
	}
	// call vendor to register device and get device token
	response, gErr := s.VendorGateway.RegisterDevice(ctx, &vgAuth.RegisterDeviceRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		DeviceId:      record.DeviceId,
		Phone:         req.GetPhoneNumber(),
		Payload:       payload,
		EmailId:       req.GetEmail(),
		UserProfileId: record.UserProfileId,
		RequestId:     requestId,
	})
	s.vendorStoreRegDev(ctx, req, response, requestId)
	// nil response returns 0 as status code, adding this check to avoid false positives
	if response != nil {
		metrics.RecordDevRegStatusMetric(req.GetRecipientPhoneNumber().ToString(), false,
			response.GetStatus().GetCode())
		_ = s.addHealthMetric(ctx, req.GetRecipientPhoneNumber(), authPb.DevRegistrationFlow_DEV_REGISTRATION_FLOW_ONBOARDING, response.GetStatus().GetCode())
		if ack && response.GetStatus().GetCode() == uint32(vendorAuth.DevRegSMSNotReceivedErrorCode) {
			logger.Info(ctx, "got no sms error after sms ack", zap.String(logger.REQUEST_ID, requestId))
		}
	}
	if err = epifigrpc.RPCError(response, gErr); err != nil {
		if response.GetStatus().IsAlreadyExists() {
			logger.Info(ctx, "device is already registered at vendor", zap.Error(err))
			regDevStatusResp, regDevStatusErr := s.handleAlreadyExistRegDev(ctx, req, record)
			if regDevStatusErr != nil {
				logger.Error(ctx, "error handling RegisterDevice already exists", zap.Error(regDevStatusErr))
				s.logCompletedRegisterDevice(ctx, req.ActorId, regDevStatusErr)
				res.Status = regDevStatusResp.GetStatus()
				return res, regDevStatusErr
			}
			logger.Info(ctx, "received device token for already existing device registration")
			deviceToken, responseStatus = regDevStatusResp.GetDeviceToken(), regDevStatusResp.GetStatus()
		} else {
			logger.Error(ctx, "error in RegisterDevice vg call", zap.Error(err))
			s.logCompletedRegisterDevice(ctx, req.ActorId, err)
			if gErr != nil {
				res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
				return res, err
			}
			res.Status = response.GetStatus()
			return res, err
		}
	} else {
		deviceToken, responseStatus = response.GetDeviceToken(), response.GetStatus()
	}

	logger.Info(ctx, fmt.Sprintf("sms endpoint: '%v', responsecode: '%v'", req.GetRecipientPhoneNumber(), response.GetVendorStatus().GetCode()))

	s.logCompletedRegisterDevice(ctx, req.ActorId, nil)
	// update device token and status in DB
	if txnErr := storagev2.RunCRDBIdempotentTxn(ctx, 3, func(txnCtx context.Context) error {
		if err = s.devRegDao.UpdateDeviceRegistrationByFilter(txnCtx, &authPb.DeviceRegistration{
			ActorId:     req.ActorId,
			DeviceId:    req.Device.DeviceId,
			DeviceToken: deviceToken,
			Status:      authPb.DeviceRegistrationStatus_REGISTERED,
			Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
			// todo(Vineet): update new device id
			VendorDeviceId: req.Device.GetDeviceId(),
		}, []authPb.DeviceRegisterationUpdateFieldMask{
			authPb.DeviceRegisterationUpdateFieldMask_REGISTRATION_STATUS,
			authPb.DeviceRegisterationUpdateFieldMask_DEVICE_TOKEN,
			authPb.DeviceRegisterationUpdateFieldMask_VENDOR_DEVICE_ID,
			authPb.DeviceRegisterationUpdateFieldMask_VENDOR,
		}); err != nil {
			logger.Error(txnCtx, "failed to update device registered status in db", zap.Error(err))
			return err
		}

		attemptId := record.GetAttemptId()
		if err = s.DeviceAttemptDao.UpdateDeviceRegistrationStatus(txnCtx, attemptId, authPb.DeviceRegistrationStatus_REGISTERED); err != nil {
			logger.Error(txnCtx, "failed to update device registration attempt status", zap.Error(err))
			return err
		}

		return nil
	}); txnErr != nil {
		if storagev2.IsErrorAmbiguous(txnErr) {
			logger.Error(ctx, "ambiguous txn error: ", zap.Error(err))
		}
		logger.Error(ctx, "failed to perform db update in transaction", zap.Error(err))
		return nil, txnErr
	}

	// expire access token to create new access token with latest device registration status
	if _, err = s.tokenStoresDao.ExpireTokenByActorId(ctx, req.GetActorId(), "", authPb.TokenType_ACCESS_TOKEN, authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE); err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, fmt.Sprintf("failed to expire old tokens of user token type %v", authPb.TokenType_ACCESS_TOKEN.String()), zap.Error(err))
	}

	// return success response
	res.Status = responseStatus
	res.DeviceToken = deviceToken
	return res, nil
}

func (s *Service) updateSimIdInContext(ctx context.Context, afuId string, simId string) error {
	// Updating sim ID in afu context
	_, err := s.afuDao.UpdateContextAndStatus(ctx, afuId, &afu.Context{
		SimId: simId,
	}, afu.OverallStatus_OVERALL_STATUS_IN_PROGRESS)
	if err != nil {
		logger.Error(ctx, "error in updating context and status", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) publishForTPAPDeviceUpdate(ctx context.Context, afuId string, actorId string) error {
	msgId, err := s.AFUVendorUpdatePublisher.PublishWithDelay(ctx, &authConsumerPb.ProcessVendorUpdateRequest{
		AfuId:       afuId,
		ActorId:     actorId,
		CredBlock:   "",
		AccessToken: "",
	}, s.AFUVendorUpdatePublisherDelay)
	if err != nil {
		logger.Error(ctx, "error publishing vendor update message to queue", zap.Error(err),
			logger.ZapAFUId(afuId), logger.ZapAFU())
		return err
	}
	logger.Info(ctx, "successfully published vendor update message to queue",
		zap.String("msgId", msgId), logger.ZapAFUId(afuId), logger.ZapAFU())
	return nil
}

// checkTPAPDeviceRegistrationEligibility does checks based on current actor state
// For example, user can stop AFU, continue onboarding with old auth factors in any other products (where AFU is required)
// and resume the halted AFU attempt
func (s *Service) checkTPAPDeviceRegistrationEligibility(ctx context.Context, actorId string, deviceId, simId string) error {
	// todo: check with Ankit if any other conditions has to be added

	devResp, err := s.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(devResp, err); err != nil {
		if devResp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "for re-registration failed to find older device registration record")
			return epifierrors.ErrFailedPrecondition
		}
		logger.Error(ctx, "error in GetDeviceAuth", zap.Error(err))
		return err
	}

	regDev := devResp.GetDevice()
	if regDev != nil && regDev.GetDeviceId() == deviceId && devResp.GetSimId() == simId && devResp.GetDeviceRegStatus() == authPb.DeviceRegistrationStatus_REGISTERED {
		logger.Info(ctx, "device already registered", zap.String(logger.DEVICE_ID, deviceId))
		return epifierrors.ErrAlreadyExists
	}

	inProgress, err := s.isCustomerCreationInProgressOrSuccess(ctx, actorId)
	if err != nil {
		return err
	}
	if inProgress {
		logger.Info(ctx, "customer creation in progress")
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

//nolint:funlen
func (s *Service) RecordReRegisteredDevice(ctx context.Context, req *authPb.RecordReRegisteredDeviceRequest) (*authPb.RecordReRegisteredDeviceResponse, error) {
	var (
		errResp = func(status *rpc.Status, msg string) (*authPb.RecordReRegisteredDeviceResponse, error) {
			status.SetDebugMessage(msg)
			return &authPb.RecordReRegisteredDeviceResponse{Status: status}, nil
		}
	)
	if req.GetActorId() == "" ||
		req.GetDevice().GetDeviceId() == "" ||
		req.GetVendorDeviceId() == "" {
		return errResp(rpc.StatusInvalidArgument(), "invalid request")
	}

	if err := storagev2.RunCRDBIdempotentTxn(ctx, 3, func(ctx context.Context) error {
		// Get last registered device registration record for the deviceId
		// if same actor id and vendor device id, do nothing
		// check if device is already registered to another actor with same vendorId => err
		// for remaining cases, delete all older dev reg records by deviceId, registered or unregistered
		// delete all older dev reg records by actorId, registered or unregistered
		// create new device registration record
		devRegRecord, daoErr := s.devRegDao.GetDeviceRegistration(ctx, "", req.GetDevice().GetDeviceId())
		if daoErr != nil && !storagev2.IsRecordNotFoundError(daoErr) {
			logger.Error(ctx, "error in fetching device registration record", zap.Error(daoErr))
			return daoErr
		}
		if devRegRecord != nil {
			if devRegRecord.GetActorId() == req.GetActorId() &&
				devRegRecord.GetVendorDeviceId() == req.GetVendorDeviceId() &&
				devRegRecord.GetStatus() == authPb.DeviceRegistrationStatus_REGISTERED {
				logger.Info(ctx, "device already registered to same actor")
				return nil
			}
			if devRegRecord.GetActorId() != req.GetActorId() &&
				devRegRecord.GetStatus() == authPb.DeviceRegistrationStatus_REGISTERED &&
				devRegRecord.GetVendorDeviceId() == req.GetVendorDeviceId() {
				logger.Error(ctx, "device already registered to another actor")
				return errDeviceRegToOtherUser
			}
			logger.Info(ctx, "deleting all older device registration records for deviceId", zap.String(logger.DEVICE_ID, obfuscator.Hashed(devRegRecord.GetDeviceId())))
			if delErr := s.devRegDao.DeleteDeviceRegistration(ctx, "", devRegRecord.GetDeviceId()); delErr != nil {
				logger.Error(ctx, "error in deleting device registration by device id", zap.Error(delErr))
				return delErr
			}
		}

		// delete all older dev reg records by actorId, registered or unregistered
		if daoErr = s.devRegDao.DeleteDeviceRegistration(ctx, req.GetActorId(), ""); daoErr != nil {
			logger.Error(ctx, "error in deleting device registration", zap.Error(daoErr))
			return daoErr
		}

		// get deleted record
		deletedRecord, delErr := s.devRegDao.GetDeletedDeviceRegistration(ctx, req.GetActorId(), "")
		if delErr != nil {
			logger.Error(ctx, "error in fetching deleted device registration record", zap.Error(delErr))
			return delErr
		}
		if deletedRecord.GetDeviceToken() == "" || deletedRecord.GetUserProfileId() == "" {
			logger.Error(ctx, "device token or user profile id not found in deleted record")
			return epifierrors.ErrFailedPrecondition
		}
		// create new record
		if daoErr = s.devRegDao.CreateRegistrationEntry(ctx, &authPb.DeviceRegistration{
			Status:        authPb.DeviceRegistrationStatus_REGISTERED,
			ActorId:       req.GetActorId(),
			DeviceId:      req.GetDevice().GetDeviceId(),
			Device:        req.GetDevice(),
			DeviceToken:   deletedRecord.GetDeviceToken(),
			UserProfileId: deletedRecord.GetUserProfileId(),
			Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
			// todo(Vineet): update new device id
			VendorDeviceId: req.GetVendorDeviceId(),
			Metadata: &authPb.DeviceRegistrationMetadata{
				RequestId: req.GetVendorRequestId(),
			},
			SimId: req.GetSimId(),
		}); daoErr != nil {
			logger.Error(ctx, "error in creating device registration", zap.Error(daoErr))
			return daoErr
		}
		return nil
	}); err != nil {
		logger.Error(ctx, "error in recording device re-registration txn", zap.Error(err))
		return errResp(rpc.StatusInternal(), "error in db txn: "+err.Error())
	}

	return &authPb.RecordReRegisteredDeviceResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) handleAlreadyExistRegDev(ctx context.Context, req *authPb.RegisterDeviceRequest, record *authPb.DeviceRegistration) (*vgAuth.GetRegisterDeviceStatusResponse, error) {
	resp, errVS := s.VendorStore.GetByActorIdAndAPI(ctx, 100, 0, req.GetActorId(), vendorstore.API_DEVICE_REGISTRATION)
	if errVS != nil {
		return nil, fmt.Errorf("error in getting response from vendor store: %w", errVS)
	}
	for _, vs := range resp {
		regDevStatusResp, err := s.VendorGateway.GetRegisterDeviceStatus(ctx, &vgAuth.GetRegisterDeviceStatusRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			DeviceId:      record.GetDeviceId(),
			UserProfileId: record.GetUserProfileId(),
			PhoneNumber:   req.GetPhoneNumber(),
			OriginalReqId: vs.GetRequestId(),
		})
		// s.vendorStoreGetRegDevStatus(ctx, req, regDevStatusResp, vs)
		if err = epifigrpc.RPCError(regDevStatusResp, err); err != nil {
			if rpc.StatusFromError(err).IsAlreadyExists() {
				logger.Info(ctx, "dev re reg already exists")
				continue
			}
			logger.Error(ctx, "error in GetRegisterDeviceStatus vg call", zap.Error(err))
			continue
		}
		if regDevStatusResp.GetDeviceToken() != "" {
			return regDevStatusResp, nil
		}
	}
	return nil, fmt.Errorf("GetRegisterDeviceStatus cannot fetch device token using vendor store")
}

func aesEncryption(ctx context.Context, plainText string, key []byte) (string, error) {
	cipherBlock, err := aes.NewCipher(key)
	if err != nil {
		logger.Error(ctx, "failed to create cipher block", zap.Error(err))
		return "", err
	}
	out := make([]byte, len(plainText))
	cipherBlock.Encrypt(out, []byte(plainText))
	return hex.EncodeToString(out), nil
}

func (s *Service) GetDeviceBindingSMS(ctx context.Context, req *authPb.GetDeviceBindingSMSRequest) (*authPb.GetDeviceBindingSMSResponse, error) {
	var (
		payloadPrefix = s.Conf.Application().SMSConfig.DeviceRegistrationPrefix
		smsEndpoint   *commontypes.PhoneNumber
	)

	encryptedPayload := getPayloadWithoutSplChar()

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewGetDeviceRegistrationSMS(epificontext.ActorIdFromContext(ctx), events2.Success, ""))
	})

	smsEndpoint = s.getSMSEndpoint(ctx, req.GetDeviceId())
	logger.Info(ctx, fmt.Sprintf("Sms endpoint choice: %v", smsEndpoint))

	if s.Conf.Flags().EnableSMSAckListener() {
		logger.Debug(ctx, "sending trigger for sms ack")
		if _, err := s.DeviceAttemptDao.Create(ctx, &authPb.DeviceRegistrationAttempt{
			ActorId:     req.GetActorId(),
			DeviceId:    req.GetDeviceId(),
			PhoneNumber: smsEndpoint.ToString(),
		}); err != nil {
			logger.Error(ctx, "error creating device registration attempt", zap.Error(err))
			return nil, err
		}
		logger.Debug(ctx, "sending trigger for sms ack", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		userPhone, _ := s.getPhoneFromActorId(ctx, req.GetActorId(), req.IsAuthFactorUpdate)
		if userPhone != nil {
			logger.Debug(ctx, "user phone found", zap.String(logger.PHONE_NUMBER, userPhone.ToString()))
			s.triggerSMSAcknowledgementNotification(ctx, userPhone)
		}
	}
	metrics.RecordDevBindingMetric(smsEndpoint.ToString(), req.IsAuthFactorUpdate)

	return &authPb.GetDeviceBindingSMSResponse{
		Payload:     payloadPrefix + encryptedPayload,
		PhoneNumber: smsEndpoint,
	}, nil
}

// GetDeviceAuth returns DeviceRegistration details for given actorId and deviceId.
// Status Code_NOT_FOUND means that it's a new user and registration has never been done.
func (s *Service) GetDeviceAuth(ctx context.Context, req *authPb.GetDeviceAuthRequest) (*authPb.GetDeviceAuthResponse, error) {
	errRes := func(status *rpc.Status, msg string) (*authPb.GetDeviceAuthResponse, error) {
		status.SetDebugMessage(msg)
		return &authPb.GetDeviceAuthResponse{Status: status}, nil
	}
	successRes := func(r *authPb.DeviceRegistration) (*authPb.GetDeviceAuthResponse, error) {
		return &authPb.GetDeviceAuthResponse{
			Status:          rpc.StatusOk(),
			ActorId:         r.GetActorId(),
			UserProfileId:   r.GetUserProfileId(),
			DeviceToken:     r.GetDeviceToken(),
			Device:          r.GetDevice(),
			DeviceRegStatus: r.GetStatus(),
			SimId:           r.GetSimId(),
			VendorDeviceId:  r.GetVendorDeviceId(),
			CreatedAt:       r.GetCreatedAt(),
			UpdatedAt:       r.GetUpdatedAt(),
		}, nil
	}
	actorId, deviceId := req.GetActorId(), req.GetDevice().GetDeviceId()

	// get device registration
	r, err := s.devRegDao.GetDeviceRegistration(ctx, actorId, deviceId, &model.OptionalParams{
		UseCache: true,
	})
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error in auth dao: GetDeviceRegistration by actor & device",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	if !storagev2.IsRecordNotFoundError(err) {
		r = s.populateVendorDeviceIdIfMissing(ctx, r)
		return successRes(r)
	}

	// If active device registration is not found,
	// search for soft-deleted/inactive/deregistered record.
	r, err = s.deletedDeviceReg(ctx, actorId, deviceId)
	if storagev2.IsRecordNotFoundError(err) {
		return errRes(rpc.StatusRecordNotFound(), "")
	}
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}
	// soft deleted record means that it has been de-registered.
	r.Status = authPb.DeviceRegistrationStatus_UNREGISTERED
	return successRes(r)
}

// populateVendorDeviceIdIfMissing when current record is registered but vendor device id is not populated
func (s *Service) populateVendorDeviceIdIfMissing(ctx context.Context, r *authPb.DeviceRegistration) *authPb.DeviceRegistration {
	if r.GetVendorDeviceId() == "" && r.GetStatus() == authPb.DeviceRegistrationStatus_REGISTERED {
		r.VendorDeviceId = r.GetDeviceId()
		r.Vendor = commonvgpb.Vendor_FEDERAL_BANK
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			_ = s.devRegDao.UpdateDeviceRegistrationByFilter(ctx, r,
				[]authPb.DeviceRegisterationUpdateFieldMask{
					authPb.DeviceRegisterationUpdateFieldMask_VENDOR_DEVICE_ID,
					authPb.DeviceRegisterationUpdateFieldMask_VENDOR,
				})
		})
	}
	return r
}

// GetFirstDeviceRegAfterTime returns the first device registrations for a given actor id after the given from time
// NOTE - if FromTime is nil, checks from start
func (s *Service) GetFirstDeviceRegAfterTime(ctx context.Context,
	req *authPb.GetFirstDeviceRegAfterTimeRequest) (*authPb.GetFirstDeviceRegAfterTimeResponse, error) {
	var res = &authPb.GetFirstDeviceRegAfterTimeResponse{}
	if req.GetFromTime() != nil && !req.GetFromTime().IsValid() {
		logger.Error(ctx, "invalid from time passed")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	fromTime := time.Time{} // zero value
	if req.GetFromTime() != nil {
		fromTime = req.GetFromTime().AsTime()
	}
	deviceRegistration, err := s.devRegDao.GetFirstDeviceRegAfterTime(ctx, req.GetActorId(), fromTime)
	switch {
	case storagev2.IsRecordNotFoundError(err):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in GetFirstDeviceRegAfterTime", zap.Error(err), zap.Time("fromTime", fromTime))
		res.Status = rpc.StatusInternalWithDebugMsg("error in GetFirstDeviceRegAfterTime")
		return res, nil
	}
	res.DeviceRegistration = deviceRegistration
	res.Status = rpc.StatusOk()
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.validateDevRegAfterTime(ctx, deviceRegistration)
	})
	return res, nil
}

// validateDevRegAfterTime function validates output from GetFirstDeviceRegAfterTime after db cleanup
func (s *Service) validateDevRegAfterTime(ctx context.Context, devReg *authPb.DeviceRegistration) {
	devReg2, err := s.devRegDao.GetDeviceRegistration(ctx, devReg.GetActorId(), "")
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "could not get devreg for actor", zap.Error(err))
		return
	}

	// if data mismatches, then we need to log the reason why
	if !areDevRegsEqual(devReg, devReg2) {
		// this is expected in case the last successful registered device is being used by someone else.
		if devReg.GetDeletedAtUnix() != 0 && devReg2 == nil {
			logger.Info(ctx, "GetFirstDeviceRegAfterTime returning stale data", zap.String(logger.REASON, "device being used by other actor"))
			return
		}

		// all other cases are unexpected
		logger.Info(ctx, "GetFirstDeviceRegAfterTime returning stale data", zap.String(logger.REASON, "unexpected scenario, check data"),
			zap.String("status1", devReg.GetStatus().String()), zap.String("token1", devReg.GetDeviceToken()),
			zap.String("created1", devReg.GetCreatedAt().String()), zap.String("status2", devReg2.GetStatus().String()),
			zap.String("token2", devReg2.GetDeviceToken()), zap.String("created2", devReg.GetCreatedAt().String()))
	}

	// data returned by GetFirstDeviceRegAfterTime is correct
	return
}

func areDevRegsEqual(a, b *authPb.DeviceRegistration) bool {
	if a == nil || b == nil {
		return true
	}
	return proto.Equal(a, b)
}

// nolint: funlen, govet
// getOrCreateDeviceReg ensures that there's a DB record for given actor & device
func (s *Service) getOrCreateDeviceReg(ctx context.Context, o *authPb.DeviceRegistration) (*authPb.DeviceRegistration, error) {
	if err := s.devRegDao.CreateRegistrationEntry(ctx, o); err != nil {
		if !storagev2.IsDuplicateRowError(err) {
			return nil, fmt.Errorf("registerDevice create db failed: %w", err)
		}

		// Handling the case where device was registered with different user
		existing, err := s.devRegDao.GetDeviceRegistration(ctx, "", o.DeviceId)
		if err != nil && !storagev2.IsRecordNotFoundError(err) {
			return nil, fmt.Errorf("create device reg get by deviceid failed: %w", err)
		}
		if !storagev2.IsRecordNotFoundError(err) {
			// record found for given device details

			// record belongs to the same user
			if existing.ActorId == o.ActorId && existing.DeviceId == o.DeviceId {
				logger.Info(ctx, "create device reg record exists",
					zap.String("status", existing.Status.String()),
				)
				return existing, nil
			}
			// record belongs to the other user and device registration was success
			if existing.Status == authPb.DeviceRegistrationStatus_REGISTERED {
				return nil, errDeviceRegToOtherUser
			}

			// device registration was attempted by other user but wasn't success
			// stale record; deleting so that a new can be created
			if err := s.deleteDeviceRegistration(ctx, existing.ActorId, existing.DeviceId); err != nil {
				return nil, fmt.Errorf("create device reg error in deleteDeviceRegistration: %w", err)
			}
		}

		// Handling the case where user already has a row in DB
		existing, err = s.devRegDao.GetDeviceRegistration(ctx, o.GetActorId(), "")
		if err != nil && !storagev2.IsRecordNotFoundError(err) {
			return nil, fmt.Errorf("create device reg get by actorid failed: %w", err)
		}
		if !storagev2.IsRecordNotFoundError(err) {
			// record found for given user

			// same device id and same user have already been handled in device check above

			// user registered on other device
			if existing.Status == authPb.DeviceRegistrationStatus_REGISTERED {
				return nil, errUserRegOnOtherDevice
			}

			// stale record with old device; deleting so that a new can be created
			if err := s.deleteDeviceRegistration(ctx, existing.ActorId, existing.DeviceId); err != nil {
				return nil, fmt.Errorf("create device reg error in deleteDeviceRegistration: %w", err)
			}
			// user profile is unique for a user
			o.UserProfileId = existing.UserProfileId
		}

		// now we should be able to create a new entry
		if err = s.devRegDao.CreateRegistrationEntry(ctx, o); err != nil {
			return nil, fmt.Errorf("error in second create device reg: %v", err)
		}
	}

	logger.Debug(ctx, "create device reg successful", zap.String("record", o.String()))
	return o, nil
}

// deletedDeviceReg gets deleted device registration record from DB.
// if record isn't found, returns nil value and nil error.
func (s *Service) deletedDeviceReg(ctx context.Context, actorId, deviceId string) (*authPb.DeviceRegistration, error) {
	r, err := s.devRegDao.GetDeletedDeviceRegistration(ctx, actorId, deviceId)
	if err != nil {
		return nil, err
	}
	return r, nil
}

// nolint: duplicate
func (s *Service) deleteDeviceRegistration(ctx context.Context, actorId, deviceId string) error {
	if err := s.devRegDao.DeleteDeviceRegistration(ctx, actorId, deviceId); err != nil {
		logger.Error(ctx, "error in delete device registration",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err),
		)
		return fmt.Errorf("error in delete device reg: %v", err)
	}
	return nil
}

// nolint: funlen
func (s *Service) DeactivateDevice(ctx context.Context, req *authPb.DeactivateDeviceRequest) (*authPb.DeactivateDeviceResponse, error) {
	var (
		errResp = func(status *rpc.Status) (*authPb.DeactivateDeviceResponse, error) {
			return &authPb.DeactivateDeviceResponse{
				Status: status,
			}, nil
		}
		devAuthDetails   *vgHeader.Auth
		ph               *commontypes.PhoneNumber
		email            string
		deactivationType = vgAuth.DeRegisterDeviceRequest_TEMPORARY
	)

	// Get user from actor for phone number, email
	userRes, err := s.UserClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: req.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		if rpc.StatusFromError(rpcErr).IsRecordNotFound() {
			return errResp(rpc.StatusRecordNotFound())
		}
		logger.Error(ctx, "error in getting user details", zap.Error(rpcErr))
		return errResp(rpc.StatusInternalWithDebugMsg(rpcErr.Error()))
	}
	ph = userRes.GetUser().GetProfile().GetPhoneNumber()
	email = userRes.GetUser().GetProfile().GetEmail()

	// If the deactivation type is PERMANENT, we will need to check if savings account is created and return if it is already created
	if req.GetDeactivationType() == authPb.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT {
		if err = s.checkPermDeactivationEligibilty(ctx, req.GetActorId(), userRes.GetUser().GetId()); err != nil {
			if errors.Is(epifierrors.ErrPermissionDenied, err) {
				return errResp(rpc.StatusPermissionDenied())
			}
			return errResp(rpc.StatusInternalWithDebugMsg(err.Error()))
		}
		deactivationType = vgAuth.DeRegisterDeviceRequest_PERMANENT
	}

	daResp, daErr := s.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: req.GetActorId(),
	})
	if daErr = epifigrpc.RPCError(daResp, daErr); daErr != nil {
		logger.Error(ctx, "error in GetDeviceAuth", zap.Error(daErr))
		return errResp(rpc.StatusInternalWithDebugMsg(daErr.Error()))
	}

	// deactivate device vendor call
	vgRes, err := s.VendorGateway.DeRegisterDevice(ctx, &vgAuth.DeRegisterDeviceRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendorMap[afuVendor],
		},
		DeviceToken:        daResp.GetDeviceToken(),
		DeviceId:           daResp.GetDevice().GetDeviceId(),
		UserProfileId:      daResp.GetUserProfileId(),
		Phone:              ph,
		Email:              email,
		DeRegistrationType: deactivationType,
	})
	if err != nil {
		logger.Error(ctx, "failed to deregister device", zap.Error(err))
		return errResp(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	if vgRes.GetStatus().GetCode() != uint32(vgAuth.DeRegisterDeviceResponse_OK) &&
		vgRes.GetStatus().GetCode() != uint32(vgAuth.DeRegisterDeviceResponse_DEVICE_ALREADY_DEACTIVATED) {
		err = fmt.Errorf("non-OK response received from VG. Code: %v, debug msg : %v", vgRes.GetStatus().Code, vgRes.GetStatus().DebugMessage)
		logger.Error(ctx, "deactivate device failed", zap.Error(err), logger.ZapAFU(), zapVendorStep)
		return errResp(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	// soft delete device registration record
	if err = s.devRegDao.DeleteDeviceRegistration(ctx, req.GetActorId(), daResp.GetDevice().GetDeviceId()); err != nil {
		logger.Error(ctx, "error in delete device registration",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err),
		)
		if deactivationType == vgAuth.DeRegisterDeviceRequest_TEMPORARY {
			// fetch device auth details
			if devAuthDetails, err = s.fetchDeviceAuthDetails(ctx, req.GetActorId()); err != nil {
				logger.Error(ctx, "error in fetching device auth details", zap.Error(err))
				return errResp(rpc.StatusInternalWithDebugMsg(err.Error()))
			}

			// re-activate device to restore original state at vendor
			if actErr, _, _ := s.ReactivateDevice(ctx, devAuthDetails, ph, email, "", false); actErr != nil {
				logger.Error(ctx, "failed to revert deactivation", zap.Error(actErr))
				return errResp(rpc.StatusInternalWithDebugMsg(actErr.Error()))
			}
		}
		return errResp(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	return &authPb.DeactivateDeviceResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) checkPermDeactivationEligibilty(ctx context.Context, actorId string, userId string) error {
	res, err := s.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in getting bank customer", zap.Error(rpcErr))
		return rpcErr
	}
	// deactivate device to put user into force afu as device got deactivated due to number updated outside FI
	if res.GetBankCustomer().GetStatus() == bankcust.Status_CUSTOMER_STATUS_INACTIVE &&
		res.GetBankCustomer().GetCustomerInactiveInfo().GetReasons()[0].GetReasonType() == bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI {
		return nil
	}

	getAccResp, errGetAcc := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: userId,
		},
	})
	if errGetAcc != nil && status.Code(errGetAcc) != codes.NotFound {
		return errGetAcc
	}
	account := getAccResp.GetAccount()
	// Permanent deactivation is done for closed accounts as well, so handling it before checking for an active account
	if account.GetId() != "" {
		if err := s.checkAccountClosureStatus(ctx, account.GetId()); err == nil {
			return nil
		}
	}
	if account != nil &&
		account.GetState() != savingsPb.State_FAILED &&
		account.GetState() != savingsPb.State_MAX_RETRIES_CREATE_ACCOUNT {
		return epifierrors.ErrPermissionDenied
	}

	inProgress, errCheck := s.isCustomerCreationInProgressOrSuccess(ctx, actorId)
	if errCheck != nil {
		logger.Error(ctx, "failed to check customer creation status", zap.Error(errCheck))
		return errCheck
	}
	if inProgress {
		logger.Info(ctx, "user cannot be deleted as cif creation in progress or success")
		return epifierrors.ErrPermissionDenied
	}
	logger.Info(ctx, "DeactivateDevice rpc called with permanent deactivation type")
	return nil
}

func (s *Service) checkAccountClosureStatus(ctx context.Context, savingsAccountId string) error {
	opStatusRes, err := s.opStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness: operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: savingsAccountId,
		},
	})
	if rpcErr := epifigrpc.RPCError(opStatusRes, err); rpcErr != nil {
		if !opStatusRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in getting operational status", zap.Error(rpcErr))
		}
		return rpcErr
	}
	accountOpStatus := opStatusRes.GetOperationalStatusInfo().GetOperationalStatus()
	if accountOpStatus != enums.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
		logger.Info(ctx, fmt.Sprintf("permission denied as account operational status is %v", accountOpStatus))
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

// deleteOldAndRegisterNewDevice deletes old records and create new entry in device registration
func (s *Service) deleteOldAndRegisterNewDevice(ctx context.Context, actorId string, device *commontypes.Device, afuVendorRequestId, simId string) error {
	if actorId == "" || device.GetDeviceId() == "" {
		return fmt.Errorf("deleteOldAndRegisterNewDevice actor and device required")
	}

	// we're deleting existing record as an actor can be registered to one device at a time
	if err := s.deleteDeviceRegistration(ctx, actorId, ""); err != nil {
		return fmt.Errorf("error in delete current device: %v", err)
	}

	// delete device record (if not registered)
	if err := s.deleteUnregisteredDevice(ctx, device.DeviceId); err != nil {
		return err
	}

	o, err := s.devRegDao.GetDeletedDeviceRegistration(ctx, actorId, "")
	if err != nil {
		return fmt.Errorf("error in get record: %v", err)
	}

	// create new record with updated details
	newDeviceRegistration := &authPb.DeviceRegistration{
		Status:        authPb.DeviceRegistrationStatus_REGISTERED,
		ActorId:       actorId,
		DeviceId:      device.GetDeviceId(),
		Device:        device,
		DeviceToken:   o.GetDeviceToken(),
		UserProfileId: o.GetUserProfileId(),
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		// todo(Vineet): update new device id
		VendorDeviceId: device.GetDeviceId(),
		Metadata: &authPb.DeviceRegistrationMetadata{
			RequestId: afuVendorRequestId,
		},
		SimId: simId,
	}
	err = s.CreateDeviceRegistrationEntry(ctx, newDeviceRegistration, nil)
	if err != nil {
		return fmt.Errorf("error in create device reg: %v", err)
	}
	return nil
}

func (s *Service) deleteUnregisteredDevice(ctx context.Context, deviceId string) error {
	if err := storagev2.RunCRDBIdempotentTxn(ctx, txnRetries, func(ctx context.Context) error {

		o, err := s.devRegDao.GetDeviceRegistration(ctx, "", deviceId)
		if storagev2.IsRecordNotFoundError(err) {
			logger.Info(ctx, "no unregistered device found")
			return nil
		}
		if err != nil {
			logger.Error(ctx, "error in get device reg", zap.Error(err))
			return err
		}
		if o.GetStatus() != authPb.DeviceRegistrationStatus_UNREGISTERED {
			logger.Info(ctx, "device id is registered with us, cannot delete")
			return nil
		}

		logger.Info(ctx, fmt.Sprintf("deleting unregistered device linked to actor: %v", o.GetActorId()),
			zap.String(logger.STATE, o.GetStatus().String()))
		return s.deleteDeviceRegistration(ctx, "", deviceId)
	}); err != nil {
		logger.Error(ctx, "error in delete unregistered device reg txn", zap.Error(err))
		return fmt.Errorf("error in delete unregistered device reg: %w", err)
	}
	return nil
}

// validateDeviceRegCount returns false if the device_id doesn't qualify for registration.
// As per the NPCI requirement to mitigate frauds on UPI, Customer on-boarding PSP shall
// restrict device binding attempt per device ID to 3 per day.
// details: https://monorail.pointz.in/p/fi-app/issues/detail?id=2227
func (s *Service) validateDeviceRegCount(ctx context.Context, deviceId string) (bool, error) {
	logDeviceId := zap.String(logger.DEVICE_ID, deviceId)
	count, err := s.devRegDao.GetDeviceRegInLast1Day(ctx, deviceId)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "validateDeviceRegCount error in GetDeviceRegInLast1Day", logDeviceId, zap.Error(err))
		return false, err
	}
	if count >= s.Conf.Application().NPCIDeviceBindingLimitPerDay {
		logger.Info(ctx, "validateDeviceRegCount validation failed", zap.String(logger.DEVICE_ID, deviceId))
		return false, nil
	}
	logger.Debug(ctx, fmt.Sprintf("validateDeviceRegCount validation passed; count: %v", count))
	return true, nil
}

// NPCI regulation: Block device registration for IOS users based on OS version
// https://monorail.pointz.in/p/fi-app/issues/detail?id=81518&sort=milestone
func (s *Service) isDeviceRegistrationAllowedOnIOS(ctx context.Context, device *commontypes.Device) (bool, error) {
	isDeviceRegAllowedFunc := func() (bool, error) {
		if !s.Conf.AFU().EnableDeviceRegistrationOSValidation() {
			return true, nil
		}
		if device.GetPlatform() != commontypes.Platform_IOS {
			return true, nil
		}
		if device.GetOsApiVersion() == "" {
			// Not allowing device registration when no OS version is found to be on safe side of compliance
			return false, epifierrors.ErrInvalidArgument
		}
		// OS API version is in this format: x.y.z where x,y,z > 0
		osVersionList := strings.Split(device.GetOsApiVersion(), ".")
		osVersion, err := strconv.Atoi(osVersionList[0])
		if err != nil {
			logger.Error(ctx, "error in converting os version string to integer", zap.Error(err))
			return false, errors.New("error in converting os version to integer")
		}
		return osVersion >= 17, nil
	}
	isDeviceRegAllowed, err := isDeviceRegAllowedFunc()
	if err == nil {
		logger.Info(ctx, "Device registration allowed status", zap.Bool(logger.STATUS, isDeviceRegAllowed), zap.String(logger.APP_VERSION_CODE, device.GetOsApiVersion()))
	}
	return isDeviceRegAllowed, err
}

func getPayloadWithoutPrefix(ctx context.Context, p string) (string, error) {
	res := strings.Split(p, " ")
	if len(res) < 2 {
		logger.Error(ctx, "invalid payload from client, payload missing either prefix or encrypted text", zap.String(logger.PAYLOAD, p))
		return "", fmt.Errorf("invalid payload from client, payload missing either prefix or encrypted text")
	}
	return res[1], nil
}

func (s *Service) vendorStoreRegDev(ctx context.Context, req *authPb.RegisterDeviceRequest, response *vgAuth.RegisterDeviceResponse, requestId string) {
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		_ = s.VendorStore.Insert(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, response.GetVendorStatus(), response.GetStatus(), vendorstore.API_DEVICE_REGISTRATION, requestId)
	})
}

func (s *Service) CreateDeviceRegistrationEntry(ctx context.Context, o *authPb.DeviceRegistration, recipientNumber *commontypes.PhoneNumber) error {
	var (
		devRegAttempt *authPb.DeviceRegistrationAttempt
		err           error
	)

	if s.Conf.Flags().EnableSMSAckListener() {
		devRegAttempt, err = s.DeviceAttemptDao.GetLastAttemptByActor(ctx, o.GetActorId())
		if err != nil && !storagev2.IsRecordNotFoundError(err) {
			logger.Error(ctx, "unable to get last attempt for actor", zap.Error(err))
			return err
		}

	}
	// fallback handling when sms Ack listener is not enabled or race condition when there is no dev reg attempt created
	if !s.Conf.Flags().EnableSMSAckListener() || storagev2.IsRecordNotFoundError(err) {
		reqId := o.GetMetadata().GetRequestId()
		devRegAttempt = &authPb.DeviceRegistrationAttempt{
			DeviceId:    o.GetDeviceId(),
			ActorId:     o.GetActorId(),
			PhoneNumber: recipientNumber.ToString(),
			RequestId:   reqId,
			Status:      0,
		}
		// create attempt in device registration attempt
		devRegAttempt, err = s.DeviceAttemptDao.Create(ctx, devRegAttempt)
		if err != nil {
			logger.Error(ctx, "failed to create device registration attempt entry", zap.Error(err))
			return err
		}
	}

	o.AttemptId = devRegAttempt.GetAttemptId()
	// create device registration table entry
	if err = s.devRegDao.CreateRegistrationEntry(ctx, o); err != nil {
		logger.Error(ctx, "failed to create device registration entry", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) UpdateNewDeviceRegistrationAttempt(ctx context.Context, o *authPb.DeviceRegistration, recipientNumber *commontypes.PhoneNumber) error {
	var (
		devRegAttempt *authPb.DeviceRegistrationAttempt
		err           error
	)

	if s.Conf.Flags().EnableSMSAckListener() {
		devRegAttempt, err = s.DeviceAttemptDao.GetLastAttemptByActor(ctx, o.GetActorId())
		if err != nil && !storagev2.IsRecordNotFoundError(err) {
			logger.Error(ctx, "unable to get last attempt for actor", zap.Error(err))
			return err
		}
	}
	// fallback handling when sms Ack listener is not enabled or race condition when there is no dev reg attempt created
	if !s.Conf.Flags().EnableSMSAckListener() || storagev2.IsRecordNotFoundError(err) {
		reqId := o.GetMetadata().GetRequestId()
		devRegAttempt = &authPb.DeviceRegistrationAttempt{
			DeviceId:    o.GetDeviceId(),
			ActorId:     o.GetActorId(),
			PhoneNumber: recipientNumber.ToString(),
			RequestId:   reqId,
			Status:      0,
		}
		// create attempt in device registration attempt
		devRegAttempt, err = s.DeviceAttemptDao.Create(ctx, devRegAttempt)
		if err != nil {
			logger.Error(ctx, "failed to create device registration attempt entry", zap.Error(err))
			return err
		}

	}

	o.AttemptId = devRegAttempt.GetAttemptId()
	if err = s.devRegDao.UpdateDeviceRegistrationByFilter(ctx, o, []authPb.DeviceRegisterationUpdateFieldMask{authPb.DeviceRegisterationUpdateFieldMask_DEVICE_REG_METADATA, authPb.DeviceRegisterationUpdateFieldMask_ATTEMPT_ID}); err != nil {
		logger.Error(ctx, "failed to update device registration attempt_id, and request id", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) isSMSAcknowledged(ctx context.Context, actorId string) (ack, wait bool) {
	logger.Debug(ctx, "checking if sms acknowledged")
	la, err := s.DeviceAttemptDao.GetLastAttemptByActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error getting the last dev reg attempt", zap.Error(err))
		return false, false
	}
	logger.Debug(ctx, fmt.Sprintf("dev reg attempt created at %v", la.GetCreatedAt().AsTime()), zap.String(logger.ATTEMPT_ID, la.GetAttemptId()))
	if la.GetSmsInfo().GetSmsAckAt() != nil {
		logger.Debug(ctx, fmt.Sprintf("sms acknowledgement at %v", la.GetSmsInfo().GetSmsAckAt().AsTime().String()))
		return true, false
	}
	if time.Since(la.GetCreatedAt().AsTime()) <= 20*time.Second {
		// wait until 20 seconds since the encryption payload generation for SMS ack
		logger.Debug(ctx, "waiting for sms acknowledgement...")
		return false, true
	}
	logger.Debug(ctx, "not waiting any longer for sms acknowledgement")
	return false, false
}

func (s *Service) triggerSMSAcknowledgementNotification(ctx context.Context, userPh *commontypes.PhoneNumber) {
	if pkgcfg.IsSimulatedEnv(s.Conf.Application().Environment) {
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			// setting random wait time before sms ack [1,5) seconds.
			randWait := rand.Intn(4) + 1
			logger.Debug(ctx, fmt.Sprintf("trying to trigger sms ack... after %d secs", randWait))
			devRegSMSAck := &federalPb.DeviceRegistrationSMSUpdateCallback{
				SmsTimestamp: time.Now().String(),
				Status:       "SUCCESS",
				MobileNumber: userPh.ToString(),
			}
			reqBody, err := protojson.Marshal(devRegSMSAck)
			if err != nil {
				logger.Error(ctx, "error marshalling sms notification request", zap.Error(err))
				return
			}
			time.Sleep(time.Duration(randWait) * time.Second)
			httpClient := client.NewHttpClient()
			postURL := s.Conf.DeviceRegistration().SMSAckUrl
			_, err = httpClient.Post(postURL, "application/json", bytes.NewBuffer(reqBody))
			if err != nil {
				logger.Error(ctx, "error encountered calling http endpoint", zap.Error(err))
			}
		})

	}
}

func (s *Service) getPhoneFromActorId(ctx context.Context, actorId string, isDoingAFU bool) (*commontypes.PhoneNumber, error) {
	if isDoingAFU {
		afuRecord, err := s.afuDao.GetByActor(ctx, actorId, 1)
		if err != nil || len(afuRecord) == 0 {
			logger.Error(ctx, "error fetching phone via user afu record", zap.Error(err))
			return nil, err
		}
		return afuRecord[0].GetContext().GetNewValues().GetPhoneNumber(), nil
	}
	userResp, err := s.UserClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		logger.Error(ctx, "error fetching phone via user record", zap.Error(err))
		return nil, err
	}
	return userResp.GetUser().GetProfile().GetPhoneNumber(), nil
}

func (s *Service) IsDeviceRegistrationAllowed(ctx context.Context, req *authPb.IsDeviceRegistrationAllowedRequest) (*authPb.IsDeviceRegistrationAllowedResponse, error) {
	var (
		reason authPb.IsDeviceRegistrationAllowedResponse_Reason
	)
	// regulatory stuff.
	isAllowed, err := s.validateDeviceRegCount(ctx, req.GetDeviceId())
	if err != nil {
		logger.Error(ctx, "error in validateDeviceRegCount", zap.Error(err))
		return &authPb.IsDeviceRegistrationAllowedResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if !isAllowed {
		logger.Info(ctx, "device has reached NPCI registration limit")
		reason = authPb.IsDeviceRegistrationAllowedResponse_REASON_DEVICE_REACHED_NPCI_REGISTRATION_LIMIT

	}
	return &authPb.IsDeviceRegistrationAllowedResponse{
		Status:    rpc.StatusOk(),
		IsAllowed: isAllowed,
		Reason:    reason,
	}, nil
}

func (s *Service) GetDeviceRegSMSAckInfo(ctx context.Context, req *authPb.GetDeviceRegSMSAckInfoRequest) (*authPb.GetDeviceRegSMSAckInfoResponse, error) {
	var (
		errResp = func(status *rpc.Status, msg string) (*authPb.GetDeviceRegSMSAckInfoResponse, error) {
			status.SetDebugMessage(msg)
			return &authPb.GetDeviceRegSMSAckInfoResponse{
				Status:         status,
				IsAcknowledged: false,
			}, nil
		}
		successResp = func(isAcknowledged bool, attemptedAt, ackAt *timestampPb.Timestamp) (*authPb.GetDeviceRegSMSAckInfoResponse, error) {
			return &authPb.GetDeviceRegSMSAckInfoResponse{
				Status:         rpc.StatusOk(),
				IsAcknowledged: isAcknowledged,
				AckAt:          ackAt,
				AttemptedAt:    attemptedAt,
			}, nil

		}
	)
	logger.Debug(ctx, "checking if sms acknowledged")
	la, err := s.DeviceAttemptDao.GetLastAttemptByActor(ctx, req.GetActorId())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return errResp(rpc.StatusInvalidArgument(), "no attempt for dev reg for actor")
		}
		logger.Error(ctx, "error getting the last dev reg attempt", zap.Error(err))
		return errResp(rpc.StatusInternal(), err.Error())
	}
	// sanity check if dev reg attempt is from same device
	if la.GetDeviceId() != req.GetDeviceId() {
		return errResp(rpc.StatusInternal(), "device id mismatch for last attempt")
	}

	if la.GetSmsInfo().GetSmsAckAt() != nil {
		logger.Debug(ctx, fmt.Sprintf("sms acknowledgement at %v", la.GetSmsInfo().GetSmsAckAt().AsTime().String()))
		return successResp(true, la.GetCreatedAt(), la.GetSmsInfo().GetSmsAckAt())
	}
	return successResp(false, la.GetCreatedAt(), nil)
}

func (s *Service) BackFillSimInfo(ctx context.Context, req *authPb.BackFillSimInfoRequest) (*authPb.BackFillSimInfoResponse, error) {
	if strings.EqualFold(req.GetActorId(), "") || strings.EqualFold(req.GetDeviceId(), "") || strings.EqualFold(req.GetSimId(), "") {
		logger.Info(ctx, "mandatory parameters are empty")
		return &authPb.BackFillSimInfoResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("argument passed is empty"),
		}, nil
	}
	devReg, err := s.devRegDao.GetDeviceRegistration(ctx, req.GetActorId(), req.GetDeviceId())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return &authPb.BackFillSimInfoResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error getting device registration", zap.Error(err))
		return &authPb.BackFillSimInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if !strings.EqualFold(devReg.GetSimId(), "") {
		logger.Info(ctx, "sim id already present, not attempting backfill", zap.String("SimId", devReg.GetSimId()))
		return &authPb.BackFillSimInfoResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("sim id already present"),
		}, nil
	}
	if devReg.GetStatus() != authPb.DeviceRegistrationStatus_REGISTERED {
		logger.Info(ctx, "device is not registered, not back filling sim id", zap.String("SimId", devReg.GetSimId()))
		return &authPb.BackFillSimInfoResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("device not registered"),
		}, nil
	}
	devReg.SimId = req.GetSimId()
	if err = s.devRegDao.UpdateDeviceRegistrationByFilter(ctx, devReg, []authPb.DeviceRegisterationUpdateFieldMask{
		authPb.DeviceRegisterationUpdateFieldMask_SIM_ID,
	}); err != nil {
		logger.Error(ctx, "error in updating sim id at backend", zap.Error(err))
		return &authPb.BackFillSimInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &authPb.BackFillSimInfoResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func getPayloadWithSplChar() string {
	// Min 36 characters required for the payload.
	// Earlier logic: "#" + YYMMDD.HHMMSS + "%" + idgen.RandAlphaNumericString(21)
	// we saw a marginal decrease in the success rate with that logic. (not 100% attributable though)
	// Following format seems to be working fine.
	// sample payload: 41GXY9CQtWFMpV4r2XZoNM.7VVit8bcAwde=
	return idgen.RandAlphaNumericString(22) + "." + idgen.RandAlphaNumericString(12) + "="
}

func getPayloadWithoutSplChar() string {
	return idgen.RandAlphaNumericString(23) + "." + idgen.RandAlphaNumericString(12)
}
