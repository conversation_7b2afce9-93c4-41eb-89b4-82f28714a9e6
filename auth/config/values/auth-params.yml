AWS:
  Region: "ap-south-1"

Application:
  UseFFMPEGToCompressVideo: false
  BKYCHandshakeTokenSigningMethod:
    Duration: 900 # 15 minutes
  LivenessConfig:
    LivenessThreshold: 75
    LivenessThresholdRetry: 30
    OTPThreshold: 60
    OTPThresholdRetry: 50
    FMThreshold: 80
    LenientLivenessThreshold: 50
    MinimumLivenessThreshold: 70
    InhouseLivenessTimeout: 120
    StrictnessLogicAFMThreshold: 80
    StrictnessLogicALivenessThreshold: 90
    LivenessAndFacematchProbability: 10
    UserBucketName: "epifi-dev-users"

OtpConfig:
  OtpResendInterval: 30s
  GenerateOtpLimits:
    - Duration: 5m
      MaxAttempts: 5
    - Duration: 1h
      MaxAttempts: 10
  CustomGenerateOtpLimits:
    GENERATE_OTP_FLOW_HANDSHAKE_OTP:
      - Duration: 5m
        MaxAttempts: 100
      - Duration: 1h
        MaxAttempts: 100

Flags:
  EnablePinRetriesExceededEventConsumer: true
  EnableAFURiskScreeningForNRUser: false
  TrimDebugMessageFromStatus: true
  EnableV2IdTokenVerifierForAndroid: false
  EnableCheckForAccessRevoke: true
  DisableLivenessAttemptChangeFeed: false
  UseNewPassingLogic: true
  RemoveTokenStoresUpdates: true
  EnableSMSAckListener: true
  EnableSmsHealthEngine: true
  EnableFMV2ForOnboarding: false
  EnableOrchestratorLivenessV2: true
  EnableEKYCInPhoneUpdate: true
  EnableDeviceRegSMSPayloadGenerationLimit:
    MinAndroidVersion: 1
    MinIosVersion: 1
    FallbackToEnableFeature: false
    DisableFeature: false

Liveness:
  OnboardingLivenessRuleId: "5dae4933-e695-4175-997b-22a923f77fdb"

TokenStoresCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "30m"
  RefreshTokenCacheTTL: "30m"
  TokenStoresPrefix: "tok:"
  TokenStoresPrefixV2: "tok"
  EnableWritesOnlyForUnOnboardedUsers: false
  DisableCachingByTokenIdForAccessTokens: false
  EnableCachingBySubject: true
  TokenCacheTTLMap:
    - REFRESH_TOKEN: "30m"
    - ACCESS_TOKEN: "15m"
  UseTokenStoresDaoV2: false

KeyCompromisedCheckConfig:
  IsUserGroupEnabled: true
  AllowedUserGroup:
    - 1 # INTERNAL
  # PartnerSdkKeyCompromised status map hold the info for keys id with their compromised status
  # As per current contract with vendor(Federal), we will get status of keyId compromised (in case of breach).
  # To propagate this error to client we will update status here.
  PartnerSdkKeyCompromisedStatusMap:
    ANDROID:
      "d6b0cf3d-afff-475b-b5e9-70a6107698a5": false
      # M2P
      "87a9f0b9-a3fa-45c2-b196-3e42a7ef7eb2": false
    IOS:
      "d6b0cf3d-afff-475b-b5e9-70a6107698a5": false
      "43077517-5edf-4000-8234-d929c9c38f3b": false
      # M2P
      "f901b718-65e3-428d-9cb7-19350b49869f": false

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/auth/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

DeviceIntegrityConfig:
  DeviceAttestationV2Cfg:
    MinAndroidVersion: 1000
    MinIosVersion: 1000
    FallbackToEnableFeature: false
  ExpiryTimeForDeviceIntegrity: 1h5m
  AsyncDeviceIntegrityRolloutPercentage: 100
  WhitelistedAttestationTokens: [ "DUMMY_TOKEN" ]
  ExpiryTimeForDeviceIntegrityV2: 168h # 7 days = 168 hours
  RandomExpiryInSecondsForDeviceIntegrityV2: 10800 # 3 hours
  OnDeviceIntegrityCheckCfg:
    MinAndroidVersion: 220
    MinIosVersion: 9000
    FallbackToEnableFeature: false
  BypassPhoneNumbers:
    - 916666012345
    - 911111654321
    - 919380570497
    - 911111606060

AFU:
  ReadOnlyFromAtmPinCheckFlag: false
  EnableATMPinEligibilityCheck: true
  # disabling in non prod envs to allow existing iOS testing devices to migrate to the required version
  EnableDeviceRegistrationOSValidation: false
  AFUCooldown:
    EnableCooldown: true
    CooldownForUPIRegulation: 24h
    CooldownDurationMap:
      - PHONE_NUM: 4320h  # 180 days
      - EMAIL: 168h  # 7 days
      - DEVICE: 1h
      - SIM: 1h
  CredentialsOrder:
    UPDATE_PHONE_NUM:
      - [ ATM_PIN_VALIDATION, LIVENESS_FM_VALIDATION ]
    UPDATE_EMAIL:
      - [ ATM_PIN_VALIDATION, LIVENESS_FM_VALIDATION ]
    UPDATE_PHONE_NUM_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_DEVICE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_DEVICE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_DEVICE:
      - [ ATM_PIN_VALIDATION, LIVENESS_FM_VALIDATION ]
    UPDATE_EMAIL_SIM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_SIM: [ ]
  LivenessManualReviewExpiryTime: 24h
  AFULivenessExternalRuleId: "Afu Liveness queue External"
  AFUModelExternalRuleId: "DS_AFU_RISK_MODEL_2"
  EnableReRegInitRetry: true
  ExpiryForEKYC: "72h" # 3 days
  VendorUpdateConsumerLockConfig:
    Timeout: "15s"

Tracing:
  Enable: false

EpifiDb:
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

DeviceIntegrityNonceCacheConfig:
  IsCachingEnabled: true
  CacheTTL: "10m"
  ForceOnlyCache: true

DeviceRegistrationCacheConfig:
  IsCachingEnabled: true
  CacheTTL: "1h"

GetSessionParams:
  # Lease duration for process execution lock.
  GetSessionParamsLockLeaseDuration: 4s

  # Prefix string for process execution lock
  GetSessionParamsLock: "sessParLk"

DeviceRegistrationSMS:
  EndpointsEnabled:
    - 919743994779: false
    - 919743994780: true
    - 919743994781: false
  DownThrottleDisabledToPercent: 10
  EnableV2SMSEndpointSelector: true
  MaxDeviceRegAttemptPerDay: 5

QuestSdk:
  Disable: true # make this false in respective env yaml files to enable quest

TokenManagerConfig:
  Enable: "TRUE"
  DisableFallbackToDBForAccessTokens: false

NetworthMcpConfig:
  EnableSessionSigning: true
