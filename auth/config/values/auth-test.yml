Application:
  Environment: "test"
  Name: "auth"
  AndroidClientSignature: "TELeVUAMntb"
  OAuthAndroidClientID: "432746002179-4q86u70ot3opldm79u9qnd82icd3b64h.apps.googleusercontent.com"
  OAuthIOSClientID: "411450025013-t1n09gmk2n6cf2dplh69mmsrktkgmlqj.apps.googleusercontent.com"
  UseFFMPEGToExtractFrame: false
  DeviceIntegrityNonceValidityInSecs: 300
  UserAccessTokenSigningMethod:
    # Keeping it equal to 60 minutes same as the validity of OAuth id token returned by Google
    # 60 * 60
    Duration: 3600
    # 15 minutes * 60
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: true
  UserRefreshTokenSigningMethod:
    # 30 days => 60 * 60 * 24 * 30
    Duration: 2592000
    # No need of inactivity timer on refresh token as that's enforced on access token
    InactivityTimer: 2592000
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WaitlistUserAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  AppInsightsAccessTokenSigningMethod:
    Duration: 900
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: false
  ChatbotAccessTokenSigningMethod:
    # 24hrs -> 24 * 60 * 60 = 86400
    # the total duration of a chat session can extend beyond 1hr in agent's conversation
    Duration: 86400
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: false
  WebLiteAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: false
  GenieRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  GenieAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  WebCABTRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WebCABTAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  RiskOutcallWebformAccessTokenSigningMethod:
    # Keeping it equal to 30 minutes
    Duration: 1800
    # Keeping it 30 minutes.
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: true
  NetworthMcpAccessTokenSigningMethod:
    Duration: 1800
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  SMSConfig:
    PhoneNumbers:
      - 9743994779
      - 9743994780
      - 9743994781
    DeviceRegistrationPrefix: "EPIFY "
  NPCIDeviceBindingLimitPerDay: 3
  SkipClientIDCheck: true
  OAuthVerifierType: 1 #Stub
  LivenessConfig:
    LivenessThreshold: 75
    LivenessThresholdRetry: 30
    OTPThreshold: 70
    OTPThresholdRetry: 30
    FMThreshold: 60
    LenientLivenessThreshold: 50
    InhouseLivenessTimeout: 1
  AppleOAuthIOSClientID: "com.epifi.fi.qa"
  AppleClientSecretValidityInMinutes: "10m"

Server:
  Ports:
    GrpcPort: 8086
    GrpcSecurePort: 9503
    HttpPort: 9850

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 3
  HystrixCommand:
    CommandName: "auth_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ServerEndpoints:
  SimulatorGrpcEndpoint: "localhost:9090"
  SimulatorHttpEndpoint: "localhost:8150"
  VendorgatewayEndpoint: "localhost:8081"
  FrontendEndpoint: "localhost:8082"
  UserEndpoint: "localhost:8083"
  SavingsEndpoint: "localhost:8084"
  CommsEndpoint: "localhost:8085"
  AuthEndpoint: "localhost:8086"
  PaymentinstrumentEndpoint: "localhost:8087"
  KycEndpoint: "localhost:8090"
  WaitlistEndPoint: "localhost:8105"

KarzaLivenessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "check-liveness-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeviceRegSMSAckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "device-reg-sms-ack-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

LivenessStatusPublisher:
  QueueName: "liveness-status-queue"

LivenessManualReviewPublisher:
  TopicName: "liveness-manual-review-topic"

LivenessSummaryCompletedEventPublisher:
  TopicName: "liveness-summary-completed-event-topic"

LivenessStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "liveness-status-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 15
      TimeUnit: "Second"

Aws:
  Endpoint: "localhost:4576"
  Region: "ap-south-1"
  S3:
    LivenessBucketName: "epifi-liveness"

OtpConfig:
  OtpResendInterval: 3s
  GenerateOtpLimits:
    - Duration: 5m
      MaxAttempts: 50
    - Duration: 1h
      MaxAttempts: 100
  SkipOTPValidationForNumbers:
    - "************"
    - "************"

BankConfig:
  CardPinLength: 4
  SecurePinLength: 4
  BankLogoUrl: "https://epifi-icons.pointz.in/fibank/icon/96.png"
  BankName: "Federal Bank"
  VendorOtpLength: 6

DeviceIntegrityConfig:
  AndroidAppPackageNames:
    - "com.epifi.paisa.local"
  AndroidApkCertificateDigestSha256:
    - "0kYoXaKxm/apSWnciRJqY3N3YBDSyUvuoUOKKVkgCo4="
  DeviceIntegrityNonceValidityInSecs: 300
  CheckApkPackageName: true
  CheckCertificateHash: true
  AppleAppAttestRootCertificate: |
    -----BEGIN CERTIFICATE-----
    MIICITCCAaegAwIBAgIQC/O+DvHN0uD7jG5yH2IXmDAKBggqhkjOPQQDAzBSMSYw
    JAYDVQQDDB1BcHBsZSBBcHAgQXR0ZXN0YXRpb24gUm9vdCBDQTETMBEGA1UECgwK
    QXBwbGUgSW5jLjETMBEGA1UECAwKQ2FsaWZvcm5pYTAeFw0yMDAzMTgxODMyNTNa
    Fw00NTAzMTUwMDAwMDBaMFIxJjAkBgNVBAMMHUFwcGxlIEFwcCBBdHRlc3RhdGlv
    biBSb290IENBMRMwEQYDVQQKDApBcHBsZSBJbmMuMRMwEQYDVQQIDApDYWxpZm9y
    bmlhMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERTHhmLW07ATaFQIEVwTtT4dyctdh
    NbJhFs/Ii2FdCgAHGbpphY3+d8qjuDngIN3WVhQUBHAoMeQ/cLiP1sOUtgjqK9au
    Yen1mMEvRq9Sk3Jm5X8U62H+xTD3FE9TgS41o0IwQDAPBgNVHRMBAf8EBTADAQH/
    MB0GA1UdDgQWBBSskRBTM72+aEH/pwyp5frq5eWKoTAOBgNVHQ8BAf8EBAMCAQYw
    CgYIKoZIzj0EAwMDaAAwZQIwQgFGnByvsiVbpTKwSga0kP0e8EeDS4+sQmTvb7vn
    53O5+FRXgeLhpJ06ysC5PrOyAjEAp5U4xDgEgllF7En3VcE3iexZZtKeYnpqtijV
    oyFraWVIyd/dganmrduC1bmTBGwD
    -----END CERTIFICATE-----
  AppleCredCertOidExtensionSeq: [ 1, 2, 840, 113635, 100, 8, 2 ]
  IosAppIdentifiers:
    - "appIdentifier"
  AllowSafetynetCertChainVerificationWithModifiedCurrTime: true
  CurrTimeOverrideForSafetynetCertVerification: "-48h"
  DeviceAttestationV2Cfg:
    MinAndroidVersion: 10
    MinIosVersion: 10
  ExpiryTimeForDeviceIntegrity: 1h5m
  MockSafetynetTokenResult:
    SAFETYNET_CTS_PROFILE_MATCH_TEST_FAILED: "deviceId-1"
    SAFETYNET_BASIC_INTEGRITY_TEST_FAILED: "deviceId-2"
    ERROR_EXTRACTING_ATTESTATION_PAYLOAD: "deviceId-3"
    SAFETYNET_ATTESTATION_CREATION_FAILURE: "deviceId-4"

AFU:
  EnableATMPinEligibilityCheck: true
  EnableDeviceRegistrationOSValidation: true
  AFUCooldown:
    EnableCooldown: true
    CooldownForUPIRegulation: 24h
    CooldownDurationMap:
      - PHONE_NUM: 4320h  # 180 days
      - EMAIL: 168h  # 7 days
      - DEVICE: 1h
      - SIM: 1h
  CredentialsOrder:
    UPDATE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_PHONE_NUM_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_DEVICE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_DEVICE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_EMAIL_SIM:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_DEVICE:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_SIM: [ ]
  VendorUpdateProducerDelayInSecs: 1
  BypassCredentialVerificationForPhoneNumbers:
    - ************
    - ************
  MaxRecordsDepthForAFUTroubleshooter: 25
  MinVersionForAFURetry:
    MinAndroidVersion: 10
    MinIOSVersion: 10
    FallbackToEnableFeature: true
  AuthFactorUpdateCacheConfig:
    IsCachingEnabled: true
    AuthFactorUpdatePrefix: "AFU_"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false
  SkipIOSIdTokenExpiryCheck: false
  EnableCheckForAccessRevoke: true
  UseNewPassingLogic: true
  EnableDeviceRegSMSPayloadGenerationLimit:
    MinAndroidVersion: 1
    MinIosVersion: 1
    FallbackToEnableFeature: false
    DisableFeature: true

AFUVendorUpdatePublisher:
  QueueName: "afu-vendor-update-queue"

AFUVendorUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "afu-vendor-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 3
      MaxAttempts: 50
      TimeUnit: "Second"

AFUManualReviewNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "afu-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Minute"

ProcessPinAttemptsExceededEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "auth-pin-attempts-exceeded-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Minute"


AuthFactorUpdatePublisher:
  TopicName: "auth-factor-update"

DeviceReregCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "device-rereg-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

BiometricEventPublisher:
  QueueName: "biometrics-details-queue"

ReturnOtpTokenOnError: true

KeyCompromisedCheckConfig:
  IsUserGroupEnabled: true
  AllowedUserGroup:
    - 1 # INTERNAL
  # PartnerSdkKeyCompromised status map hold the info for keys id with their compromised status
  # As per current contract with vendor(Federal), we will get status of keyId compromised (in case of breach).
  # To propagate this error to client we will update status here.
  # true -> mean key (corresponding to keyId) is compromised
  PartnerSdkKeyCompromisedStatusMap:
    ANDROID:
      "d6b0cf3d-afff-475b-b5e9-70a6107698a5": false
      "d6b0cf3d-afff-475b-b5e9-70a6107698a6": true
    IOS:
      "d6b0cf3d-afff-475b-b5e9-70a6107698a5": false
      "d6b0cf3d-afff-475b-b5e9-70a6107698a6": true

DeviceLocationCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "2m"
  DeviceLocationPrefix: "device_location_"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DeviceRegistrationCacheConfig:
  IsCachingEnabled: false
  CacheTTL: "72h" # 3days

DeviceRegistrationSMS:
  EndpointsEnabled:
    - ************: true
    - ************: true
    - ************: false
    - ************: true
  DownThrottleDisabledToPercent: 10
  EnableV2SMSEndpointSelector: true

DeviceRegistration:
  SMSAckUrl: "http://localhost:9098/openbanking/auth/federal/user-device/registration-sms-update"

Keycloak:
  RealmName: "master"
  BaseURL: "http://localhost:5400/"

# Create and add secrets json here.
AuthSecrets:
  SecretsKey: "{\"activeUserAccessTokenSigningKey\":\"Isignshorttokens\",\"activeUserRefreshTokenSigningKey\":\"Isignshorttokens\",\"activeWaitlistUserAccessTokenSigningKey\":\"Isighshorttokens2\",\"activeAppInsightsAccessTokenSigningKey\":\"Isignshorttokens3\",\"activeChatbotAccessTokenSigningKey\":\"Isignshorttokens4\",\"activeWebLiteAccessTokenSigningKey\":\"Isignshorttokens5\",\"activeDeviceIntegrityTokenSigningKey\":\"Isignshorttokens6\",\"retiredUserAccessTokenSigningKey\":\"Isignshorttokens1\",\"retiredUserRefreshTokenSigningKey\":\"Isignshorttokens1\",\"retiredWaitlistUserAccessTokenSigningKey\":\"Isignshorttokens2\",\"deviceIdsEnabledForSafetyNetV2\":\"[]\",\"playIntegrityKeys\":\"{\\\"decryptionkey\\\": \\\"decryptionkey\\\",\\\"verificationkey\\\": \\\"verificationkey\\\"}\",\"keycloakBKYC\":\"{\\\"adminusername\\\": \\\"admin\\\",\\\"adminpassword\\\": \\\"admin\\\",\\\"clientid\\\": \\\"sampleClient\\\",\\\"clientsecret\\\": \\\"sampleSecret\\\"}\",\"bkycHandshakeTokenSigningKey\":\"Isignshorttokens7\",\"appleOAuthIds\":\"\",\"totpSaltNwMcp\":\"sampleSecret\",\"activeNwMcpAccessTokenSigningKey\":\"sampleSecret\",\"sessionSigningKeyNwMcp\":\"sampleSecret\"}"

TokenStoresCacheConfig:
  TokenCacheTTLMap:
    - REFRESH_TOKEN: "30m"
    - ACCESS_TOKEN: "30m"
  UseTokenStoresDaoV2: true

AuthTokenCreationPublisher:
  TopicName: "auth-token-creation-topic"

NetworthMcpConfig:
  LoginUrl: "http://localhost:3000/wealth-mcp-login?token="
  EnableSessionSigning: true
