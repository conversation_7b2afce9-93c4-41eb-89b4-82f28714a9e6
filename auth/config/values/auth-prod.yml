Application:
  Environment: "prod"
  Name: "auth"
  AndroidClientSignature: "82275JXpmM1"
  OAuthAndroidClientID: "924130978752-frcue9v4nbi41mrsad929pqrsvm3v90v.apps.googleusercontent.com"
  OAuthIOSClientID: "924130978752-908b2p0qhg8j0neltr39dt41tev99gvp.apps.googleusercontent.com"
  UseFFMPEGToExtractFrame: true
  BKYCHandshakeTokenSigningMethod:
    Duration: 900 # 15 minutes
  UserAccessTokenSigningMethod:
    # Keeping it equal to 60 minutes same as the validity of OAuth id token returned by Google
    Duration: 3600
    # Ideally supposed to be around 5-15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  UserRefreshTokenSigningMethod:
    # 6 months => ~ 6 * 30 days => 60 * 60 * 24 * 30 * 6
    Duration: 15552000
    # No need of inactivity timer on refresh token as that's enforced on access token
    InactivityTimer: 15552000
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WaitlistUserAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  AppInsightsAccessTokenSigningMethod:
    Duration: 900
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: false
  ChatbotAccessTokenSigningMethod:
    # 24hrs -> 24 * 60 * 60 = 86400
    # the total duration of a chat session can extend beyond 1hr in agent's conversation
    Duration: 86400
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: false
  WebLiteAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: false
  GenieRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  GenieAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  WebCABTRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WebCABTAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  RiskOutcallWebformAccessTokenSigningMethod:
    # Keeping it equal to 30 minutes
    Duration: 1800
    # Keeping it 30 minutes.
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: true
  NetworthMcpAccessTokenSigningMethod:
    Duration: 1800
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  SMSConfig:
    PhoneNumbers:
      - 7669800900
      - 8828111222
    DeviceRegistrationPrefix: "EPIFY "
  NPCIDeviceBindingLimitPerDay: 3
  AppleOAuthIOSClientID: "com.epifi.fi"
  AppleClientSecretValidityInMinutes: "10m"
  IsSecureRedis: true
  LivenessConfig:
    LivenessAndFacematchProbability: 0
    UserBucketName: "epifi-prod-users"

Server:
  Ports:
    GrpcPort: 8086
    GrpcSecurePort: 9503
    HttpPort: 9999
    HttpPProfPort: 9990

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-11912.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11912"
  AuthDetails:
    SecretPath: "prod/redis/auth/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: auth
  HystrixCommand:
    CommandName: "auth_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 30

KarzaLivenessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-check-liveness-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

LivenessStatusPublisher:
  QueueName: "prod-liveness-status-queue"

LivenessManualReviewPublisher:
  TopicName: "prod-liveness-manual-review-topic"

LivenessSummaryCompletedEventPublisher:
  TopicName: "prod-liveness-summary-completed-event-topic"

LivenessStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-liveness-status-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 15
      TimeUnit: "Second"

BiometricEventPublisher:
  QueueName: "prod-biometrics-details-queue"

Aws:
  Endpoint: "localhost:4576"
  Region: "ap-south-1"
  S3:
    LivenessBucketName: "epifi-prod-liveness"

OtpConfig:
  OtpResendInterval: 30s
  GenerateOtpLimits:
    - Duration: 5m
      MaxAttempts: 5
    - Duration: 1h
      MaxAttempts: 10
  CustomGenerateOtpLimits:
    GENERATE_OTP_FLOW_HANDSHAKE_OTP:
      - Duration: 5m
        MaxAttempts: 100
      - Duration: 1h
        MaxAttempts: 200

BankConfig:
  CardPinLength: 4
  SecurePinLength: 4
  BankLogoUrl: "https://epifi-icons.pointz.in/fibank/icon/96.png"
  BankName: "Federal Bank"
  VendorOtpLength: 6

DeviceIntegrityConfig:
  AndroidAppPackageNames:
    - "com.epifi.paisa"  # prod app package name
  AndroidApkCertificateDigestSha256:
    - "3UxG56fjFkLebdnpDfW3Hsni8haL18qnM82q4m5iFeE=" # release build
    - "fvcKVyDF4Ik/MTZK0eYMU4mF4gSUXCK8MZNP+paCT2E=" # debug build
  DeviceIntegrityNonceValidityInSecs: 300
  CheckApkPackageName: true
  CheckCertificateHash: true
  AppleAppAttestRootCertificate: |
    -----BEGIN CERTIFICATE-----
    MIICITCCAaegAwIBAgIQC/O+DvHN0uD7jG5yH2IXmDAKBggqhkjOPQQDAzBSMSYw
    JAYDVQQDDB1BcHBsZSBBcHAgQXR0ZXN0YXRpb24gUm9vdCBDQTETMBEGA1UECgwK
    QXBwbGUgSW5jLjETMBEGA1UECAwKQ2FsaWZvcm5pYTAeFw0yMDAzMTgxODMyNTNa
    Fw00NTAzMTUwMDAwMDBaMFIxJjAkBgNVBAMMHUFwcGxlIEFwcCBBdHRlc3RhdGlv
    biBSb290IENBMRMwEQYDVQQKDApBcHBsZSBJbmMuMRMwEQYDVQQIDApDYWxpZm9y
    bmlhMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERTHhmLW07ATaFQIEVwTtT4dyctdh
    NbJhFs/Ii2FdCgAHGbpphY3+d8qjuDngIN3WVhQUBHAoMeQ/cLiP1sOUtgjqK9au
    Yen1mMEvRq9Sk3Jm5X8U62H+xTD3FE9TgS41o0IwQDAPBgNVHRMBAf8EBTADAQH/
    MB0GA1UdDgQWBBSskRBTM72+aEH/pwyp5frq5eWKoTAOBgNVHQ8BAf8EBAMCAQYw
    CgYIKoZIzj0EAwMDaAAwZQIwQgFGnByvsiVbpTKwSga0kP0e8EeDS4+sQmTvb7vn
    53O5+FRXgeLhpJ06ysC5PrOyAjEAp5U4xDgEgllF7En3VcE3iexZZtKeYnpqtijV
    oyFraWVIyd/dganmrduC1bmTBGwD
    -----END CERTIFICATE-----
  AppleCredCertOidExtensionSeq: [ 1, 2, 840, 113635, 100, 8, 2 ]
  IosAppIdentifiers:
    - "com.epifi.fi"
  AllowSafetynetCertChainVerificationWithModifiedCurrTime: true
  CurrTimeOverrideForSafetynetCertVerification: "-48h"
  DeviceAttestationV2Cfg:
    DisableFeature: false
    MinAndroidVersion: 183
    MinIosVersion: 100000
  AsyncDeviceIntegrityRolloutPercentage: 100
  ExpiryTimeForDeviceIntegrity: 1h5m
  ExpiryTimeForDeviceIntegrityV2: 168h # 7 days = 168 hours
  RandomExpiryInSecondsForDeviceIntegrityV2: 10800 # 3 hours
  OnDeviceIntegrityCheckCfg:
    MinAndroidVersion: 220
    MinIosVersion: 9000
    FallbackToEnableFeature: false

AFU:
  ReadOnlyFromAtmPinCheckFlag: false
  EnableATMPinEligibilityCheck: true
  EnableDeviceRegistrationOSValidation: true
  AFUCooldown:
    EnableCooldown: true
    CooldownForUPIRegulation: 24h
    CooldownDurationMap:
      - PHONE_NUM: 4320h  # 180 days
      - EMAIL: 168h  # 7 days
      - DEVICE: 1h
      - SIM: 1h
  CredentialsOrder:
    UPDATE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
    UPDATE_PHONE_NUM_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_DEVICE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_DEVICE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_EMAIL_SIM:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_DEVICE:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_SIM: [ ]
  VendorUpdateProducerDelayInSecs: 1
  ReRegInitIncrementalDelay: 12s
  LivenessManualReviewExpiryTime: 24h
  MaxRecordsDepthForAFUTroubleshooter: 25
  AuthFactorUpdateCacheConfig:
    IsCachingEnabled: true
    AuthFactorUpdatePrefix: "AFU_"
  MinVersionForAFURetry:
    MinAndroidVersion: 1000
    MinIOSVersion: 1000
    FallbackToEnableFeature: true
    DisableFeature: true
  AFULivenessExternalRuleId: "AFU_LIVENESS_1"
  AFUModelExternalRuleId: "DS_AFU_RISK_MODEL_2"
  EnableReRegInitRetry: true
  ExpiryForEKYC: "72h" # 3 days
  VendorUpdateConsumerLockConfig:
    Timeout: "15s"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Flags:
  EnablePinRetriesExceededEventConsumer: true
  EnableAFURiskScreeningForNRUser: false
  DisableLivenessAttemptChangeFeed: true
  EnableCheckForAccessRevoke: false
  EnableV2IdTokenVerifierForAndroid: false
  EnableLastActivityCache: false
  EnableLivenessManualReviewInAfu: true
  SkipIOSIdTokenExpiryCheck: false
  TrimDebugMessageFromStatus: false
  UseNewPassingLogic: true
  RemoveTokenStoresUpdates: true
  EnableSMSAckListener: true
  EnableSmsHealthEngine: true
  EnableFMV2ForOnboarding: false
  EnableOrchestratorLivenessV2: true
  EnableEKYCInPhoneUpdate: true
  EnableDeviceRegSMSPayloadGenerationLimit:
    MinAndroidVersion: 10000
    MinIosVersion: 10000
    FallbackToEnableFeature: false
    DisableFeature: true

AFUVendorUpdatePublisher:
  QueueName: "prod-afu-vendor-update-queue"

AFUVendorUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-afu-vendor-update-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~5 min post that regular interval is followed for next ~16h
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 7
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 100
          TimeUnit: "Minute"
      MaxAttempts: 107
      CutOff: 7
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "auth"

AFUManualReviewNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-afu-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "auth"

ProcessPinAttemptsExceededEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-auth-pin-attempts-exceeded-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "auth"

DeviceRegSMSAckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-device-reg-sms-ack-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "auth"

AuthFactorUpdatePublisher:
  TopicName: "prod-auth-factor-update"

DeviceReregCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-device-rereg-callback-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 7
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 5
          MaxAttempts: 15
          TimeUnit: "Minute"
      MaxAttempts: 22
      CutOff: 6
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "auth"

ReturnOtpTokenOnError: false

KeyCompromisedCheckConfig:
  IsUserGroupEnabled: true
  AllowedUserGroup:
    - 1 # INTERNAL
  # PartnerSdkKeyCompromised status map hold the info for keys id with their compromised status
  # As per current contract with vendor(Federal), we will get status of keyId compromised (in case of breach).
  # To propagate this error to client we will update status here.
  # true -> mean key (corresponding to keyId) is compromised
  # TODO(vivek): Add IOS key id for production on IOS prod release.
  PartnerSdkKeyCompromisedStatusMap:
    ANDROID:
      "8a671cb1-3222-4abd-bfaf-b5b53ac35f97": false
      "a781-45de-aea0-f374aeaa038f": false
      # M2P
      "69ddf8f2-1c4b-4425-b907-0082e490a4ba": false
      "df15edc5-250a-421a-8768-5dd4631b8aa2": false
    IOS:
      "3d34c003-19bb-4261-b245-e6904d9fc5f0": false
      "58fba4d2-9f16-412c-a9d1-03e7a4083538": false
      "01fd01ec-b402-45d6-bf1f-60763d7b9c9e": false
      "f7353d2e-d591-431f-8d11-3817bde1bb60": false
      # M2P
      "054f08e0-ea27-4ca8-b2f7-86edbba0c412": false
      "4b9c77e8-8ab1-42ba-8f31-4a296f5cb66b": false

TokenStoresCacheConfig:
  IsCachingEnabled: true
  CacheTTL: "30m"
  RefreshTokenCacheTTL: "24h" #1 day
  TokenStoresPrefix: "tok:"
  TokenStoresPrefixV2: "tok"
  DisableTokenStoreWrites: false
  MakeTokenStoreWritesOptional: false
  EnableWritesOnlyForUnOnboardedUsers: false
  DisableCachingByTokenIdForAccessTokens: true
  EnableCachingBySubject: true
  TokenCacheTTLMap:
    - REFRESH_TOKEN: "24h"
    - ACCESS_TOKEN: "15m"
  UseTokenStoresDaoV2: true

DeviceLocationCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "240h" # 24 * 10 days
  DeviceLocationPrefix: "device_location_"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: true
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: true
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DeviceIntegrityNonceCacheConfig:
  IsCachingEnabled: true
  CacheTTL: "10m"
  ForceOnlyCache: true

DeviceRegistrationCacheConfig:
  IsCachingEnabled: true
  CacheTTL: "1h"

DeviceRegistrationSMS:
  EndpointsEnabled:
    - 918828111222: true
    - 918828800574: true
    - 918828811024: true
    - 918750614141: false
    - 917065087676: false

  DownThrottleDisabledToPercent: 10
  EnableV2SMSEndpointSelector: true
  MaxDeviceRegAttemptPerDay: 3

Liveness:
  OnboardingLivenessRuleId: "3c49be64-92c3-4091-a70f-335aa922c51b"

Keycloak:
  RealmName: "Onboarding"
  BaseURL: "https://keycloak-prod.pointz.in/"

AuthSecrets:
  SecretsKey: "prod/auth/secrets"

TokenManagerConfig:
  Enable: "TRUE"
  WhitelistedActorIds: [ "AC210722voRQ2x7nStWQDZ0pBV9fdA==","AC220930A5DzsNW0SEqjPOtOMAdtIw==", "AC210721BAKDIpfuSV6avTCqaNqs/w==", "AC210603ok4ygB5kTz2W6UQ1EtTCcA==" ]
  WhitelistedPhoneNumberHashes: [ "2af8052055013fdbdd89b0e2a967e5fa8b93542b","9268ba19ef4627251abdb93dbd4f54efc78d9896","4f01c74a009ff3d6755d74d743696d411bc3fb9c", "c834564139ee43ef0d82ea598a0231ca1e0b0f9f" ]
  DisableFallbackToDBForAccessTokens: false

# config not required, adding to avoid getting populated in config difference job
DeviceRegistration:
  SMSAckUrl: ""

AuthTokenCreationPublisher:
  TopicName: "prod-auth-token-creation-topic"

NetworthMcpConfig:
  LoginUrl: "http://fi.money/wealth-mcp-login?token="
