// nolint: dogsled
package tiering

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	pkgDateTime "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	mock_events "github.com/epifi/be-common/pkg/events/mocks"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/segment"
	mocks2 "github.com/epifi/gamma/api/segment/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/criteria"
	"github.com/epifi/gamma/tiering/dao"
	mockDao "github.com/epifi/gamma/tiering/dao/mocks"
	"github.com/epifi/gamma/tiering/data"
	"github.com/epifi/gamma/tiering/evaluator"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/movement"
	"github.com/epifi/gamma/tiering/release"
	"github.com/epifi/gamma/tiering/test"
	"github.com/epifi/gamma/tiering/test/mocks"
	mock_tier_options "github.com/epifi/gamma/tiering/test/mocks/tier_options"
	"github.com/epifi/gamma/tiering/tier_options"
	"github.com/epifi/gamma/tiering/timeline"
)

var (
	gconf *genconf.Config

	FiveTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{},
			},
		}
	}

	TenTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{},
			},
		}
	}

	OneHundredTierOptions = func(minBalance *gmoney.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Balance{
								Balance: &criteriaPb.Balance{
									MinBalance: minBalance,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	OneThousandTierOptions = func(minBalance *gmoney.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Balance{
								Balance: &criteriaPb.Balance{
									MinBalance: minBalance,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	TwoThousandTierOptions = func(minSalaryAmount *gmoney.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									MinSalary: minSalaryAmount,
								},
							},
						},
					},
				},
			},
		}
	}

	tierOptionsMap = map[tieringEnumPb.Tier][]*criteriaPb.Option{
		tieringEnumPb.Tier_TIER_FIVE: FiveTierOptions(),
		tieringEnumPb.Tier_TIER_TEN:  TenTierOptions(),
		tieringEnumPb.Tier_TIER_ONE_HUNDRED: OneHundredTierOptions(&gmoney.Money{
			CurrencyCode: "INR",
			Units:        10000,
		}),
		tieringEnumPb.Tier_TIER_ONE_THOUSAND: OneThousandTierOptions(&gmoney.Money{
			CurrencyCode: "INR",
			Units:        50000,
		}),
		tieringEnumPb.Tier_TIER_TWO_THOUSAND: TwoThousandTierOptions(&gmoney.Money{
			CurrencyCode: "INR",
			Units:        25000,
		}),
	}

	actorScreenInteraction = func() *tieringPb.ActorScreenInteraction {
		return &tieringPb.ActorScreenInteraction{
			ActorId: "actor-1",
			Screen:  tieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
			Status:  tieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_VISITED,
		}
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, gconf, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_GetTieringPitchV2(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockTmhDao := mockDao.NewMockTierMovementHistoryDao(ctrl)
	mockEtmDao := mockDao.NewMockEligibleTierMovementDao(ctrl)
	mockAtiDao := mockDao.NewMockActorTierInfoDao(ctrl)
	mockMovementManager := mocks.NewMockTierMovementManager(ctrl)
	mockCriteriaManager := mocks.NewMockTierCriteria(ctrl)
	mockTimelineManager := mocks.NewMockTierTimeline(ctrl)
	mockOptionsManager := mock_tier_options.NewMockManager(ctrl)
	mockDataProcessor := mocks.NewMockTieringDataProcessor(ctrl)
	defer ctrl.Finish()

	type mockStruct struct {
		mockTmhDao          *mockDao.MockTierMovementHistoryDao
		mockEtmDao          *mockDao.MockEligibleTierMovementDao
		mockAtiDao          *mockDao.MockActorTierInfoDao
		mockCriteriaManager *mocks.MockTierCriteria
		mockTimelineManager *mocks.MockTierTimeline
		mockOptionsManager  *mock_tier_options.MockManager
		mockMovementManager *mocks.MockTierMovementManager
		mockDataProcessor   *mocks.MockTieringDataProcessor
	}

	type fields struct {
		releaseManager          release.Manager
		tierMovementHistoryDao  dao.TierMovementHistoryDao
		eligibleTierMovementDao dao.EligibleTierMovementDao
		actorTierInfoDao        dao.ActorTierInfoDao
		criteriaManager         criteria.TierCriteria
		tierTimelineManager     timeline.TierTimeline
		movementManager         movement.TierMovementManager
		tierOptionsManager      tier_options.Manager
		dataProcessor           data.TieringDataProcessor
	}
	type args struct {
		ctx   context.Context
		req   *tieringPb.GetTieringPitchV2Request
		mocks func(mockStruct *mockStruct)
	}
	curTimestamp := timestampPb.Now()
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringPb.GetTieringPitchV2Response
		wantErr bool
	}{
		{
			name: "success, tier 10, not in grace, not in cool off",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(false, nil, nil)
					m.mockMovementManager.EXPECT().IsEligibleForTierUpgrade(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(true, nil).Times(3)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status:      rpcPb.StatusOk(),
				CurrentTier: tieringExtPb.Tier_TIER_FI_BASIC,
				MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
					{
						TierName:          tieringExtPb.Tier_TIER_FI_REGULAR,
						IsMovementAllowed: false,
						MovementTimestamp: nil,
						Options:           FiveTierOptions(),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_BASIC,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options:           TenTierOptions(),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options: OneHundredTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        10000,
						}),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options: OneThousandTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        50000,
						}),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options: TwoThousandTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        25000,
						}),
					},
				},
				LastUpgradeDetails:   nil,
				LastDowngradeDetails: nil,
				ActorBaseTier:        tieringExtPb.Tier_TIER_FI_BASIC,
			},
			wantErr: false,
		},
		{
			name: "success, tier 100, in grace, not in cool off",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(true, &timeline.GraceDetails{MovementTimestamp: curTimestamp, GracePeriod: 1296000}, nil)
					m.mockMovementManager.EXPECT().IsEligibleForTierUpgrade(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(true, nil).Times(2)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status:      rpcPb.StatusOk(),
				CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
				MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
					{
						TierName:          tieringExtPb.Tier_TIER_FI_REGULAR,
						IsMovementAllowed: false,
						MovementTimestamp: nil,
						Options:           FiveTierOptions(),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_BASIC,
						IsMovementAllowed: false,
						MovementTimestamp: nil,
						Options:           TenTierOptions(),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
						IsMovementAllowed: false,
						MovementTimestamp: curTimestamp,
						Options: OneHundredTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        10000,
						}),
						GraceParams: &tieringExtPb.GraceParams{Period: 1296000},
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options: OneThousandTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        50000,
						}),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options: TwoThousandTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        25000,
						}),
					},
				},
				ActorBaseTier: tieringExtPb.Tier_TIER_FI_BASIC,
			},
			wantErr: false,
		},
		{
			name: "success, tier 100, not in grace, in cool off",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(false, nil, nil)
					m.mockMovementManager.EXPECT().IsEligibleForTierUpgrade(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil).Times(2)
					m.mockTimelineManager.EXPECT().GetUpgradeDetails(gomock.Any(), gomock.Any()).
						Return(curTimestamp, nil, nil).Times(2)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status:      rpcPb.StatusOk(),
				CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
				MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
					{
						TierName:          tieringExtPb.Tier_TIER_FI_REGULAR,
						IsMovementAllowed: false,
						MovementTimestamp: nil,
						Options:           FiveTierOptions(),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_BASIC,
						IsMovementAllowed: false,
						MovementTimestamp: nil,
						Options:           TenTierOptions(),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
						IsMovementAllowed: true,
						MovementTimestamp: nil,
						Options: OneHundredTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        10000,
						}),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
						IsMovementAllowed: false,
						MovementTimestamp: curTimestamp,
						Options: OneThousandTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        50000,
						}),
					},
					{
						TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
						IsMovementAllowed: false,
						MovementTimestamp: curTimestamp,
						Options: TwoThousandTierOptions(&gmoney.Money{
							CurrencyCode: "INR",
							Units:        25000,
						}),
					},
				},
				LastUpgradeDetails:   nil,
				LastDowngradeDetails: nil,
				ActorBaseTier:        tieringExtPb.Tier_TIER_FI_BASIC,
			},
			wantErr: false,
		},
		{
			name: "failure, error in tier options map",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error gathering data for pitch v2"),
			},
			wantErr: false,
		},
		{
			name: "failure, error in getting same tier details ",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(false, nil, errors.New("some random error"))
					m.mockMovementManager.EXPECT().IsEligibleForTierUpgrade(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(true, nil).Times(3)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting tier movement details"),
			},
			wantErr: false,
		},
		{
			name: "failure, error in getting higher tier details",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(false, nil, nil)
					m.mockMovementManager.EXPECT().IsEligibleForTierUpgrade(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, errors.New("some random error")).Times(3)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting tier movement details"),
			},
			wantErr: false,
		},
		{
			name: "failure, error in getting upgrade details",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, epifierrors.ErrRecordNotFound).Times(2)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(false, nil, nil)
					m.mockMovementManager.EXPECT().IsEligibleForTierUpgrade(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil).Times(2)
					m.mockTimelineManager.EXPECT().GetUpgradeDetails(gomock.Any(), gomock.Any()).
						Return(nil, nil, errors.New("some random error")).Times(2)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting tier movement details"),
			},
			wantErr: false,
		},
		{
			name: "failure, error other than record not found from TMH dao",
			fields: fields{
				tierMovementHistoryDao: mockTmhDao,
				actorTierInfoDao:       mockAtiDao,
				criteriaManager:        mockCriteriaManager,
				tierTimelineManager:    mockTimelineManager,
				movementManager:        mockMovementManager,
				tierOptionsManager:     mockOptionsManager,
				dataProcessor:          mockDataProcessor,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetTieringPitchV2Request{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
					m.mockDataProcessor.EXPECT().GetBaseTierForActor(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_TEN, nil)
					m.mockOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).
						Return(tierOptionsMap, nil)
					m.mockTmhDao.EXPECT().GetLatestByActorIdAndMovementType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, errors.New("some random error")).Times(2)
					m.mockEtmDao.EXPECT().GetLatestByActorIdAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					m.mockEtmDao.EXPECT().GetLatestByActorIdMovementTypeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).Times(2)
				},
			},
			want: &tieringPb.GetTieringPitchV2Response{
				Status: rpcPb.StatusInternalWithDebugMsg("error gathering data for pitch v2"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				releaseManager:          tt.fields.releaseManager,
				actorTierInfoDao:        tt.fields.actorTierInfoDao,
				tierOptionsManager:      tt.fields.tierOptionsManager,
				tierMovementHistoryDao:  tt.fields.tierMovementHistoryDao,
				tierTimelineManager:     tt.fields.tierTimelineManager,
				movementManager:         tt.fields.movementManager,
				dataProcessor:           tt.fields.dataProcessor,
				eligibleTierMovementDao: mockEtmDao,
			}
			m := &mockStruct{
				mockTmhDao:          mockTmhDao,
				mockAtiDao:          mockAtiDao,
				mockCriteriaManager: mockCriteriaManager,
				mockTimelineManager: mockTimelineManager,
				mockOptionsManager:  mockOptionsManager,
				mockMovementManager: mockMovementManager,
				mockDataProcessor:   mockDataProcessor,
				mockEtmDao:          mockEtmDao,
			}
			tt.args.mocks(m)
			got, err := s.GetTieringPitchV2(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTieringPitchV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				fmt.Printf("diff: %v\n", cmp.Diff(got, tt.want, protocmp.Transform()))
				t.Errorf("GetTieringPitchV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_EvaluateTierForActor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCriteriaManager := mocks.NewMockTierCriteria(ctrl)
	mockEvaluator := mocks.NewMockTierEvaluator(ctrl)
	mockAtiDao := mockDao.NewMockActorTierInfoDao(ctrl)

	type mockStruct struct {
		mockCriteriaManager *mocks.MockTierCriteria
		mockEvaluator       *mocks.MockTierEvaluator
		mockAtiDao          *mockDao.MockActorTierInfoDao
	}

	type fields struct {
		criteriaManager  criteria.TierCriteria
		tierEvaluator    evaluator.TierEvaluator
		actorTierInfoDao dao.ActorTierInfoDao
	}
	type args struct {
		ctx   context.Context
		req   *tieringPb.EvaluateTierForActorRequest
		mocks func(m *mockStruct)
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringPb.EvaluateTierForActorResponse
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				criteriaManager:  mockCriteriaManager,
				tierEvaluator:    mockEvaluator,
				actorTierInfoDao: mockAtiDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.EvaluateTierForActorRequest{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockCriteriaManager.EXPECT().GetActiveCriteriaForActor(gomock.Any(), "actor-1").
						Return(&tieringPb.TierCriteria{
							Id: "criteria-1",
						}, nil)
					m.mockEvaluator.EXPECT().Evaluate(gomock.Any(), "actor-1", gomock.Any(), &tieringPb.TierCriteria{
						Id: "criteria-1",
					}).
						Return(&tieringPb.EvaluatorMeta{EvaluatedTier: tieringEnumPb.Tier_TIER_ONE_THOUSAND}, nil)
					m.mockAtiDao.EXPECT().Get(gomock.Any(), "actor-1", tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TIER).
						Return(&tieringPb.ActorTierInfo{
							ActorId: "actor-1",
							Tier:    tieringEnumPb.Tier_TIER_TEN,
						}, nil)
				},
			},
			want: &tieringPb.EvaluateTierForActorResponse{
				Status:              rpcPb.StatusOk(),
				EvaluatedTier:       tieringExtPb.Tier_TIER_FI_INFINITE,
				CriteriaReferenceId: "criteria-1",
			},
			wantErr: false,
		},
		{
			name: "failure, error in getting active criteria",
			fields: fields{
				criteriaManager: mockCriteriaManager,
				tierEvaluator:   mockEvaluator,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.EvaluateTierForActorRequest{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockCriteriaManager.EXPECT().GetActiveCriteriaForActor(gomock.Any(), "actor-1").
						Return(nil, errors.New("some random error"))
				},
			},
			want: &tieringPb.EvaluateTierForActorResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting active criteria for actor"),
			},
			wantErr: false,
		},
		{
			name: "failure, error in evaluator",
			fields: fields{
				criteriaManager:  mockCriteriaManager,
				tierEvaluator:    mockEvaluator,
				actorTierInfoDao: mockAtiDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPb.EvaluateTierForActorRequest{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockCriteriaManager.EXPECT().GetActiveCriteriaForActor(gomock.Any(), "actor-1").
						Return(&tieringPb.TierCriteria{
							Id: "criteria-1",
						}, nil)
					m.mockEvaluator.EXPECT().Evaluate(gomock.Any(), "actor-1", gomock.Any(), &tieringPb.TierCriteria{
						Id: "criteria-1",
					}).
						Return(&tieringPb.EvaluatorMeta{EvaluatedTier: tieringEnumPb.Tier_TIER_UNSPECIFIED}, errors.New("some random error"))
					m.mockAtiDao.EXPECT().Get(gomock.Any(), "actor-1", tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TIER).
						Return(&tieringPb.ActorTierInfo{
							ActorId: "actor-1",
							Tier:    tieringEnumPb.Tier_TIER_TEN,
						}, nil)
				},
			},
			want: &tieringPb.EvaluateTierForActorResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error evaluating tier for the actor"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				criteriaManager:  tt.fields.criteriaManager,
				tierEvaluator:    tt.fields.tierEvaluator,
				actorTierInfoDao: tt.fields.actorTierInfoDao,
			}
			m := &mockStruct{
				mockCriteriaManager: mockCriteriaManager,
				mockEvaluator:       mockEvaluator,
				mockAtiDao:          mockAtiDao,
			}
			tt.args.mocks(m)
			got, err := s.EvaluateTierForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("EvaluateTierForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EvaluateTierForActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetActorScreenInteractionDetails(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockActorScreenInteractionDao := mockDao.NewMockActorScreenInteractionDao(ctrl)

	type mockStruct struct {
		mockActorScreenInteractionDao *mockDao.MockActorScreenInteractionDao
	}

	type args struct {
		ctx   context.Context
		req   *tieringPb.GetActorScreenInteractionDetailsRequest
		mocks func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		args    args
		want    *tieringPb.GetActorScreenInteractionDetailsResponse
		wantErr bool
	}{
		{
			name: "#1 Adding actor screen interaction",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetActorScreenInteractionDetailsRequest{
					ActorId:     "actor-1",
					Screen:      tieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
					RequestType: tieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_ADD,
				},
				mocks: func(m *mockStruct) {
					m.mockActorScreenInteractionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(actorScreenInteraction(), nil)
				},
			},
			want: &tieringPb.GetActorScreenInteractionDetailsResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "#2 Success : Get actor screen interaction",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetActorScreenInteractionDetailsRequest{
					ActorId:     "actor-1",
					Screen:      tieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
					RequestType: tieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_GET,
				},
				mocks: func(m *mockStruct) {
					m.mockActorScreenInteractionDao.EXPECT().GetByActorIdAndScreen(gomock.Any(), gomock.Any(), gomock.Any()).Return(actorScreenInteraction(), nil)
				},
			},
			want: &tieringPb.GetActorScreenInteractionDetailsResponse{
				Status:   rpcPb.StatusOk(),
				Response: tieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_VISITED,
			},
			wantErr: false,
		},
		{
			name: "#3 Failure : Get actor screen interaction (Not Found)",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetActorScreenInteractionDetailsRequest{
					ActorId:     "actor-1",
					Screen:      tieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
					RequestType: tieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_GET,
				},
				mocks: func(m *mockStruct) {
					m.mockActorScreenInteractionDao.EXPECT().GetByActorIdAndScreen(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &tieringPb.GetActorScreenInteractionDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "#4 Adding existing actor screen interaction",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.GetActorScreenInteractionDetailsRequest{
					ActorId:     "actor-1",
					Screen:      tieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
					RequestType: tieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_ADD,
				},
				mocks: func(m *mockStruct) {
					m.mockActorScreenInteractionDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrDuplicateEntry)
				},
			},
			want: &tieringPb.GetActorScreenInteractionDetailsResponse{
				Status: rpcPb.StatusAlreadyExists(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				actorScreenInteractionDao: mockActorScreenInteractionDao,
			}
			m := &mockStruct{
				mockActorScreenInteractionDao: mockActorScreenInteractionDao,
			}
			tt.args.mocks(m)
			got, err := s.GetActorScreenInteractionDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActorScreenInteractionDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActorScreenInteractionDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_CheckIfActorIsEligibleForCashbackReward(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type mockStruct struct {
		mockTmhDao          *mockDao.MockTierMovementHistoryDao
		mockEtmDao          *mockDao.MockEligibleTierMovementDao
		mockAtiDao          *mockDao.MockActorTierInfoDao
		mockCriteriaManager *mocks.MockTierCriteria
		mockTimelineManager *mocks.MockTierTimeline
		mockOptionsManager  *mock_tier_options.MockManager
		mockDataProcessor   *mocks.MockTieringDataProcessor
		mockEventBroker     *mock_events.MockBroker
		mockSegmentClient   *mocks2.MockSegmentationServiceClient
		clientMock          redismock.ClientMock
		wg                  *sync.WaitGroup
	}

	type args struct {
		ctx   context.Context
		req   *tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest
		mocks func(m *mockStruct)
	}

	actorId := "actor-123"
	tier := tieringExtPb.Tier_TIER_FI_INFINITE
	rewardMonthDate := timestampPb.New(time.Date(2025, 6, 1, 0, 0, 0, 0, time.UTC))

	tests := []struct {
		name         string
		args         args
		wantEligible bool
		wantErr      bool
		wantStatus   *rpcPb.Status
	}{
		{
			name: "tier not present in distinctTiers should return isEligible false",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_PLUS}, nil)
				},
			},
			wantEligible: false,
			wantErr:      false,
			wantStatus:   rpcPb.StatusOkWithDebugMsg("actor was not part of the input tier for the month"),
		},
		{
			name: "segmentId not found in config, should return eligible true",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{tier}, nil)
					m.clientMock.ExpectExists(helper.GetAbuserCheckBypassKey(actorId, tier)).SetVal(1)
					// No segmentId found, so no segmentClient call
				},
			},
			wantEligible: true,
			wantErr:      false,
			wantStatus:   rpcPb.StatusOk(),
		},
		{
			name: "segment instance not found should return status internal",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{tier}, nil)
					m.clientMock.ExpectExists(helper.GetAbuserCheckBypassKey(actorId, tier)).SetVal(0)
					m.mockSegmentClient.EXPECT().IsMember(
						gomock.Any(),
						&segment.IsMemberRequest{
							ActorId:    actorId,
							SegmentIds: []string{"AWS_test-segment"},
						},
					).Return(&segment.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segment.SegmentMembership{
							"AWS_test-segment": {
								SegmentStatus: segment.SegmentStatus_SEGMENT_INSTANCE_NOT_FOUND,
								IsActorMember: false,
							},
						},
					}, nil)
				},
			},
			wantEligible: false,
			wantErr:      false,
			wantStatus:   rpcPb.StatusInternalWithDebugMsg("unexpected segment status"),
		},
		{
			name: "segment found, actor not member, should return isEligible true, no event emitted",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{tier}, nil)
					m.clientMock.ExpectExists(helper.GetAbuserCheckBypassKey(actorId, tier)).SetVal(0)
					m.mockSegmentClient.EXPECT().IsMember(
						gomock.Any(),
						&segment.IsMemberRequest{
							ActorId:    actorId,
							SegmentIds: []string{"AWS_test-segment"},
						},
					).Return(&segment.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segment.SegmentMembership{
							"AWS_test-segment": {
								SegmentStatus: segment.SegmentStatus_SEGMENT_INSTANCE_FOUND,
								IsActorMember: false,
							},
						},
					}, nil)
					// No event emitted
				},
			},
			wantEligible: true,
			wantErr:      false,
			wantStatus:   rpcPb.StatusOk(),
		},
		{
			name: "segment found, actor is member, should emit event and return isEligible false",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{tier}, nil)
					m.clientMock.ExpectExists(helper.GetAbuserCheckBypassKey(actorId, tier)).SetVal(0)
					m.mockSegmentClient.EXPECT().IsMember(
						gomock.Any(),
						&segment.IsMemberRequest{
							ActorId:    actorId,
							SegmentIds: []string{"AWS_test-segment"},
						},
					).Return(&segment.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segment.SegmentMembership{
							"AWS_test-segment": {
								SegmentStatus: segment.SegmentStatus_SEGMENT_INSTANCE_FOUND,
								IsActorMember: true,
							},
						},
					}, nil)
					m.wg.Add(1)
					m.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).DoAndReturn(
						func(arg0, arg1 interface{}) {
							defer m.wg.Done()
						},
					)
				},
			},
			wantEligible: false,
			wantErr:      false,
			wantStatus:   rpcPb.StatusOk(),
		},
		{
			name: "bypass check (redis Exists) returns error, should proceed gracefully",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{tier}, nil)
					m.clientMock.ExpectExists(helper.GetAbuserCheckBypassKey(actorId, tier)).SetErr(errors.New("redis error"))
					m.mockSegmentClient.EXPECT().IsMember(
						gomock.Any(),
						&segment.IsMemberRequest{
							ActorId:    actorId,
							SegmentIds: []string{"AWS_test-segment"},
						},
					).Return(&segment.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segment.SegmentMembership{
							"AWS_test-segment": {
								SegmentStatus: segment.SegmentStatus_SEGMENT_INSTANCE_FOUND,
								IsActorMember: false,
							},
						},
					}, nil)
				},
			},
			wantEligible: true,
			wantErr:      false,
			wantStatus:   rpcPb.StatusOk(),
		},
		{
			name: "dataProcessor.GetActorDistinctTier returns error, should return internal error status",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: rewardMonthDate,
				},
				mocks: func(m *mockStruct) {
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(2025, 6, 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(2025, 7, 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return(nil, errors.New("processor error"))
				},
			},
			wantEligible: false,
			wantErr:      false,
			wantStatus:   rpcPb.StatusInternalWithDebugMsg("error in getting distinct tiers for actor"),
		},
		{
			name: "empty reward month should use last month's range ",
			args: args{
				ctx: context.Background(),
				req: &tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest{
					ActorId:     actorId,
					Tier:        tier,
					RewardMonth: nil,
				},
				mocks: func(m *mockStruct) {
					// Calculate last month's start and end in IST
					lastMonth := time.Now().In(pkgDateTime.IST).AddDate(0, -1, 0)
					m.mockDataProcessor.EXPECT().GetActorDistinctTier(
						gomock.Any(),
						actorId,
						time.Date(lastMonth.Year(), lastMonth.Month(), 1, 0, 0, 0, 0, pkgDateTime.IST),
						time.Date(time.Now().Year(), time.Now().Month(), 1, 0, 0, 0, 0, pkgDateTime.IST).Add(-1*time.Duration(1)),
					).Return([]tieringExtPb.Tier{}, nil)
				},
			},
			wantEligible: false,
			wantErr:      false,
			wantStatus:   rpcPb.StatusOkWithDebugMsg("actor was not part of the input tier for the month"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockTmhDao := mockDao.NewMockTierMovementHistoryDao(ctrl)
			mockEtmDao := mockDao.NewMockEligibleTierMovementDao(ctrl)
			mockAtiDao := mockDao.NewMockActorTierInfoDao(ctrl)
			mockCriteriaManager := mocks.NewMockTierCriteria(ctrl)
			mockTimelineManager := mocks.NewMockTierTimeline(ctrl)
			mockOptionsManager := mock_tier_options.NewMockManager(ctrl)
			mockDataProcessor := mocks.NewMockTieringDataProcessor(ctrl)
			mockEventBroker := mock_events.NewMockBroker(ctrl)
			client, clientMock := redismock.NewClientMock()
			mockSegmentClient := mocks2.NewMockSegmentationServiceClient(ctrl)
			var wg sync.WaitGroup

			m := &mockStruct{
				mockTmhDao:          mockTmhDao,
				mockEtmDao:          mockEtmDao,
				mockAtiDao:          mockAtiDao,
				mockCriteriaManager: mockCriteriaManager,
				mockTimelineManager: mockTimelineManager,
				mockOptionsManager:  mockOptionsManager,
				mockDataProcessor:   mockDataProcessor,
				mockEventBroker:     mockEventBroker,
				clientMock:          clientMock,
				mockSegmentClient:   mockSegmentClient,
				wg:                  &wg,
			}

			tt.args.mocks(m)

			service := &Service{
				gconf:                   gconf,
				tierMovementHistoryDao:  mockTmhDao,
				eligibleTierMovementDao: mockEtmDao,
				actorTierInfoDao:        mockAtiDao,
				criteriaManager:         mockCriteriaManager,
				tierTimelineManager:     mockTimelineManager,
				tierOptionsManager:      mockOptionsManager,
				dataProcessor:           mockDataProcessor,
				eventBroker:             mockEventBroker,
				redisClient:             client,
				segmentationClient:      mockSegmentClient,
			}
			resp, err := service.CheckIfActorIsEligibleForCashbackReward(tt.args.ctx, tt.args.req)
			m.wg.Wait()

			if (err != nil) != tt.wantErr {
				t.Errorf("CheckIfActorIsEligibleForCashbackReward() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if resp.GetIsEligible() != tt.wantEligible {
				t.Errorf("expected eligibility: %v, got: %v", tt.wantEligible, resp.GetIsEligible())
			}
			if tt.wantStatus != nil && !reflect.DeepEqual(resp.GetStatus(), tt.wantStatus) {
				t.Errorf("expected status: %v, got: %v", tt.wantStatus, resp.GetStatus())
			}
		})
	}
}
