// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	tiering "github.com/epifi/gamma/api/tiering"
	enums "github.com/epifi/gamma/api/tiering/enums"
	external "github.com/epifi/gamma/api/tiering/external"
	gomock "github.com/golang/mock/gomock"
)

// MockTieringDataProcessor is a mock of TieringDataProcessor interface.
type MockTieringDataProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockTieringDataProcessorMockRecorder
}

// MockTieringDataProcessorMockRecorder is the mock recorder for MockTieringDataProcessor.
type MockTieringDataProcessorMockRecorder struct {
	mock *MockTieringDataProcessor
}

// NewMockTieringDataProcessor creates a new mock instance.
func NewMockTieringDataProcessor(ctrl *gomock.Controller) *MockTieringDataProcessor {
	mock := &MockTieringDataProcessor{ctrl: ctrl}
	mock.recorder = &MockTieringDataProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTieringDataProcessor) EXPECT() *MockTieringDataProcessorMockRecorder {
	return m.recorder
}

// EnrichCtxForFeatureEvaluation mocks base method.
func (m *MockTieringDataProcessor) EnrichCtxForFeatureEvaluation(ctx context.Context, actorId string) (context.Context, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnrichCtxForFeatureEvaluation", ctx, actorId)
	ret0, _ := ret[0].(context.Context)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnrichCtxForFeatureEvaluation indicates an expected call of EnrichCtxForFeatureEvaluation.
func (mr *MockTieringDataProcessorMockRecorder) EnrichCtxForFeatureEvaluation(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnrichCtxForFeatureEvaluation", reflect.TypeOf((*MockTieringDataProcessor)(nil).EnrichCtxForFeatureEvaluation), ctx, actorId)
}

// GetActorDistinctTier mocks base method.
func (m *MockTieringDataProcessor) GetActorDistinctTier(ctx context.Context, actorId string, timeSince, timeTill time.Time) ([]external.Tier, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorDistinctTier", ctx, actorId, timeSince, timeTill)
	ret0, _ := ret[0].([]external.Tier)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorDistinctTier indicates an expected call of GetActorDistinctTier.
func (mr *MockTieringDataProcessorMockRecorder) GetActorDistinctTier(ctx, actorId, timeSince, timeTill interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorDistinctTier", reflect.TypeOf((*MockTieringDataProcessor)(nil).GetActorDistinctTier), ctx, actorId, timeSince, timeTill)
}

// GetBaseTierForActor mocks base method.
func (m *MockTieringDataProcessor) GetBaseTierForActor(ctx context.Context, actorId string) (enums.Tier, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBaseTierForActor", ctx, actorId)
	ret0, _ := ret[0].(enums.Tier)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBaseTierForActor indicates an expected call of GetBaseTierForActor.
func (mr *MockTieringDataProcessorMockRecorder) GetBaseTierForActor(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBaseTierForActor", reflect.TypeOf((*MockTieringDataProcessor)(nil).GetBaseTierForActor), ctx, actorId)
}

// GetCurrentTierDefaultToBaseTier mocks base method.
func (m *MockTieringDataProcessor) GetCurrentTierDefaultToBaseTier(ctx context.Context, actorId string) (enums.Tier, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentTierDefaultToBaseTier", ctx, actorId)
	ret0, _ := ret[0].(enums.Tier)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentTierDefaultToBaseTier indicates an expected call of GetCurrentTierDefaultToBaseTier.
func (mr *MockTieringDataProcessorMockRecorder) GetCurrentTierDefaultToBaseTier(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentTierDefaultToBaseTier", reflect.TypeOf((*MockTieringDataProcessor)(nil).GetCurrentTierDefaultToBaseTier), ctx, actorId)
}

// GetTierTimeRangesForActor mocks base method.
func (m *MockTieringDataProcessor) GetTierTimeRangesForActor(ctx context.Context, actorId string, tiers []external.Tier, timeSince time.Time) (map[external.Tier][]*tiering.TimeRange, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTierTimeRangesForActor", ctx, actorId, tiers, timeSince)
	ret0, _ := ret[0].(map[external.Tier][]*tiering.TimeRange)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTierTimeRangesForActor indicates an expected call of GetTierTimeRangesForActor.
func (mr *MockTieringDataProcessorMockRecorder) GetTierTimeRangesForActor(ctx, actorId, tiers, timeSince interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierTimeRangesForActor", reflect.TypeOf((*MockTieringDataProcessor)(nil).GetTierTimeRangesForActor), ctx, actorId, tiers, timeSince)
}

// IsMultipleWaysToEnterTierEnabled mocks base method.
func (m *MockTieringDataProcessor) IsMultipleWaysToEnterTierEnabled(ctx context.Context, actorId string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMultipleWaysToEnterTierEnabled", ctx, actorId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsMultipleWaysToEnterTierEnabled indicates an expected call of IsMultipleWaysToEnterTierEnabled.
func (mr *MockTieringDataProcessorMockRecorder) IsMultipleWaysToEnterTierEnabled(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMultipleWaysToEnterTierEnabled", reflect.TypeOf((*MockTieringDataProcessor)(nil).IsMultipleWaysToEnterTierEnabled), ctx, actorId)
}
