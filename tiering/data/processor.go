package data

import (
	"context"
	"time"

	beTieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/dao"
)

//go:generate mockgen -source=processor.go -destination=../test/mocks/mock_data.go -package=mocks
type TieringDataProcessor interface {
	GetTierTimeRangesForActor(ctx context.Context, actorId string, tiers []tieringExternalPb.Tier, timeSince time.Time) (map[tieringExternalPb.Tier][]*beTieringPb.TimeRange, error)
	GetCurrentTierDefaultToBaseTier(ctx context.Context, actorId string) (tieringEnumPb.Tier, error)
	GetBaseTierForActor(ctx context.Context, actorId string) (tieringEnumPb.Tier, error)
	GetActorDistinctTier(ctx context.Context, actorId string, timeSince time.Time, timeTill time.Time) ([]tieringExternalPb.Tier, error)
	EnrichCtxForFeatureEvaluation(ctx context.Context, actorId string) (context.Context, error)
	IsMultipleWaysToEnterTierEnabled(ctx context.Context, actorId string) (bool, error)
}

type TieringDataProcessorService struct {
	gconf            *genconf.Config
	atiDao           dao.ActorTierInfoDao
	tmhDao           dao.TierMovementHistoryDao
	userClient       userPb.UsersClient
	releaseEvaluator release.IEvaluator
}

func NewTieringDataProcessorService(gconf *genconf.Config, atiDao dao.ActorTierInfoDao, tmhDao dao.TierMovementHistoryDao, userClient userPb.UsersClient, releaseEvaluator release.IEvaluator) *TieringDataProcessorService {
	return &TieringDataProcessorService{
		gconf:            gconf,
		atiDao:           atiDao,
		tmhDao:           tmhDao,
		userClient:       userClient,
		releaseEvaluator: releaseEvaluator,
	}
}
