package data

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	mock_dao "github.com/epifi/gamma/tiering/dao/mocks"
)

func TestTieringDataProcessorService_GetActorDistinctTier(t1 *testing.T) {
	timeSince1 := time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)
	currentTime := time.Now()
	type args struct {
		ctx       context.Context
		actorId   string
		timeSince time.Time
	}
	type mockStruct struct {
		mockTmhDao *mock_dao.MockTierMovementHistoryDao
		mockAtiDao *mock_dao.MockActorTierInfoDao
	}
	tests := []struct {
		name    string
		args    args
		want    []tieringExternalPb.Tier
		mocks   func(m *mockStruct)
		wantErr bool
	}{
		{
			name: "have distinct tiers in tier movement history after timeSince",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				timeSince: timeSince1,
			},
			want: []tieringExternalPb.Tier{tieringExternalPb.Tier_TIER_FI_INFINITE},
			mocks: func(m *mockStruct) {
				m.mockTmhDao.EXPECT().GetDistinctTiersForActor(gomock.Any(), gomock.Any(), timeSince1, currentTime).Return([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_ONE_THOUSAND}, nil)
			},
			wantErr: false,
		},
		{
			name: "no distinct tiers from tier movement history after timeSince, use current tier as the distinct tier",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				timeSince: timeSince1,
			},
			want: []tieringExternalPb.Tier{tieringExternalPb.Tier_TIER_FI_INFINITE},
			mocks: func(m *mockStruct) {
				m.mockTmhDao.EXPECT().GetDistinctTiersForActor(gomock.Any(), gomock.Any(), timeSince1, currentTime).Return([]tieringEnumPb.Tier{}, nil)
				m.mockAtiDao.EXPECT().Get(gomock.Any(), "actor-1", tiering.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TIER).Return(&tiering.ActorTierInfo{
					Tier: tieringEnumPb.Tier_TIER_ONE_THOUSAND,
				}, nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			mockAtiDao := mock_dao.NewMockActorTierInfoDao(ctrl)
			mockTmhDao := mock_dao.NewMockTierMovementHistoryDao(ctrl)

			t := &TieringDataProcessorService{
				gconf:  gconf,
				atiDao: mockAtiDao,
				tmhDao: mockTmhDao,
			}

			m := &mockStruct{
				mockTmhDao: mockTmhDao,
				mockAtiDao: mockAtiDao,
			}

			tt.mocks(m)

			got, err := t.GetActorDistinctTier(tt.args.ctx, tt.args.actorId, tt.args.timeSince, currentTime)
			if (err != nil) != tt.wantErr {
				t1.Errorf("GetActorDistinctTier() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("GetActorDistinctTier() got = %v, want %v", got, tt.want)
			}
		})
	}
}
