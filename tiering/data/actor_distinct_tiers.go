package data

import (
	"context"
	"fmt"
	"time"

	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/tiering/tiermappings"
)

func (t *TieringDataProcessorService) GetActorDistinctTier(ctx context.Context, actorId string, timeSince time.Time, timeTill time.Time) ([]tieringExternalPb.Tier, error) {
	// get all distinct tiers for actor
	tierList, err := t.tmhDao.GetDistinctTiersForActor(ctx, actorId, timeSince, timeTill)
	if err != nil {
		return nil, err
	}

	if len(tierList) == 0 {
		currTier, getCurrTierErr := t.GetCurrentTierDefaultToBaseTier(ctx, actorId)
		if getCurrTierErr != nil {
			return nil, fmt.Errorf("failed to get actor tier info for actorId: %s, error: %v", actorId, getCurrTierErr)
		}

		tierList = append(tierList, currTier)
	}

	// convert internal tier to external tier
	extTierList, err := tiermappings.GetExternalTierListFromInternalTierList(tierList)
	if err != nil {
		return nil, err
	}

	return extTierList, nil
}
