package dao

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/config"
	"github.com/epifi/gamma/tiering/config/genconf"
)

type TierMovementHistoryTestSuite struct {
	pgdb                   *gormv2.DB
	tierMovementHistoryDao TierMovementHistoryDao
	conf                   *config.Config
	gconf                  *genconf.Config
	pgdbName               string
}

var (
	tierMovementHistoryTestSuite TierMovementHistoryTestSuite
	tierMovementHistory1         = &tieringPb.TierMovementHistory{
		Id:                             "57f83510-407f-4a9a-ada3-998a032a54df",
		EligibilityMovementReferenceId: "57f83510-407f-4a9a-ada3-998a032a54de",
		CriteriaReferenceId:            "57f83510-407f-4a9a-ada3-998a032a54dd",
		FromTier:                       tieringEnumPb.Tier_TIER_TEN,
		ToTier:                         tieringEnumPb.Tier_TIER_ONE_HUNDRED,
		ActorId:                        "actor-1",
		MovementType:                   tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
		Provenance:                     tieringEnumPb.Provenance_PROVENANCE_AUTOMATIC,
		Reason:                         &tieringPb.TierMovementReason{},
	}
	tierMovementHistory2 = &tieringPb.TierMovementHistory{
		Id:                             "67f83510-407f-4a9a-ada3-998a032a54df",
		EligibilityMovementReferenceId: "57f83510-407f-4a9a-ada3-998a032a54de",
		CriteriaReferenceId:            "57f83510-407f-4a9a-ada3-998a032a54dd",
		FromTier:                       tieringEnumPb.Tier_TIER_TEN,
		ToTier:                         tieringEnumPb.Tier_TIER_ONE_HUNDRED,
		ActorId:                        "actor-1",
		MovementType:                   tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
		Provenance:                     tieringEnumPb.Provenance_PROVENANCE_AUTOMATIC,
		Reason:                         &tieringPb.TierMovementReason{},
	}
	tierMovementHistory3 = &tieringPb.TierMovementHistory{
		Id:         "57f83510-407f-4a9a-ada3-998a032a54df",
		Provenance: tieringEnumPb.Provenance_PROVENANCE_INSTANT_UPGRADE,
	}
	tierMovementHistory4 = &tieringPb.TierMovementHistory{
		Id:                             "57f83510-407f-4a9a-ada3-998a032a56df",
		EligibilityMovementReferenceId: "57f83510-407f-4a9a-ada3-998a032a54de",
		CriteriaReferenceId:            "57f83510-407f-4a9a-ada3-998a032a54dd",
		FromTier:                       tieringEnumPb.Tier_TIER_TEN,
		ToTier:                         tieringEnumPb.Tier_TIER_ONE_HUNDRED,
		ActorId:                        "actor-1",
		MovementType:                   tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
		Provenance:                     tieringEnumPb.Provenance_PROVENANCE_AUTOMATIC,
		Reason:                         &tieringPb.TierMovementReason{},
	}
	tierMovementHistory5 = &tieringPb.TierMovementHistory{
		Id:                             "51d8052a-137d-481c-926a-94f3fadec30d",
		EligibilityMovementReferenceId: "57f83510-407f-4a9a-ada3-998a032a54de",
		CriteriaReferenceId:            "57f83510-407f-4a9a-ada3-998a032a54dd",
		FromTier:                       tieringEnumPb.Tier_TIER_ONE_HUNDRED,
		ToTier:                         tieringEnumPb.Tier_TIER_ONE_THOUSAND,
		ActorId:                        "actor-1",
		MovementType:                   tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
		Provenance:                     tieringEnumPb.Provenance_PROVENANCE_AUTOMATIC,
		Reason:                         &tieringPb.TierMovementReason{},
	}
)

func assertTierMovementHistory(t *testing.T, got, want *tieringPb.TierMovementHistory) {
	opts := []cmp.Option{
		protocmp.Transform(),
		protocmp.IgnoreFields(&tieringPb.TierMovementHistory{}, "created_at", "updated_at"),
	}
	if diff := cmp.Diff(got, want, opts...); diff != "" {
		t.Errorf("diff: %s", diff)
		return
	}
}

func assertTierMovementHistories(t *testing.T, got, want []*tieringPb.TierMovementHistory) {
	if len(got) != len(want) {
		t.Errorf("assertTierMovementHistories: len(got) (%d) != len(want) (%d)", len(got), len(want))
		return
	}
	sort.Slice(got, func(i, j int) bool {
		return got[i].GetId() < got[j].GetId()
	})
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetId() < want[j].GetId()
	})
	for i := 0; i < len(got); i++ {
		opts := []cmp.Option{
			protocmp.Transform(),
			protocmp.IgnoreFields(&tieringPb.TierMovementHistory{}, "created_at", "updated_at"),
		}
		if diff := cmp.Diff(got[i], want[i], opts...); diff != "" {
			t.Errorf("diff: %s", diff)
			return
		}
	}
}

func TestTierMovementHistoryDaoCrdb_Create(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx                 context.Context
		tierMovementHistory *tieringPb.TierMovementHistory
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		want    *tieringPb.TierMovementHistory
	}{
		{
			name:   "successfully create record",
			fields: fields{},
			args: args{
				ctx:                 context.Background(),
				tierMovementHistory: tierMovementHistory2,
			},
			wantErr: false,
			want:    tierMovementHistory2,
		},
		{
			name:   "error, duplicate key",
			fields: fields{},
			args: args{
				ctx:                 context.Background(),
				tierMovementHistory: tierMovementHistory2,
			},
			wantErr: true,
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.Create(tt.args.ctx, tt.args.tierMovementHistory)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistory(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_Get(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       *tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				id:  "57f83510-407f-4a9a-ada3-998a032a54df",
			},
			wantErr: false,
			want:    tierMovementHistory1,
		},
		{
			name:   "error, id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name:   "error, record not found",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				id:  "67f83510-407f-4a9a-ada3-998a032a54df",
			},
			wantErr: true,
		},
		{
			name:   "success, use field masks",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				id:  "57f83510-407f-4a9a-ada3-998a032a54df",
			},
			wantErr: false,
			want:    tierMovementHistory1,
			fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
			},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.Get(tt.args.ctx, tt.args.id, tt.fieldMasks...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistory(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_Update(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx                 context.Context
		tierMovementHistory *tieringPb.TierMovementHistory
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "successfully update record",
			fields: fields{},
			args: args{
				ctx:                 context.Background(),
				tierMovementHistory: tierMovementHistory3,
			},
			wantErr: false,
		},
		{
			name:   "error, id not provided",
			fields: fields{},
			args: args{
				ctx:                 context.Background(),
				tierMovementHistory: &tieringPb.TierMovementHistory{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			err := c.Update(tt.args.ctx, tt.args.tierMovementHistory, []tieringPb.TierMovementHistoryFieldMask{tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE})
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetByEligibilityMovementRefId(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx                            context.Context
		eligibilityMovementReferenceId string
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       *tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:                            context.Background(),
				eligibilityMovementReferenceId: "57f83510-407f-4a9a-ada3-998a032a54de",
			},
			wantErr: false,
			want:    tierMovementHistory4,
		},
		{
			name:   "error, eligibility reference id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name:   "error, record not found",
			fields: fields{},
			args: args{
				ctx:                            context.Background(),
				eligibilityMovementReferenceId: "67f83510-407f-4a9a-ada3-998a032a54de",
			},
			wantErr: true,
		},
		{
			name:   "success, use field masks",
			fields: fields{},
			args: args{
				ctx:                            context.Background(),
				eligibilityMovementReferenceId: "57f83510-407f-4a9a-ada3-998a032a54de",
			},
			wantErr: false,
			want:    tierMovementHistory4,
			fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
			},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetByEligibilityMovementRefId(tt.args.ctx, tt.args.eligibilityMovementReferenceId, tt.fieldMasks...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByEligibilityMovementRefId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistory(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetLatestByActorIdAndMovementType(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx          context.Context
		actorId      string
		movementType tieringEnumPb.TierMovementType
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       *tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				movementType: tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
			},
			wantErr: false,
			want:    tierMovementHistory1,
		},
		{
			name:   "error, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name:   "error, record not found",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
			},
			wantErr: true,
		},
		{
			name:   "success, use field masks",
			fields: fields{},
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				movementType: tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
			},
			wantErr: false,
			want:    tierMovementHistory1,
			fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
			},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetLatestByActorIdAndMovementType(tt.args.ctx, tt.args.actorId, tt.args.movementType, tt.fieldMasks...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestByActorIdAndMovementType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistory(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetByActorId(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx     context.Context
		actorId string
		limit   int
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       []*tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				limit:   2,
			},
			wantErr: false,
			want:    []*tieringPb.TierMovementHistory{tierMovementHistory5, tierMovementHistory1},
		},
		{
			name:   "error, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetByActorId(tt.args.ctx, tt.args.actorId, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistories(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetLatestByActorIdAndTierBeforeTimestamp(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx       context.Context
		actorId   string
		tierAfter time.Time
		tiers     []tieringEnumPb.Tier
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       *tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				tierAfter: time.Now(),
				tiers: []tieringEnumPb.Tier{
					tieringEnumPb.Tier_TIER_TEN,
					tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					tieringEnumPb.Tier_TIER_TWO_THOUSAND,
				},
			},
			wantErr: false,
			want:    tierMovementHistory1,
		},
		{
			name:   "error, eligibility reference id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name:   "error, record not found",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
			},
			wantErr: true,
		},
		{
			name:   "success, use field masks",
			fields: fields{},
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				tierAfter: time.Now(),
				tiers: []tieringEnumPb.Tier{
					tieringEnumPb.Tier_TIER_TEN,
					tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					tieringEnumPb.Tier_TIER_TWO_THOUSAND,
				},
			},
			wantErr: false,
			want:    tierMovementHistory1,
			fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
			},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetLatestByActorIdAndTierBeforeTimestamp(tt.args.ctx, tt.args.actorId, tt.args.tiers, tt.args.tierAfter, tt.fieldMasks...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestByActorIdAndTierBeforeTimestamp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistory(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetMovementListByActorIdMovementType(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx          context.Context
		actorId      string
		movementType tieringEnumPb.TierMovementType
		timeAfter    time.Time
		limit        int
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       []*tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				movementType: tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
				timeAfter:    time.Date(2022, 01, 01, 0, 1, 1, 0, time.UTC),
				limit:        10,
			},
			wantErr: false,
			want:    []*tieringPb.TierMovementHistory{tierMovementHistory5, tierMovementHistory1},
		},
		{
			name:   "neutral, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
			want:    []*tieringPb.TierMovementHistory{},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetMovementListByActorIdMovementTypeAfterTimestamp(tt.args.ctx, tt.args.actorId, tt.args.movementType, tt.args.timeAfter, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMovementListByActorIdMovementTypeAfterTimestamp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistories(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetMovementListByActorIdTierFromMovementType(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx          context.Context
		actorId      string
		tierFrom     tieringEnumPb.Tier
		movementType tieringEnumPb.TierMovementType
		timeAfter    time.Time
		limit        int
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       []*tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierFrom:     tieringEnumPb.Tier_TIER_TEN,
				movementType: tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
				timeAfter:    time.Date(2022, 01, 01, 0, 1, 1, 0, time.UTC),
				limit:        10,
			},
			wantErr: false,
			want:    []*tieringPb.TierMovementHistory{tierMovementHistory1},
		},
		{
			name:   "neutral, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
			want:    []*tieringPb.TierMovementHistory{},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetMovementListByActorIdTierFromMovementType(tt.args.ctx, tt.args.actorId, tt.args.tierFrom, tt.args.movementType, tt.args.timeAfter, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMovementListByActorIdTierFromMovementType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistories(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetMovementCountByActorIdMovementType(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx          context.Context
		actorId      string
		movementType tieringEnumPb.TierMovementType
		timeAfter    time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		want    int64
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				movementType: tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
				timeAfter:    time.Date(2022, 01, 01, 0, 1, 1, 0, time.UTC),
			},
			wantErr: false,
			want:    2,
		},
		{
			name:   "neutral, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
			want:    0,
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetMovementCountByActorIdMovementType(tt.args.ctx, tt.args.actorId, tt.args.movementType, tt.args.timeAfter)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMovementCountByActorIdMovementType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.True(t, got == tt.want, fmt.Sprintf("got:%v, want:%v", got, tt.want))
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetMovementCountByActorIdTierFromMovementType(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx          context.Context
		actorId      string
		tierFrom     tieringEnumPb.Tier
		movementType tieringEnumPb.TierMovementType
		timeAfter    time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		want    int64
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierFrom:     tieringEnumPb.Tier_TIER_TEN,
				movementType: tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
				timeAfter:    time.Date(2022, 01, 01, 0, 1, 1, 0, time.UTC),
			},
			wantErr: false,
			want:    1,
		},
		{
			name:   "neutral, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
			want:    0,
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetMovementCountByActorIdTierFromMovementType(tt.args.ctx, tt.args.actorId, tt.args.tierFrom, tt.args.movementType, tt.args.timeAfter)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMovementCountByActorIdTierFromMovementType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.True(t, got == tt.want, fmt.Sprintf("got:%v, want:%v", got, tt.want))
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetLatestByActorId(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		want       *tieringPb.TierMovementHistory
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}{
		{
			name:   "successfully retrieve record",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			wantErr: false,
			want:    tierMovementHistory1,
		},
		{
			name:   "error, actor id not provided",
			fields: fields{},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name:   "error, record not found",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-2",
			},
			wantErr: true,
		},
		{
			name:   "success, use field masks",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			wantErr: false,
			want:    tierMovementHistory1,
			fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
				tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
			},
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetLatestByActorId(tt.args.ctx, tt.args.actorId, tt.fieldMasks...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assertTierMovementHistory(t, got, tt.want)
			}
		})
	}
}

func TestTierMovementHistoryImpl_GetTierMovementHistories(t *testing.T) {
	token1 := "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTYzNzY0NTk5NiwibmFub3MiOjE4NDc3NjAwMH0sIk9mZnNldCI6MiwiSXNSZXZlcnNlIjpmYWxzZX0="
	token2 := "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2OTE4MTk5NiwibmFub3MiOjE4NDc3NjAwMH0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjp0cnVlfQ=="
	token3 := "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2OTE4MTk5NiwibmFub3MiOjE4NDc3NjAwMH0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0="
	// token3 := "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTYzNzY0NTk5NiwibmFub3MiOjE4NDc3NjAwMH0sIk9mZnNldCI6MiwiSXNSZXZlcnNlIjp0cnVlfQ=="
	pageTokenFor2ndPage, err := pagination.GetPageToken(&rpc.PageContextRequest{
		Token: &rpc.PageContextRequest_AfterToken{
			AfterToken: token1,
		},
		PageSize: 2,
	})
	require.NoError(t, err)

	layout := "2006-01-02 15:04:05.999999999 -07:00"
	t1, err1 := time.Parse(layout, "2021-11-01 05:39:56.184776 +00:00")
	require.NoError(t, err1)
	t2, err2 := time.Parse(layout, "2021-11-05 05:39:56.184776 +00:00")
	require.NoError(t, err2)
	t3, err3 := time.Parse(layout, "2021-11-10 05:39:56.184776 +00:00")
	require.NoError(t, err3)
	t4, err4 := time.Parse(layout, "2021-11-15 05:39:56.184776 +00:00")
	require.NoError(t, err4)
	t5, err5 := time.Parse(layout, "2021-11-20 05:39:56.184776 +00:00")
	require.NoError(t, err5)

	type args struct {
		ctx        context.Context
		actorId    string
		pageToken  *pagination.PageToken
		pageSize   uint32
		options    []storagev2.FilterOption
		fieldMasks []tieringPb.TierMovementHistoryFieldMask
	}
	type flags struct {
		compareCreatedAt bool
	}
	tests := []struct {
		name    string
		args    args
		flags   flags
		want    []*tieringPb.TierMovementHistory
		want2   *rpc.PageContextResponse
		wantErr bool
	}{
		{
			name: "record not found",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-100",
				pageToken: nil,
				pageSize:  2,
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "get by filter, 2 responses in a page",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				pageToken: &pagination.PageToken{IsReverse: true, Timestamp: timestampPb.Now()},
				pageSize:  2,
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			want: []*tieringPb.TierMovementHistory{tierMovementHistory5, tierMovementHistory4},
			want2: &rpc.PageContextResponse{
				BeforeToken: token2,
				HasBefore:   true,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "get higher tiers",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				pageToken: nil,
				pageSize:  1,
				options:   []storagev2.FilterOption{WithToTierIn([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_ONE_THOUSAND})},
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			want: []*tieringPb.TierMovementHistory{tierMovementHistory5},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "pagination: has next page",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				pageToken: nil,
				pageSize:  2,
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			want: []*tieringPb.TierMovementHistory{tierMovementHistory4, tierMovementHistory1},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  token3,
				HasAfter:    true,
			},
			wantErr: false,
		},
		{
			name: "pagination: 2nd page",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				pageToken: pageTokenFor2ndPage,
				pageSize:  2,
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			want: []*tieringPb.TierMovementHistory{tierMovementHistory5},
			want2: &rpc.PageContextResponse{
				BeforeToken: token2,
				HasBefore:   true,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "pagination: no pages before and after",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				pageToken: nil,
				pageSize:  10,
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			want: []*tieringPb.TierMovementHistory{tierMovementHistory4, tierMovementHistory1, tierMovementHistory5},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "tmh for finding time ranges when user was in a tier - plus",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				pageToken: nil,
				pageSize:  25,
				options: []storagev2.FilterOption{
					WithFromTierInOrToTierIn([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_ONE_HUNDRED}),
					WithOrderByCreatedAt(false),
				},
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			flags: flags{
				compareCreatedAt: true,
			},
			want: []*tieringPb.TierMovementHistory{
				{
					FromTier:  tieringEnumPb.Tier_TIER_UNSPECIFIED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					CreatedAt: timestampPb.New(t1),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t2),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_TEN,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					CreatedAt: timestampPb.New(t4),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t5),
				},
			},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "tmh for finding time ranges when user was in a tier - infinite",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				pageToken: nil,
				pageSize:  25,
				options: []storagev2.FilterOption{
					WithFromTierInOrToTierIn([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_ONE_THOUSAND}),
					WithOrderByCreatedAt(false),
				},
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			flags: flags{
				compareCreatedAt: true,
			},
			want: []*tieringPb.TierMovementHistory{
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t2),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					ToTier:    tieringEnumPb.Tier_TIER_TEN,
					CreatedAt: timestampPb.New(t3),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t5),
				},
			},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "tmh for finding time ranges when user was in a tier - ten",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				pageToken: nil,
				pageSize:  25,
				options: []storagev2.FilterOption{
					WithFromTierInOrToTierIn([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_TEN}),
					WithOrderByCreatedAt(false),
				},
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			flags: flags{
				compareCreatedAt: true,
			},
			want: []*tieringPb.TierMovementHistory{
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					ToTier:    tieringEnumPb.Tier_TIER_TEN,
					CreatedAt: timestampPb.New(t3),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_TEN,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					CreatedAt: timestampPb.New(t4),
				},
			},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "tmh for finding time ranges when user was in a tier - plus, infinite",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				pageToken: nil,
				pageSize:  25,
				options: []storagev2.FilterOption{
					WithFromTierInOrToTierIn([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_ONE_HUNDRED, tieringEnumPb.Tier_TIER_ONE_THOUSAND}),
					WithOrderByCreatedAt(false),
				},
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			flags: flags{
				compareCreatedAt: true,
			},
			want: []*tieringPb.TierMovementHistory{
				{
					FromTier:  tieringEnumPb.Tier_TIER_UNSPECIFIED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					CreatedAt: timestampPb.New(t1),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t2),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					ToTier:    tieringEnumPb.Tier_TIER_TEN,
					CreatedAt: timestampPb.New(t3),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_TEN,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					CreatedAt: timestampPb.New(t4),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t5),
				},
			},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
		{
			name: "tmh for finding time ranges when user was in a tier - plus, infinite, with created at greater than",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				pageToken: nil,
				pageSize:  25,
				options: []storagev2.FilterOption{
					WithFromTierInOrToTierIn([]tieringEnumPb.Tier{tieringEnumPb.Tier_TIER_ONE_HUNDRED, tieringEnumPb.Tier_TIER_ONE_THOUSAND}),
					WithOrderByCreatedAt(false),
					WithCreatedAtGreaterThan(t3),
				},
				fieldMasks: []tieringPb.TierMovementHistoryFieldMask{
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
					tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
				},
			},
			flags: flags{
				compareCreatedAt: true,
			},
			want: []*tieringPb.TierMovementHistory{
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					ToTier:    tieringEnumPb.Tier_TIER_TEN,
					CreatedAt: timestampPb.New(t3),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_TEN,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					CreatedAt: timestampPb.New(t4),
				},
				{
					FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					ToTier:    tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					CreatedAt: timestampPb.New(t5),
				},
			},
			want2: &rpc.PageContextResponse{
				BeforeToken: "",
				HasBefore:   false,
				AfterToken:  "",
				HasAfter:    false,
			},
			wantErr: false,
		},
	}
	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &TierMovementHistoryImpl{
				pgdb:        tierMovementHistoryTestSuite.pgdb,
				maxPageSize: 25,
			}
			got, got2, err := a.GetTierMovementHistories(tt.args.ctx, tt.args.actorId, tt.args.pageToken, tt.args.pageSize, tt.args.options, tt.args.fieldMasks...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTierMovementHistories() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			for _, g := range got {
				if !tt.flags.compareCreatedAt {
					g.CreatedAt = nil
				}

				g.UpdatedAt = nil
			}

			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetTierMovementHistories() (-got +want) %s", diff)
			}

			if diff := cmp.Diff(got2, tt.want2, protocmp.Transform()); diff != "" {
				t.Errorf("GetTierMovementHistories() (-got2 +want2) %s", diff)
			}
		})
	}
}

func TestTierMovementHistoryDaoCrdb_GetDistinctTiersForActor(t *testing.T) {
	layout := "2006-01-02 15:04:05.999999999 -07:00"
	t1, err1 := time.Parse(layout, "2021-11-01 05:39:56.184776 +00:00")
	require.NoError(t, err1)
	type args struct {
		ctx       context.Context
		actorId   string
		timeSince time.Time
		timeTill  time.Time
	}
	tests := []struct {
		name    string
		args    args
		want    []tieringEnumPb.Tier
		wantErr bool
	}{
		{
			name: "successfully retrieve distinct tiers for actor",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				timeSince: t1,
				timeTill:  time.Now(),
			},
			want: []tieringEnumPb.Tier{
				tieringEnumPb.Tier_TIER_TEN,
				tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				tieringEnumPb.Tier_TIER_ONE_THOUSAND,
			},
			wantErr: false,
		},
		{
			name: "successfully retrieve distinct tiers for actor, filter unspecified tier",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-4",
				timeSince: t1,
				timeTill:  time.Now(),
			},
			want: []tieringEnumPb.Tier{
				tieringEnumPb.Tier_TIER_ONE_THOUSAND,
			},
			wantErr: false,
		},
		{
			name: "no tiers for non-existent actor",
			args: args{
				ctx:       context.Background(),
				actorId:   "non-existent-actor",
				timeSince: t1,
				timeTill:  time.Now(),
			},
			want:    []tieringEnumPb.Tier{},
			wantErr: false,
		},
		{
			name: "no tiers within time range",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-1",
				timeSince: time.Now().Add(24 * time.Hour), // future time
				timeTill:  time.Now().Add(48 * time.Hour),
			},
			want:    []tieringEnumPb.Tier{},
			wantErr: false,
		},
		{
			name: "should return first 6 days distinct tiers of users of november ",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				timeSince: time.Date(2021, 11, 1, 0, 0, 0, 0, time.UTC),
				timeTill:  time.Date(2021, 11, 6, 0, 0, 0, 0, time.UTC),
			},
			want: []tieringEnumPb.Tier{
				tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				tieringEnumPb.Tier_TIER_ONE_THOUSAND,
			},
			wantErr: false,
		},
		{
			name: "actor-3, till 2021-11-20, expect TIER_ONE_HUNDRED, TIER_ONE_THOUSAND, TIER_TEN",
			args: args{
				ctx:       context.Background(),
				actorId:   "actor-3",
				timeSince: time.Date(2021, 11, 1, 0, 0, 0, 0, time.UTC),
				timeTill:  time.Date(2021, 11, 20, 0, 0, 0, 0, time.UTC),
			},
			want: []tieringEnumPb.Tier{
				tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				tieringEnumPb.Tier_TIER_ONE_THOUSAND,
				tieringEnumPb.Tier_TIER_TEN,
			},
			wantErr: false,
		},
	}

	pkgTest.TruncateAndPopulateRdsFixtures(t, tierMovementHistoryTestSuite.pgdb, tierMovementHistoryTestSuite.gconf.TieringDb().GetName(), AffectedTestTables)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &TierMovementHistoryImpl{
				pgdb: tierMovementHistoryTestSuite.pgdb,
			}
			got, err := c.GetDistinctTiersForActor(tt.args.ctx, tt.args.actorId, tt.args.timeSince, tt.args.timeTill)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetDistinctTiersForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Sort both slices for consistent comparison
			sort.Slice(got, func(i, j int) bool { return got[i] < got[j] })
			sort.Slice(tt.want, func(i, j int) bool { return tt.want[i] < tt.want[j] })

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDistinctTiersForActor() = %v, want %v", got, tt.want)
			}
		})
	}
}
