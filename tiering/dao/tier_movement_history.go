// nolint:dupl
package dao

import (
	"context"
	"errors"
	"sort"
	"time"

	"github.com/samber/lo"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/dao/model"
)

type TierMovementHistoryImpl struct {
	pgdb        dbTypes.TieringPGDB
	maxPageSize uint32
}

func NewTierMovementHistoryImpl(
	pgdb dbTypes.TieringPGDB, maxPageSize uint32,
) *TierMovementHistoryImpl {
	return &TierMovementHistoryImpl{
		pgdb:        pgdb,
		maxPageSize: maxPageSize,
	}
}

var tierMovementHistoryFieldMaskMap = map[tieringPb.TierMovementHistoryFieldMask]string{
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ID:                                "id",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ELIGIBILITY_MOVEMENT_REFERENCE_ID: "eligibility_movement_reference_id",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CRITERIA_REFERENCE_ID:             "criteria_reference_id",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER:                         "from_tier",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER:                           "to_tier",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_ACTOR_ID:                          "actor_id",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE:                     "movement_type",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE:                        "provenance",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON:                            "reason",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT:                        "created_at",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_UPDATED_AT:                        "updated_at",
	tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_DELETED_AT:                        "deleted_at",
}

var _ TierMovementHistoryDao = &TierMovementHistoryImpl{}

func (a *TierMovementHistoryImpl) Create(ctx context.Context, tierMovementHistory *tieringPb.TierMovementHistory) (*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "Create", time.Now())
	tierMovementHistoryModel := model.NewTierMovementHistory(tierMovementHistory)
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	res := d.Create(&tierMovementHistoryModel)
	if res.Error != nil {
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return tierMovementHistoryModel.GetProto(), nil
}

func (a *TierMovementHistoryImpl) Get(ctx context.Context, id string, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "Get", time.Now())
	if id == "" {
		return nil, errors.New("id is mandatory")
	}

	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var tierMovementHistoryModel model.TierMovementHistory

	var selectedColumns []string
	if len(fieldMasks) == 0 {
		selectedColumns = a.selectAllColumnsForFieldMask()
	} else {
		selectedColumns = a.selectedColumnsForFieldMask(fieldMasks)
	}
	if err := d.Select(selectedColumns).Where("id = ?", id).Take(&tierMovementHistoryModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return tierMovementHistoryModel.GetProto(), nil
}

func (a *TierMovementHistoryImpl) Update(ctx context.Context, tierMovementHistory *tieringPb.TierMovementHistory, updateMask []tieringPb.TierMovementHistoryFieldMask) error {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "Update", time.Now())
	if tierMovementHistory.GetId() == "" {
		return errors.New("id is mandatory")
	}
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	tierMovementHistoryModel := model.NewTierMovementHistory(tierMovementHistory)
	updateColumns := a.selectedColumnsForFieldMask(updateMask)

	res := d.Model(&model.TierMovementHistory{}).Where("id = ?", tierMovementHistory.GetId()).Select(updateColumns).Updates(tierMovementHistoryModel)
	if res.Error != nil {
		return res.Error
	}
	return nil
}

func (a *TierMovementHistoryImpl) GetByEligibilityMovementRefId(ctx context.Context, eligibilityMovementReferenceId string, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetByEligibilityMovementRefId", time.Now())
	if eligibilityMovementReferenceId == "" {
		return nil, errors.New("eligibilityMovementReferenceId is mandatory")
	}
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var tierMovementHistoryModel model.TierMovementHistory

	var selectedColumns []string
	if len(fieldMasks) == 0 {
		selectedColumns = a.selectAllColumnsForFieldMask()
	} else {
		selectedColumns = a.selectedColumnsForFieldMask(fieldMasks)
	}
	if err := d.Select(selectedColumns).Where("eligibility_movement_reference_id = ?", eligibilityMovementReferenceId).Take(&tierMovementHistoryModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return tierMovementHistoryModel.GetProto(), nil
}

func (a *TierMovementHistoryImpl) GetMovementCountByActorIdTierFromMovementType(ctx context.Context, actorId string,
	tierFrom tieringEnumPb.Tier, movementType tieringEnumPb.TierMovementType, timeAfter time.Time) (int64, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetMovementCountByActorIdTierFromMovementType", time.Now())
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var count int64
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Where("from_tier = ?", tierFrom).
		Where("movement_type = ?", movementType).
		Where("created_at >= ?", timeAfter).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (a *TierMovementHistoryImpl) GetMovementCountByActorIdMovementType(ctx context.Context, actorId string,
	movementType tieringEnumPb.TierMovementType, timeAfter time.Time) (int64, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetMovementCountByActorIdMovementType", time.Now())
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var count int64
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Where("movement_type = ?", movementType).
		Where("created_at >= ?", timeAfter).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (a *TierMovementHistoryImpl) GetMovementListByActorIdTierFromMovementType(ctx context.Context, actorId string,
	tierFrom tieringEnumPb.Tier, movementType tieringEnumPb.TierMovementType, timeAfter time.Time, limit int) ([]*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetMovementListByActorIdTierFromMovementType", time.Now())
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var movementHistoryList []model.TierMovementHistory
	// TODO (sayan) : add proper index
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Where("from_tier = ?", tierFrom).
		Where("movement_type = ?", movementType).
		Where("created_at >= ?", timeAfter).
		Order("created_at desc").
		Limit(limit).
		Find(&movementHistoryList).
		Error; err != nil {
		return nil, err
	}
	var tmhPbList []*tieringPb.TierMovementHistory
	for index := range movementHistoryList {
		tmhPbList = append(tmhPbList, movementHistoryList[index].GetProto())
	}
	return tmhPbList, nil
}

func (a *TierMovementHistoryImpl) GetMovementListByActorIdMovementTypeAfterTimestamp(ctx context.Context, actorId string,
	movementType tieringEnumPb.TierMovementType, timeAfter time.Time, limit int) ([]*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetMovementListByActorIdMovementTypeAfterTimestamp", time.Now())
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var movementHistoryList []model.TierMovementHistory
	// TODO (sayan) : add proper index
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Where("movement_type = ?", movementType).
		Where("created_at >= ?", timeAfter).
		Order("created_at desc").
		Limit(limit).
		Find(&movementHistoryList).
		Error; err != nil {
		return nil, err
	}
	var tmhPbList []*tieringPb.TierMovementHistory
	for index := range movementHistoryList {
		tmhPbList = append(tmhPbList, movementHistoryList[index].GetProto())
	}
	return tmhPbList, nil
}

func (a *TierMovementHistoryImpl) GetLatestByActorIdAndTierBeforeTimestamp(ctx context.Context, actorId string, tiers []tieringEnumPb.Tier,
	timeBefore time.Time, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetLatestByActorIdAndTierBeforeTimestamp", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id is mandatory")
	}
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var tierMovementHistoryModel model.TierMovementHistory

	var selectedColumns []string
	if len(fieldMasks) == 0 {
		selectedColumns = a.selectAllColumnsForFieldMask()
	} else {
		selectedColumns = a.selectedColumnsForFieldMask(fieldMasks)
	}
	if err := d.Select(selectedColumns).
		Where("actor_id = ?", actorId).
		Where("to_tier IN ?", tiers).
		Where("created_at <= ?", timeBefore).
		Order("created_at DESC").
		Take(&tierMovementHistoryModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return tierMovementHistoryModel.GetProto(), nil
}

func (a *TierMovementHistoryImpl) GetByActorId(ctx context.Context, actorId string, limit int) ([]*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetByActorId", time.Now())
	if actorId == "" {
		return nil, errors.New("actorId is mandatory")
	}
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var movementHistoryList []model.TierMovementHistory
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Order("created_at DESC").
		Limit(limit).
		Find(&movementHistoryList).
		Error; err != nil {
		return nil, err
	}
	var tmhPbList []*tieringPb.TierMovementHistory
	for index := range movementHistoryList {
		tmhPbList = append(tmhPbList, movementHistoryList[index].GetProto())
	}
	return tmhPbList, nil
}

func (a *TierMovementHistoryImpl) GetLatestByActorIdAndMovementType(ctx context.Context, actorId string,
	movementType tieringEnumPb.TierMovementType, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetLatestByActorIdAndMovementType", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id is mandatory")
	}
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var tierMovementHistoryModel model.TierMovementHistory

	var selectedColumns []string
	if len(fieldMasks) == 0 {
		selectedColumns = a.selectAllColumnsForFieldMask()
	} else {
		selectedColumns = a.selectedColumnsForFieldMask(fieldMasks)
	}
	if err := d.Select(selectedColumns).
		Where("actor_id = ?", actorId).
		Where("movement_type = ?", movementType).
		Order("created_at DESC").
		Take(&tierMovementHistoryModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return tierMovementHistoryModel.GetProto(), nil
}

func (a *TierMovementHistoryImpl) GetLatestByActorId(ctx context.Context, actorId string,
	fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetLatestByActorId", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id is mandatory")
	}
	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	var tierMovementHistoryModel model.TierMovementHistory

	var selectedColumns []string
	if len(fieldMasks) == 0 {
		selectedColumns = a.selectAllColumnsForFieldMask()
	} else {
		selectedColumns = a.selectedColumnsForFieldMask(fieldMasks)
	}
	if err := d.Select(selectedColumns).
		Where("actor_id = ?", actorId).
		Order("created_at DESC").
		Take(&tierMovementHistoryModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return tierMovementHistoryModel.GetProto(), nil
}

func (a *TierMovementHistoryImpl) GetTierMovementHistories(ctx context.Context, actorId string, pageToken *pagination.PageToken, pageSize uint32, options []storagev2.FilterOption, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) ([]*tieringPb.TierMovementHistory, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetTierMovementHistories", time.Now())
	if actorId == "" {
		return nil, nil, errors.New("actor id is mandatory")
	}

	d := gormctxv2.FromContextOrDefault(ctx, a.pgdb)
	d = d.Model(&model.TierMovementHistory{})
	d = d.Where("actor_id = ?", actorId)
	for _, opts := range options {
		d = opts.ApplyInGorm(d)
	}

	var selectedColumns []string
	if len(fieldMasks) == 0 {
		selectedColumns = a.selectAllColumnsForFieldMask()
	} else {
		selectedColumns = a.selectedColumnsForFieldMask(fieldMasks)
	}

	d = d.Select(selectedColumns)

	// fetch pageSize + 1 extra row to compute next page availability.
	if pageSize > a.maxPageSize || pageSize == 0 {
		pageSize = a.maxPageSize
	}

	d = d.Limit(int(pageSize) + 1)

	if pageToken != nil {
		if pageToken.IsReverse {
			d = d.Where("created_at <= ?", pageToken.Timestamp.AsTime()).Order("created_at" + " DESC")
		} else {
			d = d.Where("created_at >= ?", pageToken.Timestamp.AsTime()).Order("created_at" + " ASC")
		}
		d = d.Offset(int(pageToken.Offset))
	} else {
		d = d.Order("created_at" + " ASC")
	}

	tierMovementHistoryModels := make([]*model.TierMovementHistory, 0)
	err := d.Scan(&tierMovementHistoryModels).Error
	if err != nil {
		return nil, nil, err
	}

	if len(tierMovementHistoryModels) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), model.TierMovementHistories(tierMovementHistoryModels))
	if err != nil {
		return nil, nil, err
	}

	tierMovementHistoryModels = rows.(model.TierMovementHistories)
	tmhProtos := make([]*tieringPb.TierMovementHistory, len(tierMovementHistoryModels))
	for i, tmhModel := range tierMovementHistoryModels {
		tmhProtos[i] = tmhModel.GetProto()
	}

	return tmhProtos, pageCtxResp, nil
}

func (t *TierMovementHistoryImpl) GetDistinctTiersForActor(ctx context.Context, actorId string, timeSince time.Time) ([]tieringEnumPb.Tier, error) {
	defer metric_util.TrackDuration("tiering/dao", "TierMovementHistoryImpl", "GetDistinctTiersForActor", time.Now())
	d := gormctxv2.FromContextOrDefault(ctx, t.pgdb)
	var toTierList []tieringEnumPb.Tier
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Where("created_at >= ?", timeSince).
		Select("DISTINCT to_tier").Find(&toTierList).Error; err != nil {
		return nil, err
	}

	var fromTierList []tieringEnumPb.Tier
	if err := d.Model(&model.TierMovementHistory{}).Where("actor_id = ?", actorId).
		Where("created_at >= ?", timeSince).
		Select("DISTINCT from_tier").Find(&fromTierList).Error; err != nil {
		return nil, err
	}

	distinctTierList := lo.Union(toTierList, fromTierList)
	return lo.Filter(distinctTierList, func(item tieringEnumPb.Tier, _ int) bool {
		return item != tieringEnumPb.Tier_TIER_UNSPECIFIED
	}), nil
}

// selectedColumnsForFieldMask converts field mask from enum to column_name string values
func (a *TierMovementHistoryImpl) selectedColumnsForFieldMask(fieldMask []tieringPb.TierMovementHistoryFieldMask) []string {
	var selectColumns []string
	for _, field := range fieldMask {
		selectColumns = append(selectColumns, tierMovementHistoryFieldMaskMap[field])
	}
	sort.Strings(selectColumns)
	return selectColumns
}

// selectAllColumnsForFieldMask converts all field mask enums to column_name string values array
func (a *TierMovementHistoryImpl) selectAllColumnsForFieldMask() []string {
	var selectColumns []string
	for _, columnName := range tierMovementHistoryFieldMaskMap {
		selectColumns = append(selectColumns, columnName)
	}
	sort.Strings(selectColumns)
	return selectColumns
}
