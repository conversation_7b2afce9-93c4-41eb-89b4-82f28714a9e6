//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .

package dao

import (
	"context"
	"time"

	"github.com/google/wire"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
)

var ActorTierInfoSet = wire.NewSet(
	NewActorTierInfoCache, wire.Bind(new(ActorTierInfoDao), new(*ActorTierInfoCache)),
	NewActorTierInfoImpl,
)

var TierCriteriaSet = wire.NewSet(
	NewTierCriteriaImpl, wire.Bind(new(TierCriteriaDao), new(*TierCriteriaImpl)),
)

var EligibleTierMovementSet = wire.NewSet(
	NewEligibleTierMovementCache, wire.Bind(new(EligibleTierMovementDao), new(*EligibleTierMovementCache)),
	NewEligibleTierMovementImpl,
)

var TierMovementHistorySet = wire.NewSet(
	NewTierMovementHistoryCache, wire.Bind(new(TierMovementHistoryDao), new(*TierMovementHistoryCache)),
	NewTierMovementHistoryImpl,
)

var ActorScreenInteractionSet = wire.NewSet(
	NewActorScreenInteractionImpl, wire.Bind(new(ActorScreenInteractionDao), new(*ActorScreenInteractionImpl)),
)

var WireSet = wire.NewSet(
	ActorTierInfoSet,
	TierCriteriaSet,
	EligibleTierMovementSet,
	TierMovementHistorySet,
	ActorScreenInteractionSet,
)

// TierCriteriaDao defines all dao operations that can be done on TierCriteria entity
type TierCriteriaDao interface {
	// Create creates a new tier criteria in database and returns created object
	// Returns error in case of any DB creation failure
	Create(ctx context.Context, tierCriteria *tieringPb.TierCriteria) (*tieringPb.TierCriteria, error)
	// Get fetches the tier criteria by its primary key i.e, id
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	Get(ctx context.Context, id string, fieldMasks ...tieringPb.TierCriteriaFieldMask) (*tieringPb.TierCriteria, error)
	// Update takes the update mask and updates tier criteria for those columns
	// Returns error in case of any DB update failure
	Update(ctx context.Context, tierCriteria *tieringPb.TierCriteria, updateMask []tieringPb.TierCriteriaFieldMask) error
	// GetByCriteriaName fetches tier criteria by criteria name
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetByCriteriaName(ctx context.Context, criteriaName tieringEnumPb.CriteriaName, fieldMasks ...tieringPb.TierCriteriaFieldMask) (*tieringPb.TierCriteria, error)
	// GetActiveCriteria fetches the active criteria
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetActiveCriteria(ctx context.Context, fieldMasks ...tieringPb.TierCriteriaFieldMask) (*tieringPb.TierCriteria, error)
}

// ActorTierInfoDao defines all dao operations that can be done on ActorTierInfo entity
type ActorTierInfoDao interface {
	// Create creates a new tier criteria in database and returns created object
	// Returns error in case of any DB creation failure
	Create(ctx context.Context, actorTierInfo *tieringPb.ActorTierInfo) (*tieringPb.ActorTierInfo, error)
	// Get fetches the tier criteria by its primary key i.e, actorId
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	Get(ctx context.Context, actorId string, fieldMasks ...tieringPb.ActorTierInfoFieldMask) (*tieringPb.ActorTierInfo, error)
	// Update takes the update mask and updates tier criteria for those columns
	// Returns error in case of any DB update failure
	Update(ctx context.Context, actorTierInfo *tieringPb.ActorTierInfo, updateMask []tieringPb.ActorTierInfoFieldMask) (*tieringPb.ActorTierInfo, error)
}

// EligibleTierMovementDao defines all dao operations that can be done on EligibleTierMovement entity
type EligibleTierMovementDao interface {
	// Create creates a new tier criteria in database and returns created object
	// Returns error in case of any DB creation failure
	Create(ctx context.Context, eligibleTierMovement *tieringPb.EligibleTierMovement) (*tieringPb.EligibleTierMovement, error)
	// Get fetches the tier criteria by its primary key i.e, id
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	Get(ctx context.Context, id string, fieldMasks ...tieringPb.EligibleTierMovementFieldMask) (*tieringPb.EligibleTierMovement, error)
	// Update takes the update mask and updates tier criteria for those columns
	// Returns error in case of any DB update failure
	Update(ctx context.Context, eligibleTierMovement *tieringPb.EligibleTierMovement, updateMask []tieringPb.EligibleTierMovementFieldMask) error
	// GetLatestByActorIdAndStatus fetches the latest eligible tier movement by actorId and status
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetLatestByActorIdAndStatus(ctx context.Context, actorId string, status tieringEnumPb.EligibleTierMovementStatus, fieldMasks ...tieringPb.EligibleTierMovementFieldMask) (*tieringPb.EligibleTierMovement, error)
	// GetLatestByActorIdAndStatus fetches the latest eligible tier movement by actorId and status
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetLatestByActorIdAndMovementType(ctx context.Context, actorId string, movementType tieringEnumPb.TierMovementType, fieldMasks ...tieringPb.EligibleTierMovementFieldMask) (*tieringPb.EligibleTierMovement, error)
	// GetLatestByActorIdMovementTypeStatus gets the latest eligible movement by actor, movement type and status
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetLatestByActorIdMovementTypeStatus(ctx context.Context, actorId string, movementType tieringEnumPb.TierMovementType, status tieringEnumPb.EligibleTierMovementStatus, fieldMasks ...tieringPb.EligibleTierMovementFieldMask) (*tieringPb.EligibleTierMovement, error)
	// GetByActorId fetches all eligible tier movements for actor for a given limit in descending order
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetByActorId(ctx context.Context, actorId string, limit int) ([]*tieringPb.EligibleTierMovement, error)
}

// TierMovementHistoryDao defines all dao operations that can be done on TierMovementHistory entity
type TierMovementHistoryDao interface {
	// Create creates a new tier criteria in database and returns created object
	// Returns error in case of any DB creation failure
	Create(ctx context.Context, tierMovementHistory *tieringPb.TierMovementHistory) (*tieringPb.TierMovementHistory, error)
	// Get fetches the tier criteria by its primary key i.e, id
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	Get(ctx context.Context, id string, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error)
	// Update takes the update mask and updates tier criteria for those columns
	// Returns error in case of any DB update failure
	Update(ctx context.Context, tierMovementHistory *tieringPb.TierMovementHistory, updateMask []tieringPb.TierMovementHistoryFieldMask) error
	// GetByEligibilityMovementRefId fetches the tier criteria by eligibility movement reference id
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetByEligibilityMovementRefId(ctx context.Context, eligibilityMovementReferenceId string, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error)
	// GetMovementCountByActorIdTierFromMovementType gets number of movements for an actorId, from a particular tier, for a specific movement type and after a certain time
	GetMovementCountByActorIdTierFromMovementType(ctx context.Context, actorId string, tierFrom tieringEnumPb.Tier, movementType tieringEnumPb.TierMovementType, timeAfter time.Time) (int64, error)
	// GetMovementCountByActorIdMovementType gets number of movements for an actorId, for a specific movement type and after a certain time
	GetMovementCountByActorIdMovementType(ctx context.Context, actorId string, movementType tieringEnumPb.TierMovementType, timeAfter time.Time) (int64, error)
	// GetMovementListByActorIdTierFromMovementType gets the movements for an actorId, from a particular tier, for a specific movement type and after a certain time
	// TODO(sainath): Move to paginated
	GetMovementListByActorIdTierFromMovementType(ctx context.Context, actorId string, tierFrom tieringEnumPb.Tier, movementType tieringEnumPb.TierMovementType, timeAfter time.Time, limit int) ([]*tieringPb.TierMovementHistory, error)
	// GetMovementListByActorIdMovementTypeAfterTimestamp gets number of movements for an actorId, for a specific movement type and after a certain time
	// TODO(sainath): Move to paginated
	GetMovementListByActorIdMovementTypeAfterTimestamp(ctx context.Context, actorId string, movementType tieringEnumPb.TierMovementType, timeAfter time.Time, limit int) ([]*tieringPb.TierMovementHistory, error)
	// GetLatestByActorIdAndTierBeforeTimestamp returns latest tier movement before given timestamp by actorId and tier
	GetLatestByActorIdAndTierBeforeTimestamp(ctx context.Context, actorId string, tiers []tieringEnumPb.Tier, timeBefore time.Time, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error)
	// GetByActorId fetches all tier movement histories for actor for a given limit in descending order
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	// TODO(sainath): Move to paginated
	GetByActorId(ctx context.Context, actorId string, limit int) ([]*tieringPb.TierMovementHistory, error)
	// GetLatestByActorIdAndMovementType fetches the latest tier movement for a given actor of given movement type
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetLatestByActorIdAndMovementType(ctx context.Context, actorId string, movementType tieringEnumPb.TierMovementType, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error)
	// GetLatestByActorId fetches the latest tier movement for a given actor
	// Returns epifierrors.ErrRecordNotFound if record is not found in DB
	// Returns error in case of any other get error from DB
	GetLatestByActorId(ctx context.Context, actorId string, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) (*tieringPb.TierMovementHistory, error)
	// GetTierMovementHistories returns paginated list of tier movement histories
	// actorId is a mandatory filter
	GetTierMovementHistories(ctx context.Context, actorId string, pageToken *pagination.PageToken, pageSize uint32, options []storagev2.FilterOption, fieldMasks ...tieringPb.TierMovementHistoryFieldMask) ([]*tieringPb.TierMovementHistory, *rpc.PageContextResponse, error)
	// GetDistinctTiersForActor returns the list of tiers , user has been in , from timeSince to timeTill
	// mandatory request params : actorId , timeSince , timeTill
	// returns empty list if no tier movement history found in the given time range
	GetDistinctTiersForActor(ctx context.Context, actorId string, timeSince time.Time, timeTill time.Time) ([]tieringEnumPb.Tier, error)
}

// ActorScreenInteractionDao defines all dao operations that can be done on ActorScreenInteraction entity
type ActorScreenInteractionDao interface {
	// Create creates a new actor page visit in database and returns created object
	Create(ctx context.Context, actorScreenInteraction *tieringPb.ActorScreenInteraction) (*tieringPb.ActorScreenInteraction, error)
	// Update takes the update mask and updates actor page detail for those columns
	// Returns error in case of any DB update failure
	Update(ctx context.Context, actorScreenInteraction *tieringPb.ActorScreenInteraction, updateMask []tieringPb.ActorScreenInteractionFieldMask) error
	// GetByActorIdAndScreen fetches the actor page detail by actorId and screen
	GetByActorIdAndScreen(ctx context.Context, actorId string, screen tieringEnumPb.TieringScreen) (*tieringPb.ActorScreenInteraction, error)
}

// getDbImpl method provides the DB (CRDB/PGDB) based on pgdb use flag
func getDbImpl(toUsePgdb bool, crdb dbTypes.EpifiCRDB, pgdb dbTypes.TieringPGDB) *gormv2.DB {
	if toUsePgdb {
		return pgdb
	}
	return crdb
}
