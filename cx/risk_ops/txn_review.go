package risk_ops

import (
	"context"
	"errors"
	"fmt"
	"time"

	grpc_zap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	riskOpsPb "github.com/epifi/gamma/api/cx/risk_ops"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cxLogger "github.com/epifi/gamma/cx/logger"
	riskCxEvents "github.com/epifi/gamma/cx/risk_ops/events"
)

// nolint: funlen
func (s *Service) GetTransactionReviewDetails(req *riskOpsPb.GetTransactionReviewDetailsRequest,
	respServer riskOpsPb.RiskOps_GetTransactionReviewDetailsServer) error {
	ctx := respServer.Context()
	ctx = epificontext.CtxWithAgentEmail(ctx, req.GetHeader().GetAgentEmail())
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	if len(req.GetActorId()) == 0 {
		cxLogger.Error(ctx, "actor id is nil in req")
		return fmt.Errorf("actor id is not passed in the request")
	}

	goroutine.RunWithDefaultTimeout(ctx, func(goCtx context.Context) {
		// populate case id pending here
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(goCtx), riskCxEvents.NewSherlockPageHitEvent(req.GetHeader().GetAgentEmail(), req.GetCaseId(), riskCxEvents.TransactionViewLoadPageEventName))
	})
	startTime := time.Now()
	cxLogger.Info(ctx, "started fetching transactions for txn review flow", zap.Int("txn_duration_in_days", getTimeRangeInDays(req.GetFilters())))

	account, err := s.getSavingsAccount(ctx, req.GetActorId())
	if err != nil {
		cxLogger.Error(ctx, "failed to fetch savings account", zap.Error(err))
		return fmt.Errorf("error in fetching savins account for actor")
	}

	var (
		txnCount  int
		pageCount int
	)

	var pageContextRequest *rpcPb.PageContextRequest
	for {
		txnReviewObj, responseContext, fetchErr := s.txnReviewDataFetcher.Fetch(ctx, &riskOpsPb.GetTransactionsRequest{
			ActorId:            req.GetActorId(),
			AccountId:          account.GetId(),
			Filters:            req.GetFilters(),
			PageContextRequest: pageContextRequest,
		})
		switch {
		case errors.Is(fetchErr, epifierrors.ErrRecordNotFound):
			break
		case fetchErr != nil:
			cxLogger.Error(ctx, "error in fetching the risk review transactions", zap.Error(fetchErr))
			return fmt.Errorf("error while fetching transactions from pay service")
		}

		// capturing total number of pages and txns to log it for visibility
		pageCount++
		txnCount += len(txnReviewObj)
		if sendErr := respServer.Send(&riskOpsPb.GetTransactionReviewDetailsResponse{
			Status:       rpcPb.StatusOk(),
			Transactions: txnReviewObj,
		}); sendErr != nil {
			cxLogger.Error(ctx, "got some error while sending the response", zap.Error(sendErr))
			return fmt.Errorf("error while sending response, %w", sendErr)
		}

		// Implementation restricts movement in one side. Before token won't be empty if next page exists.
		// i.e. before token is equivalent to next page token.
		if !responseContext.GetHasBefore() || responseContext.GetBeforeToken() == "" {
			break
		}
		// Not specifying page size since page size will be decided by internal implementation
		pageContextRequest = &rpcPb.PageContextRequest{
			Token: &rpcPb.PageContextRequest_BeforeToken{
				BeforeToken: responseContext.GetBeforeToken(),
			},
		}
	}
	timeSpentLog := grpc_zap.DefaultDurationToField(time.Since(startTime))
	cxLogger.Info(ctx, "Successfully finished fetching transactions for review",
		zap.Int("txn_duration_in_days", getTimeRangeInDays(req.GetFilters())),
		timeSpentLog, zap.Int(logger.PAGE_COUNT, pageCount), zap.Int(logger.TXN_COUNT, txnCount))
	return nil
}

func getTimeRangeInDays(filters *riskOpsPb.TransactionFilters) int {
	inHours := filters.GetToTime().AsTime().Sub(filters.GetFromTime().AsTime()).Hours()
	return int(inHours / 24)
}

func (s *Service) getAlertsForActor(ctx context.Context, actorId string) ([]*caseManagementPb.AlertWithRuleDetails, error) {
	resp, err := s.caseManagementClient.GetAlerts(ctx, &caseManagementPb.GetAlertsRequest{
		ActorId: actorId,
		Limit:   200,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, err
	}
	return resp.GetAlerts(), nil
}
