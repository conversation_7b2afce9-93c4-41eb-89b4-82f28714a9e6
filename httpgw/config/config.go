// nolint: unused
package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	// loads config from file
	k, _, err2 := cfg.LoadConfigUsingKoanf(configDirPath, cfg.HTTP_GATEWAY_SERVICE)
	if err2 != nil {
		return nil, fmt.Errorf("failed to load config: %w", err2)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, fmt.Errorf("error in loading secrets: %w", err)
	}

	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	if err = readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	// update default pgp keys and passphrase in config.Secrets.ids
	for k, v := range keyToSecret {
		c.Secrets.Ids[k] = v
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val) // nolint: govet
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/httpgw/config Config
type Config struct {
	Application   *Application
	Server        *Server
	Logging       *cfg.Logging
	SecureLogging *SecureLogging
	Secrets       *cfg.Secrets
	AWS           *Aws
	HttpServer    *HttpServer
	Tracing       *cfg.Tracing
	Profiling     *cfg.Profiling
	RudderStack   *cfg.RudderStackBroker
}

type SecureLogging struct {
	EnableSecureLog bool
	SecureLogPath   string
	MaxSizeInMBs    int // megabytes
	MaxBackups      int // There will be MaxBackups + 1 total files
}

type RudderStackBroker struct {
	Host          string
	Key           string
	IntervalInSec time.Duration
	BatchSize     int
	Verbose       bool
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Port            int
	HealthCheckPort int
}

type Aws struct {
	Region string
}

type HttpServer struct {
	// ReadHeaderTimeout is the amount of time allowed to read
	// request headers. The connection's read deadline is reset
	// after reading the headers and the Handler can decide what
	// is considered too slow for the body. If ReadHeaderTimeout
	// is zero, the value of ReadTimeout is used. If both are
	// zero, there is no timeout.
	ReadHeaderTimeout time.Duration

	// ReadTimeout is the maximum duration for reading the entire
	// request, including the body.
	//
	// Because ReadTimeout does not let Handlers make per-request
	// decisions on each request body's acceptable deadline or
	// upload rate, most users will prefer to use
	// ReadHeaderTimeout. It is valid to use them both.
	ReadTimeout time.Duration
}
