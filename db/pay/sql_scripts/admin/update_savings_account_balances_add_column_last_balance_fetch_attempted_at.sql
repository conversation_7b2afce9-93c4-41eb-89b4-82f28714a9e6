-- Add the new column `last_balance_fetch_attempted_at` if it doesn't exist with Default value NULL
ALTER TABLE public.savings_account_balances ADD COLUMN IF NOT EXISTS last_balance_fetch_attempted_at TIMESTAMPTZ DEFAULT NULL;

-- Create an index on the new column for better query performance
CREATE INDEX IF NOT EXISTS savings_account_balances_last_fetch_attempted_at_idx ON public.savings_account_balances (last_balance_fetch_attempted_at DESC);
