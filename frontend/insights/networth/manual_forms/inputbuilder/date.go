// nolint:dupl
package inputbuilder

import (
	datePb "google.golang.org/genproto/googleapis/type/date"

	networthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthEnumsPb "github.com/epifi/gamma/api/frontend/insights/networth/enums"
)

type DateBuilder struct {
	*networthPb.NetWorthManualFormInputComponent
}

func NewDateBuilder(title, placeholderText, fieldName string) *DateBuilder {
	component := networthPb.NewNetWorthManualFormInputComponent(fieldName, networthEnumsPb.NetworthManualFormInputStyle_NETWORTH_MANUAL_FORM_INPUT_STYLE_DATE_PICKER)
	component.WithMandatoryDisplayTitle(title)
	component.WithMandatoryPlaceholderText(placeholderText)
	component.InputData = WithInputDataDate(fieldName)
	return &DateBuilder{NetWorthManualFormInputComponent: component}
}

func NewOptionalDateBuilder(title, placeholderText, fieldName string) *DateBuilder {
	component := networthPb.NewNetWorthManualFormInputComponent(fieldName, networthEnumsPb.NetworthManualFormInputStyle_NETWORTH_MANUAL_FORM_INPUT_STYLE_DATE_PICKER)
	component.MakeOptional()
	component.WithDefaultTitle(title)
	component.WithDefaultPlaceholderText(placeholderText)
	component.InputData = WithInputDataDate(fieldName)
	return &DateBuilder{NetWorthManualFormInputComponent: component}
}

func (d *DateBuilder) WithValue(value *datePb.Date) {
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetDateData().Data = value
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetDateData().Data = value
}

func (d *DateBuilder) WithValidation(validation *networthPb.DateType_DateValidation) {
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetDateData().Validation = validation
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetDateData().Validation = validation
}

func (d *DateBuilder) Build() *networthPb.NetWorthManualFormInputComponent {
	return d.NetWorthManualFormInputComponent
}

func WithInputDataDate(fieldName string) *networthPb.NetWorthManualInputData {
	return &networthPb.NetWorthManualInputData{
		FieldName: fieldName,
		DataType:  networthEnumsPb.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_DATE,
		Input: &networthPb.NetWorthManualInputData_SingleOption{
			SingleOption: &networthPb.SingleInputOption{
				InputValue: &networthPb.InputOptionValue{
					Value: &networthPb.InputOptionValue_DateData{
						DateData: &networthPb.DateType{},
					},
				},
				InputOptionData: &networthPb.InputOptionData{
					InputValue: &networthPb.InputOptionValue{
						Value: &networthPb.InputOptionValue_DateData{
							DateData: &networthPb.DateType{},
						},
					},
				},
			},
		},
	}
}
