// nolint:goimports
package fi_to_fi

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/durationpb"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	beCaPb "github.com/epifi/gamma/api/connected_account"
	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	beCaExternalPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/connected_account/mocks"
	feCaFeaturesFiToFiPb "github.com/epifi/gamma/api/frontend/connected_account/features"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/savings"
	mocks5 "github.com/epifi/gamma/api/savings/mocks"
	caDlScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	feCaHelper "github.com/epifi/gamma/frontend/connected_account"
	"github.com/epifi/gamma/frontend/test"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"
)

var (
	conf   *config.ConnectedAccount
	feConf *config.Config
	gconf  *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	feConf, gconf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestConnectFiToFiSvc_GetFiToFiBottomSheet(t *testing.T) {
	t.Skip()
	ctr := gomock.NewController(t)
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	mockReleaseClient := mock_release.NewMockIEvaluator(ctr)
	mockSavingsClient := mocks5.NewMockSavingsClient(ctr)
	mockOnbClient := mockOnb.NewMockOnboardingClient(ctr)

	defer ctr.Finish()
	dlScreenOptions, _ := deeplinkv3.GetScreenOptionV2(&caDlScreenOptions.LandingPageFiToFiFlowScreenOptions{
		AaEntity: types.AaEntity_AA_ENTITY_AA_FINVU,
	})

	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
		savingsClient        savings.SavingsClient
		conf                 *genconf.Config
		releaseEvaluator     release.IEvaluator
		onboardingClient     onbPb.OnboardingClient
	}
	type args struct {
		ctx         context.Context
		actorId     string
		appVersion  uint32
		appPlatform commontypes.Platform
		mocks       []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feCaFeaturesFiToFiPb.InitiateFiToFiFlowBottomSheet
		wantErr bool
	}{
		{
			name: "#1 Error evaluating if FiToFiFlow enabled for user, GetFiToFiBottomSheet returns nil,error",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						false, errors.New("failed to get constraint for the actor")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#2 Given the constraints Fi To Fi flow not enabled for the user, GetFiToFiBottomSheet returns nil,nil",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						false, nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "#3 Error getting all accounts for the actor, GetFiToFiBottomSheet returns nil,error",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						true, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
					mockCaClient.EXPECT().GetAllAccounts(context.Background(), &beCaPb.GetAllAccountsRequest{
						PageContext: &rpcPb.PageContextRequest{
							PageSize: feCaHelper.FederalFipMaxDepositAccounts,
						},
						AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						ActorId:               "actor_id1",
						AccountFilterList: []beCaExternalPb.AccountFilter{beCaExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
							beCaExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
						FipIdList: []string{caPkg.FederalFipId},
					}).Return(&beCaPb.GetAllAccountsResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#4 Record not found while getting all accounts for the actor, GetFiToFiBottomSheet returns bottom sheet,nil error",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						true, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
					mockCaClient.EXPECT().GetAllAccounts(context.Background(), &beCaPb.GetAllAccountsRequest{
						PageContext: &rpcPb.PageContextRequest{
							PageSize: feCaHelper.FederalFipMaxDepositAccounts,
						},
						AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						ActorId:               "actor_id1",
						AccountFilterList: []beCaExternalPb.AccountFilter{beCaExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
							beCaExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
						FipIdList: []string{caPkg.FederalFipId},
					}).Return(&beCaPb.GetAllAccountsResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil),
					mockCaClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{
						Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU}, nil),
				},
			},
			want: &feCaFeaturesFiToFiPb.InitiateFiToFiFlowBottomSheet{
				Title: commontypes.GetTextFromStringFontColourFontStyle(feCaHelper.FiToFiBottomSheetDisplayTitle,
					feCaHelper.FiToFiBottomSheetTitleFontColor,
					commontypes.FontStyle_HEADLINE_M,
				),
				Description: commontypes.GetTextFromStringFontColourFontStyle(feCaHelper.FiToFiBottomSheetDisplayDesc,
					feCaHelper.FiToFiBottomSheetDescFontColor,
					commontypes.FontStyle_BODY_S,
				),
				WealthAndAaTnc: &commontypes.Text{
					FontColor: feCaHelper.FiToFiBottomSheetWealthTncFontColor,
					DisplayValue: &commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">Finvu TnC</a>",
						"https://web.staging.pointz.in/wealth/TnC", "https://finvu.in/terms")},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_XS,
					},
				},
				AaEntity: types.AaEntity_AA_ENTITY_AA_FINVU,
				Cta: &deeplink.Cta{
					Type: deeplink.Cta_CONTINUE,
					Text: feCaHelper.FiToFiBottomSheetCtaDisplayText,
					Deeplink: &deeplink.Deeplink{
						Screen:          deeplink.Screen_CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN,
						ScreenOptionsV2: dlScreenOptions,
					},
					DisplayTheme: deeplink.Cta_PRIMARY,
					Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				},
				SecureSpendsIcon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: feCaHelper.FiToFiBottomSheetIconURL,
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  feCaHelper.FiToFiBottomSheetIconWidth,
								Height: feCaHelper.FiToFiBottomSheetIconHeight,
							},
						},
					},
				},
				MaxRetryAllowedFailureCase: 3,
				InvalidateNumRetryDuration: durationpb.New(86400 * time.Second),
			},
			wantErr: false,
		},
		{
			name: "#5 Fi Federal saving account already connected, GetFiToFiBottomSheet returns nil,nil",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				savingsClient:        mockSavingsClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						true, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
					mockCaClient.EXPECT().GetAllAccounts(context.Background(), &beCaPb.GetAllAccountsRequest{
						PageContext: &rpcPb.PageContextRequest{
							PageSize: feCaHelper.FederalFipMaxDepositAccounts,
						},
						AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						ActorId:               "actor_id1",
						AccountFilterList: []beCaExternalPb.AccountFilter{beCaExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
							beCaExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
						FipIdList: []string{caPkg.FederalFipId},
					}).Return(&beCaPb.GetAllAccountsResponse{
						Status: rpcPb.StatusOk(),
						AccountDetailsList: []*beCaExternalPb.AccountDetails{
							{MaskedAccountNumber: "QWER1234"},
						},
					}, nil),
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
						Account: &savings.Account{
							AccountNo: "QWERTY1234",
						},
					}, nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "#6 Fi Federal saving account not connected,, GetFiToFiBottomSheet returns bottom sheet,nil error",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				savingsClient:        mockSavingsClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						true, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
					mockCaClient.EXPECT().GetAllAccounts(context.Background(), &beCaPb.GetAllAccountsRequest{
						PageContext: &rpcPb.PageContextRequest{
							PageSize: feCaHelper.FederalFipMaxDepositAccounts,
						},
						AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						ActorId:               "actor_id1",
						AccountFilterList: []beCaExternalPb.AccountFilter{beCaExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
							beCaExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
						FipIdList: []string{caPkg.FederalFipId},
					}).Return(&beCaPb.GetAllAccountsResponse{
						Status:             rpcPb.StatusOk(),
						AccountDetailsList: []*beCaExternalPb.AccountDetails{},
					}, nil),
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
						Account: &savings.Account{
							AccountNo: "QWERTY1234",
						},
					}, nil),
					mockCaClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{
						Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU}, nil),
				},
			},
			want: &feCaFeaturesFiToFiPb.InitiateFiToFiFlowBottomSheet{
				Title: commontypes.GetTextFromStringFontColourFontStyle(feCaHelper.FiToFiBottomSheetDisplayTitle,
					feCaHelper.FiToFiBottomSheetTitleFontColor,
					commontypes.FontStyle_HEADLINE_M,
				),
				Description: commontypes.GetTextFromStringFontColourFontStyle(feCaHelper.FiToFiBottomSheetDisplayDesc,
					feCaHelper.FiToFiBottomSheetDescFontColor,
					commontypes.FontStyle_BODY_S,
				),
				WealthAndAaTnc: &commontypes.Text{
					FontColor: feCaHelper.FiToFiBottomSheetWealthTncFontColor,
					DisplayValue: &commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">Finvu TnC</a>",
						"https://web.staging.pointz.in/wealth/TnC", "https://finvu.in/terms")},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_XS,
					},
				},
				AaEntity: types.AaEntity_AA_ENTITY_AA_FINVU,
				Cta: &deeplink.Cta{
					Type: deeplink.Cta_CONTINUE,
					Text: feCaHelper.FiToFiBottomSheetCtaDisplayText,
					Deeplink: &deeplink.Deeplink{
						Screen:          deeplink.Screen_CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN,
						ScreenOptionsV2: dlScreenOptions,
					},
					DisplayTheme: deeplink.Cta_PRIMARY,
					Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				},
				SecureSpendsIcon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: feCaHelper.FiToFiBottomSheetIconURL,
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  feCaHelper.FiToFiBottomSheetIconWidth,
								Height: feCaHelper.FiToFiBottomSheetIconHeight,
							},
						},
					},
				},
				MaxRetryAllowedFailureCase: 3,
				InvalidateNumRetryDuration: durationpb.New(86400 * time.Second),
			},
			wantErr: false,
		},
		{
			name: "#7 Fi Federal saving account not connected, GetFiToFiBottomSheet returns nil response and internal error",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				savingsClient:        mockSavingsClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						true, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
					mockCaClient.EXPECT().GetAllAccounts(context.Background(), &beCaPb.GetAllAccountsRequest{
						PageContext: &rpcPb.PageContextRequest{
							PageSize: feCaHelper.FederalFipMaxDepositAccounts,
						},
						AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						ActorId:               "actor_id1",
						AccountFilterList: []beCaExternalPb.AccountFilter{beCaExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
							beCaExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
						FipIdList: []string{caPkg.FederalFipId},
					}).Return(&beCaPb.GetAllAccountsResponse{
						Status:             rpcPb.StatusOk(),
						AccountDetailsList: []*beCaExternalPb.AccountDetails{},
					}, nil),
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
						Account: &savings.Account{
							AccountNo: "QWERTY1234",
						},
					}, nil),
					mockCaClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{
						Status: rpcPb.StatusInternal()}, nil),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "#5 Fi Federal saving account is created within account discovery threshold return false,nil",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				conf:                 gconf,
				releaseEvaluator:     mockReleaseClient,
				savingsClient:        mockSavingsClient,
				onboardingClient:     mockOnbClient,
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor_id1",
				mocks: []interface{}{
					mockReleaseClient.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(
						types.Feature_CA_CONNECT_FI_TO_FI).WithActorId("actor_id1")).Return(
						true, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
					mockCaClient.EXPECT().GetAllAccounts(context.Background(), &beCaPb.GetAllAccountsRequest{
						PageContext: &rpcPb.PageContextRequest{
							PageSize: feCaHelper.FederalFipMaxDepositAccounts,
						},
						AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
						ActorId:               "actor_id1",
						AccountFilterList: []beCaExternalPb.AccountFilter{beCaExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
							beCaExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
						FipIdList: []string{caPkg.FederalFipId},
					}).Return(&beCaPb.GetAllAccountsResponse{
						Status: rpcPb.StatusOk(),
						AccountDetailsList: []*beCaExternalPb.AccountDetails{
							{MaskedAccountNumber: "QWER1234"},
						},
					}, nil),
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
						Account: &savings.Account{
							AccountNo: "QWERTY12345",
							CreatedAt: timestamp.Now(),
						},
					}, nil),
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ConnectFiToFiSvc{
				beConnectedAccClient: tt.fields.beConnectedAccClient,
				savingsClient:        tt.fields.savingsClient,
				conf:                 tt.fields.conf,
				releaseEvaluator:     tt.fields.releaseEvaluator,
				onboardingClient:     tt.fields.onboardingClient,
			}
			got, err := c.GetFiToFiBottomSheet(tt.args.ctx, tt.args.actorId, tt.args.appVersion, tt.args.appPlatform, "")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFiToFiBottomSheet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFiToFiBottomSheet() got = %v, want %v", got, tt.want)
			}
		})
	}
}
