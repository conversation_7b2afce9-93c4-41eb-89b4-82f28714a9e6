//go:generate mockgen -source=fi_to_fi.go -destination=../../test/mocks/mock_fi_to_fi.go -package=mocks
package fi_to_fi

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	feCaFeaturesFiToFiPb "github.com/epifi/gamma/api/frontend/connected_account/features"
	"github.com/epifi/gamma/api/frontend/deeplink"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	caDlScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	feCaHelper "github.com/epifi/gamma/frontend/connected_account"
	fiToFiCaHelper "github.com/epifi/gamma/frontend/connected_account/fi_to_fi_helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
)

var ConnectedAccountFeatureFiToFiWireSet = wire.NewSet(NewConnectFiToFiSvc, wire.Bind(new(IConnectFiToFiSvc), new(*ConnectFiToFiSvc)))

type IConnectFiToFiSvc interface {
	GetFiToFiBottomSheet(ctx context.Context, actorId string, appVersion uint32,
		appPlatform commontypes.Platform, osAPIVersion string) (*feCaFeaturesFiToFiPb.InitiateFiToFiFlowBottomSheet, error)
}

type ConnectFiToFiSvc struct {
	beConnectedAccClient   beCaPb.ConnectedAccountClient
	savingsClient          savingsPb.SavingsClient
	conf                   *genconf.Config
	releaseEvaluator       release.IEvaluator
	onboardingClient       beOnbPb.OnboardingClient
	connectFiToFiHelperSvc fiToFiCaHelper.IConnectFiToFiHelperSvc
}

func NewConnectFiToFiSvc(beConnectedAccClient beCaPb.ConnectedAccountClient, savingsClient savingsPb.SavingsClient,
	conf *genconf.Config, releaseEvaluator release.IEvaluator, onboardingClient beOnbPb.OnboardingClient,
	connectFiToFiHelperSvc fiToFiCaHelper.IConnectFiToFiHelperSvc) *ConnectFiToFiSvc {
	return &ConnectFiToFiSvc{
		beConnectedAccClient:   beConnectedAccClient,
		savingsClient:          savingsClient,
		conf:                   conf,
		releaseEvaluator:       releaseEvaluator,
		onboardingClient:       onboardingClient,
		connectFiToFiHelperSvc: connectFiToFiHelperSvc,
	}
}

// GetFiToFiBottomSheet returns bottom sheet to initiate Fi to Fi flow in any entry point only if it is applicable for the actor,
// else returns nil as bottom sheet
func (c *ConnectFiToFiSvc) GetFiToFiBottomSheet(ctx context.Context, actorId string, appVersion uint32,
	appPlatform commontypes.Platform, osAPIVersion string) (*feCaFeaturesFiToFiPb.InitiateFiToFiFlowBottomSheet, error) {
	// Check if Fi to Fi flow is enabled for a user/ if Fi to Fi flow bottom sheet should appear or not.
	isFiToFiFlowEnabled, err := c.connectFiToFiHelperSvc.IsFiToFiFlowEnabled(ctx, actorId, appPlatform, appVersion, osAPIVersion)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprint("GetFiToFiBottomSheet: error while getting Fi To Fi bottom sheet,"+
			"isFiToFiFlowEnabled failure in checking", zap.String(logger.ACTOR_ID_V2, actorId)))
	}
	if isFiToFiFlowEnabled == false {
		return nil, nil
	}

	aaEntityForConnectResp, err := c.beConnectedAccClient.GetAaEntityForConnect(ctx, &beCaPb.GetAaEntityForConnectRequest{
		ActorId:     actorId,
		AppVersion:  appVersion,
		AppPlatform: appPlatform,
	})
	if rpcErr := epifigrpc.RPCError(aaEntityForConnectResp, err); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, fmt.Sprint("GetFiToFiBottomSheet: error while getting Fi To Fi bottom sheet,"+
			"GetAaEntityForConnect: error while determining aa entity", zap.String(logger.ACTOR_ID_V2, actorId)))
	}

	aaEntity := feCaHelper.ConvertToTypesAaEntity(aaEntityForConnectResp.GetAaEntity())
	if aaEntity == types.AaEntity_AA_ENTITY_UNSPECIFIED {
		return nil, errors.New(fmt.Sprint("GetFiToFiBottomSheet: error while getting Fi To Fi bottom sheet,"+
			"ConvertToTypesAaEntity: aaEntity can not be UNSPECIFIED type", zap.String(logger.ACTOR_ID_V2, actorId)))
	}

	dlScreenOptions, err := deeplinkv3.GetScreenOptionV2(&caDlScreenOptions.LandingPageFiToFiFlowScreenOptions{
		AaEntity: aaEntity,
	})
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprint("GetFiToFiBottomSheet: error while getting Fi To Fi bottom sheet,"+
			"LandingPageFiToFiFlowScreenOptions: Cta screen options can't be determined", zap.String(logger.ACTOR_ID_V2, actorId)))
	}

	return &feCaFeaturesFiToFiPb.InitiateFiToFiFlowBottomSheet{
		Title: commontypes.GetTextFromStringFontColourFontStyle(feCaHelper.FiToFiBottomSheetDisplayTitle,
			feCaHelper.FiToFiBottomSheetTitleFontColor,
			commontypes.FontStyle_HEADLINE_M,
		),
		Description: commontypes.GetTextFromStringFontColourFontStyle(feCaHelper.FiToFiBottomSheetDisplayDesc,
			feCaHelper.FiToFiBottomSheetDescFontColor,
			commontypes.FontStyle_BODY_S,
		),
		WealthAndAaTnc: &commontypes.Text{
			FontColor: feCaHelper.FiToFiBottomSheetWealthTncFontColor,
			DisplayValue: &commontypes.Text_Html{Html: feCaHelper.GetWealthAndAaTncDisplayText(aaEntity,
				c.conf.LegalDocuments().FiWealthTncUrl, c.conf.LegalDocuments().AaFinvuTncUrl,
				c.conf.LegalDocuments().AaOnemoneyTncUrl)},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_XS,
			},
		},
		AaEntity: aaEntity,
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CONTINUE,
			Text: feCaHelper.FiToFiBottomSheetCtaDisplayText,
			Deeplink: &deeplink.Deeplink{
				Screen:          deeplink.Screen_CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN,
				ScreenOptionsV2: dlScreenOptions,
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		SecureSpendsIcon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: feCaHelper.FiToFiBottomSheetIconURL, // TODO icon url
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  feCaHelper.FiToFiBottomSheetIconWidth,
						Height: feCaHelper.FiToFiBottomSheetIconHeight,
					},
				},
			},
		},
		MaxRetryAllowedFailureCase: c.conf.ConnectedAccount().MaxRetryAllowedForFiToFiFlowFailureCase(),
		InvalidateNumRetryDuration: durationpb.New(c.conf.ConnectedAccount().InvalidateConnectFiToFiFlowRetries()),
	}, nil
}
