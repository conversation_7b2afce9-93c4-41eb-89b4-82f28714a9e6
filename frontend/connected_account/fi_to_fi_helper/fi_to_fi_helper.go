package fi_to_fi_helper

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/feature/release"
)

var ConnectedAccountFeatureFiToFiHelperWireSet = wire.NewSet(NewConnectFiToFiHelperSvc, wire.Bind(new(IConnectFiToFiHelperSvc), new(*ConnectFiToFiHelperSvc)))

const iosDeviceOSVersionCheck = 17
const iosDeviceOS17MinAppVersionCheck = 481
const FederalFipMaxDepositAccounts = 50

// FiFedAccDiscoveryThresholdDuration signifies that atleast this much configured time is required for Fi Fed account to get discovered from AA API
const FiFedAccDiscoveryThresholdDuration = time.Hour * 24 * 7

type IConnectFiToFiHelperSvc interface {
	IsFiToFiFlowEnabled(ctx context.Context, actorId string, appPlatform commontypes.Platform,
		appVersion uint32, osAPIVersion string) (bool, error)
}

type ConnectFiToFiHelperSvc struct {
	beConnectedAccClient beCaPb.ConnectedAccountClient
	savingsClient        beSavingsPb.SavingsClient
	conf                 *genconf.Config
	releaseEvaluator     release.IEvaluator
	onboardingClient     beOnbPb.OnboardingClient
}

func NewConnectFiToFiHelperSvc(beConnectedAccClient beCaPb.ConnectedAccountClient, savingsClient beSavingsPb.SavingsClient,
	conf *genconf.Config, releaseEvaluator release.IEvaluator, onboardingClient beOnbPb.OnboardingClient) *ConnectFiToFiHelperSvc {
	return &ConnectFiToFiHelperSvc{
		beConnectedAccClient: beConnectedAccClient,
		savingsClient:        savingsClient,
		conf:                 conf,
		releaseEvaluator:     releaseEvaluator,
		onboardingClient:     onboardingClient,
	}
}

// IsFiToFiFlowEnabled is applicable/Fi to Fi flow bottom sheet will appear for an actor only if:
// - user has not connected Fi Federal saving bank account yet.
// - Federal FIP(FDRLFIPPROD) operations are not down.
// - If Federal operations are not generally down but still failing for the user then based on certain number of retries
// done in a specific period, fi to fi flow will be disabled for the user.
// if this method returns true then connect Fi To Fi bottom sheet will appear in entry points
// in case of error isFiToFiFlowEnabled value is returned as true with actual error.
func (c *ConnectFiToFiHelperSvc) IsFiToFiFlowEnabled(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion uint32, osAPIVersion string) (bool, error) {

	isFiToFiFlowEnabledForSpecificOsVersion, isFiToFiFlowEnabledForSpecificOsVersionErr := c.isFiToFiFlowEnabledForDeviceVersion(appPlatform, appVersion, osAPIVersion)
	if isFiToFiFlowEnabledForSpecificOsVersionErr != nil {
		logger.Error(ctx, "failed to check if fi to fi enabled for specific device OS version",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("os_version", osAPIVersion), zap.Any(logger.APP_PLATFORM, appPlatform),
			zap.Error(isFiToFiFlowEnabledForSpecificOsVersionErr))
	}

	if isFiToFiFlowEnabledForSpecificOsVersionErr == nil && !isFiToFiFlowEnabledForSpecificOsVersion {
		return false, nil
	}

	isConnectFiToFiFlowEnabledForUser, err := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(
		types.Feature_CA_CONNECT_FI_TO_FI).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("error checking if connect fi to fi feature is enabled for the actor : %v, err : %w", actorId, err)
	}
	if !isConnectFiToFiFlowEnabledForUser {
		logger.Debug(ctx, "CONNECT_FI_TO_FI feature is not enabled for user", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}
	isFilite, err := c.isFiliteUser(ctx, actorId)
	if err != nil {
		return false, fmt.Errorf("error checking if connect fi to fi feature is enabled for the Fi lite actor : %v, err : %w", actorId, err)
	}
	if isFilite {
		logger.Debug(ctx, "CONNECT_FI_TO_FI feature is not enabled for Fi Lite user", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	allFdrlConnectedAccList, err := c.beConnectedAccClient.GetAllAccounts(ctx, &beCaPb.GetAllAccountsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: FederalFipMaxDepositAccounts,
		},
		ActorId: actorId,
		AccountFilterList: []beCaExtPb.AccountFilter{beCaExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
			beCaExtPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL},
		FipIdList:             []string{caPkg.FederalFipId},
		AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
	})
	if rpcErr := epifigrpc.RPCError(allFdrlConnectedAccList, err); rpcErr != nil {
		if allFdrlConnectedAccList.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "No deposit type Federal bank account is connected for the user,"+
				" so CONNECT_FI_TO_FI is enabled", zap.String(logger.ACTOR_ID_V2, actorId))
			return true, nil
		}
		return false, errors.Wrap(rpcErr, fmt.Sprint("isFiToFiFlowEnabled: error while checking if Fi to Fi flow is enabled for actor", zap.String(logger.ACTOR_ID_V2, actorId)))
	}
	// Get savings account by actor id
	savingsAccResp, savingsAccErr := c.savingsClient.GetAccount(ctx, &beSavingsPb.GetAccountRequest{
		Identifier: &beSavingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	if savingsAccErr != nil || savingsAccResp == nil || savingsAccResp.GetAccount() == nil {
		return false, errors.Wrap(savingsAccErr, "isFiToFiFlowEnabled: error fetching savings account by actor id")
	}

	// This check ensures that after creation of savings account at Federal end, account should also get discovered from AA APIs.
	if savingsAccResp.GetAccount().GetCreatedAt().AsTime().Add(FiFedAccDiscoveryThresholdDuration).After(time.Now()) {
		logger.Debug(ctx, "Federal saving account is created within account discovery threshold duration",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("savings_acc_id", savingsAccResp.GetAccount().GetId()))
		return false, nil
	}
	savingsAccNum := savingsAccResp.GetAccount().GetAccountNo()
	for _, fdrlConnectedAcc := range allFdrlConnectedAccList.GetAccountDetailsList() {
		if fdrlConnectedAcc.GetIfscCode() == caPkg.FiFederalIfscCode ||
			fdrlConnectedAcc.GetMaskedAccountNumber()[len(fdrlConnectedAcc.GetMaskedAccountNumber())-4:] ==
				savingsAccNum[len(savingsAccNum)-4:] {
			logger.Debug(ctx, "Federal saving account is already connected, so CONNECT_FI_TO_FI is disabled for user",
				zap.String(logger.ACTOR_ID_V2, actorId))
			return false, nil
		}
	}
	logger.Debug(ctx, "User is completely eligible to connect Fi Federal saving bank account",
		zap.String(logger.ACTOR_ID_V2, actorId))
	return true, nil
}

func (c *ConnectFiToFiHelperSvc) isFiToFiFlowEnabledForDeviceVersion(appPlatform commontypes.Platform, appVersion uint32, osAPIVersion string) (bool, error) {
	osAPIVersion = strings.TrimSpace(osAPIVersion)
	// not blocking the flow in case of os version is not present in req header/not able to parse or in case of any other error
	if appPlatform == commontypes.Platform_IOS && len(osAPIVersion) != 0 {
		parts := strings.Split(osAPIVersion, ".")
		if len(parts) == 0 {
			return false, fmt.Errorf("can not parse os version to check, actor: %v", osAPIVersion)
		}

		osVersionStr := parts[0]
		osVersionVal, err := strconv.Atoi(osVersionStr)
		if err != nil {
			return false, fmt.Errorf("error converting version string to version val, actor: %v", osAPIVersion)
		}

		if osVersionVal >= iosDeviceOSVersionCheck {
			if appVersion >= uint32(iosDeviceOS17MinAppVersionCheck) {
				return true, nil
			}
			return false, nil
		}
	}
	return true, nil
}

func (c *ConnectFiToFiHelperSvc) isFiliteUser(ctx context.Context, actorId string) (bool, error) {
	isFilite, err := c.onboardingClient.GetFeatureDetails(ctx, &beOnbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(isFilite, err); err != nil {
		logger.Error(ctx, "error in getting fi-lite user details")
		return false, err
	}

	return isFilite.GetIsFiLiteUser(), nil
}
