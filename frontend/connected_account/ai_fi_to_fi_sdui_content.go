package connected_account

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2"
	caDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

// Fi to FI bottom sheet
const aaIconURL = "https://epifi-icons.pointz.in/networth/mcp/fi_ai.png"
const aaBottomSheetTitle = "Connect your Federal Bank a/c with AI and get smart insights"
const aaBottomSheetSubtitle = "Your Federal Bank transactions will appear in your net worth and can be used to share with AI & get insights"
const aaBottomSheetTnc1 = "I agree to terms & conditions of <span style= \"color:#00B899\"> epiFi Wealth </span> and"
const aaBottomSheetTnc2 = "to enable AI insights"
const aaButtonText = "Agree & Connect"

func aiFiToFIBottomSheetContent(aaEntity typesv2.AaEntity, epifiWealthURl, aaTnCURL string, platform commontypes.Platform) *sections.Section {
	bottomSheetComponents := make([]*components.Component, 0)

	//nolint: gocritic
	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetVisualElementFromUrlHeightAndWidth(aaIconURL, 120, 120).
					WithImageType(commontypes.ImageType_PNG)),
		})

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_XS,
				},
			),
		},
	)

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromStringFontColourFontStyleFontAlignment(aaBottomSheetTitle, "#313234", commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER),
			),
		},
	)

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_XS,
				},
			),
		},
	)

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromStringFontColourFontStyleFontAlignment(aaBottomSheetSubtitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS, commontypes.Text_ALIGNMENT_CENTER)),
		})

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_XXL,
				},
			),
		},
	)

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_S,
				},
			),
		},
	)

	var tnc1Deeplink *deeplinkPb.Deeplink
	if platform == commontypes.Platform_IOS {
		tnc1Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEB_PAGE,
			ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
				WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
					WebpageUrl: epifiWealthURl,
				},
			},
		}
	} else {
		tnc1Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
			ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
				ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
					ExternalUrl: epifiWealthURl,
				},
			},
		}
	}

	//nolint: gocritic
	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromHtmlStringFontColourFontStyle(aaBottomSheetTnc1, colors.ColorSlate, commontypes.FontStyle_BODY_XS)),
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								tnc1Deeplink,
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedAIFiToFiTnc",
						Properties: map[string]string{
							"url_type": "wealth",
						},
					},
				},
			},
		},
	)

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_XXS,
				},
			),
		},
	)

	//nolint: staticcheck
	spanString := ""
	//nolint: staticcheck
	if aaEntity == typesv2.AaEntity_AA_ENTITY_AA_FINVU {
		spanString = "<span style= \"color:#00B899\">Finvu's TnC </span>"
	} else if aaEntity == typesv2.AaEntity_AA_ENTITY_AA_ONE_MONEY {
		spanString = "<span style= \"color:#00B899\">OneMoney's TnC </span>"
	}

	var tnc2Deeplink *deeplinkPb.Deeplink
	if platform == commontypes.Platform_IOS {
		tnc2Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEB_PAGE,
			ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
				WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
					WebpageUrl: aaTnCURL,
				},
			},
		}
	} else {
		tnc2Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
			ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
				ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
					ExternalUrl: aaTnCURL,
				},
			},
		}
	}

	//nolint: gocritic
	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetTextFromHtmlStringFontColourFontStyle(spanString+aaBottomSheetTnc2, colors.ColorSlate, commontypes.FontStyle_BODY_XS)),
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: ui.GetAnyWithoutError(
								tnc2Deeplink,
							),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "ClickedAIFiToFiTnc",
						Properties: map[string]string{
							"url_type": "AA",
						},
					},
				},
			},
		},
	)

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				},
			),
		},
	)

	dlScreenOptions, _ := deeplinkv3.GetScreenOptionV2(&caDlOptions.LandingPageFiToFiFlowScreenOptions{
		AaEntity: aaEntity,
	})

	bottomSheetComponents = append(bottomSheetComponents,
		&components.Component{
			Content: ui.GetAnyWithoutError(
				&sections.VerticalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
									},
									BgColor: widget.GetBlockBackgroundColour(colors.ColorForest),
									Corner: &properties.CornerProperty{
										TopLeftCornerRadius:  40,
										TopRightCornerRadius: 40,
										BottomLeftCorner:     40,
										BottomRightCorner:    40,
									},
								},
							},
						},
					},
					Components: []*components.Component{
						&components.Component{
							Content: ui.GetAnyWithoutError(
								ui.NewITC().
									WithTexts(commontypes.
										GetTextFromStringFontColourFontStyleFontAlignment(aaButtonText, colors.ColorSnow, commontypes.FontStyle_BUTTON_M, commontypes.Text_ALIGNMENT_CENTER)).
									WithContainerPaddingSymmetrical(16, 12).
									WithContainerCornerRadius(16).
									WithContainerBackgroundColor(colors.ColorForest),
							),
						},
					},
					InteractionBehaviors: []*behaviors.InteractionBehavior{
						{
							Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
								OnClickBehavior: &behaviors.OnClickBehavior{
									Action: ui.GetAnyWithoutError(
										&deeplinkPb.Deeplink{
											Screen:          deeplinkPb.Screen_CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN,
											ScreenOptionsV2: dlScreenOptions,
										},
									),
								},
							},
							AnalyticsEvent: &analytics.AnalyticsEvent{
								EventName: "ClickedAIFiToFiContinue",
							},
						},
					},
				},
			),
		})

	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_Padding{
							Padding: &properties.PaddingProperty{
								Top:    8,
								Left:   16,
								Right:  16,
								Bottom: 28,
							},
						},
					},
				},
				Components: bottomSheetComponents,
				LoadBehavior: &behaviors.LifecycleBehavior{
					Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "LoadedAAFitoFiBottomSheet",
					},
				},
			},
		},
	}
}
