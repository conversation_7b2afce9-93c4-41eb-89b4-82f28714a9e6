---
description: To write unit test for a service method
globs: 
alwaysApply: false
---
This rule will be used to write unit tests for a service method. Unit tests should only test the core logic of the method while using mocks for the dependencies.

1. Structure -
    - Unit test files should be in the same directory as the service method being tested, with the same name suffixed with _test.go
    - Each package should contain a main_test.go file to initialize global configs and loggers. Skip this step if already present. Refer [main_test.go](mdc:nudge/main_test.go) for example.

2. Unit Tests Definition -
    - Test function names should follow this format: Test{ServiceName}_{MethodName}
    - Test cases should be defined as a slice of structs with the following format:
     ```go
     tests := []struct {
        name       string
        args       args
        setupMocks func(actorNudgeDaoMock *daoMock.MockActorNudgeDao)
        want       *nudgePb.NudgeDisplay
        wantErr    bool
     }{}
     ```
     where -
     1. name: Describes the functionality being tested.
     2. args: Struct for input arguments for the method under test.
     3. setupMocks: Function called before the method execution to set up mocks' input/output expectations.
     4. want: Expected output from the method.
     5. wantErr: Whether an error is expected (true or false).
    - Focus on meaningful tests, not just code coverage.
    - Ensure both happy and unhappy paths are tested.
    - Refer to TestService_GetChosenActorNudgeDisplay test in [service_test.go](mdc:nudge/service_test.go) for example.

3. Mocking -
    - Use Gomock (github.com/golang/mock/gomock) for mocking dependencies.
    - Only mock dependencies that are actually used in the method; set unused dependencies to nil.
    - Locate mock implementations by searching (grep) the codebase—mocks are usually found in /mocks directories.
    - If relevant mocks are not found, do the following
        - Use the following go:generate instruction template to enable mock generation in the file containing that dependency (referred to as FILENAME that you should substitute with the actual filename).
            - //go:generate mockgen -source=<FILENAME> -destination=./mocks/<FILENAME> -package=mocks 
        - Make sure that the "-package=mocks" in the above go:generate instruction is not modified to anything else.
        - Run go generate in the directory containing the dependency file
        - Do the above two steps for all the dependencies
    - When defining mock expectations, use the .EXPECT().Method().Return() pattern. Example: mockDao.EXPECT().FetchActorNudge(gomock.Any()).Return(expectedNudge, nil)
    - Wherever possible, assert the exact request being sent to the mock. It is acceptable to use `gomock.Any()` for the context parameter, but the request parameter should be compared for equality using `gomock.Eq()`. Example: `mockConsent.EXPECT().RecordConsents(gomock.Any(), gomock.Eq(expectedReq)).Return(nil, errors.New("rpc error"))`

4. Unit Tests Executor -
   - Loop through all test definitions and perform the following inside a `t.Run` block for each test case:
        - Initialize a new `gomock.Controller(t)`.
        - Create mock instances for all dependencies using the controller.
        - Construct the service object with the newly created mocks.
        - Set up mock expectations using the `setupMocks` function, passing the created mocks to it.
        - Execute the method with the test input.
        - Compare actual vs. expected output using either:
            - Vanilla comparators (`==`), or
            - `protocmp.Diff` for protobuf objects (preferred).
            - `reflect.DeepEqual` for other Go structs.

5. **Important:** Test Execution and Verification -
    - After writing the tests, immediately run them using `ENVIRONMENT=test go test .` within the package directory to confirm they pass and function as expected.
    - Iterate on fixing any compilation errors or test failures found during the test run.
